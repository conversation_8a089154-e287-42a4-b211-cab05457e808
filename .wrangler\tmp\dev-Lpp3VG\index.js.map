{"version": 3, "sources": ["../bundle-oi0dnv/checked-fetch.js", "../bundle-oi0dnv/strip-cf-connecting-ip-header.js", "../../../node_modules/hono/dist/compose.js", "../../../node_modules/hono/dist/request/constants.js", "../../../node_modules/hono/dist/utils/body.js", "../../../node_modules/hono/dist/utils/url.js", "../../../node_modules/hono/dist/request.js", "../../../node_modules/hono/dist/utils/html.js", "../../../node_modules/hono/dist/context.js", "../../../node_modules/hono/dist/router.js", "../../../node_modules/hono/dist/utils/constants.js", "../../../node_modules/hono/dist/hono-base.js", "../../../node_modules/hono/dist/router/reg-exp-router/node.js", "../../../node_modules/hono/dist/router/reg-exp-router/trie.js", "../../../node_modules/hono/dist/router/reg-exp-router/router.js", "../../../node_modules/hono/dist/router/smart-router/router.js", "../../../node_modules/hono/dist/router/trie-router/node.js", "../../../node_modules/hono/dist/router/trie-router/router.js", "../../../node_modules/hono/dist/hono.js", "../../../node_modules/hono/dist/middleware/cors/index.js", "../../../node_modules/hono/dist/utils/color.js", "../../../node_modules/hono/dist/middleware/logger/index.js", "../../../node_modules/hono/dist/middleware/pretty-json/index.js", "../../../node_modules/hono/dist/middleware/secure-headers/secure-headers.js", "../../../src/services/kv.ts", "../../../node_modules/nanoid/url-alphabet/index.js", "../../../node_modules/nanoid/index.browser.js", "../../../src/services/bookmarks.ts", "../../../src/services/categories.ts", "../../../src/services/migration.ts", "../../../src/services/chrome-bookmarks.ts", "../../../src/services/ai.ts", "../../../src/services/statistics.ts", "../../../src/routes/api.ts", "../../../src/components/HomePage.tsx", "../../../src/components/AdminPanel.tsx", "../../../src/components/Layout.tsx", "../../../src/routes/pages.ts", "../../../src/routes/admin.ts", "../../../src/index.ts", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-oi0dnv/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-oi0dnv/middleware-loader.entry.ts"], "sourceRoot": "E:\\Work\\cloudnav\\.wrangler\\tmp\\dev-Lpp3VG", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "function stripCfConnectingIPHeader(input, init) {\n\tconst request = new Request(input, init);\n\trequest.headers.delete(\"CF-Connecting-IP\");\n\treturn request;\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\treturn Reflect.apply(target, thisArg, [\n\t\t\tstripCfConnectingIPHeader.apply(null, argArray),\n\t\t]);\n\t},\n});\n", "// src/compose.ts\nvar compose = (middleware, onError, onNotFound) => {\n  return (context, next) => {\n    let index = -1;\n    return dispatch(0);\n    async function dispatch(i) {\n      if (i <= index) {\n        throw new Error(\"next() called multiple times\");\n      }\n      index = i;\n      let res;\n      let isError = false;\n      let handler;\n      if (middleware[i]) {\n        handler = middleware[i][0][0];\n        context.req.routeIndex = i;\n      } else {\n        handler = i === middleware.length && next || void 0;\n      }\n      if (handler) {\n        try {\n          res = await handler(context, () => dispatch(i + 1));\n        } catch (err) {\n          if (err instanceof Error && onError) {\n            context.error = err;\n            res = await onError(err, context);\n            isError = true;\n          } else {\n            throw err;\n          }\n        }\n      } else {\n        if (context.finalized === false && onNotFound) {\n          res = await onNotFound(context);\n        }\n      }\n      if (res && (context.finalized === false || isError)) {\n        context.res = res;\n      }\n      return context;\n    }\n  };\n};\nexport {\n  compose\n};\n", "// src/request/constants.ts\nvar GET_MATCH_RESULT = Symbol();\nexport {\n  GET_MATCH_RESULT\n};\n", "// src/utils/body.ts\nimport { HonoRequest } from \"../request.js\";\nvar parseBody = async (request, options = /* @__PURE__ */ Object.create(null)) => {\n  const { all = false, dot = false } = options;\n  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;\n  const contentType = headers.get(\"Content-Type\");\n  if (contentType?.startsWith(\"multipart/form-data\") || contentType?.startsWith(\"application/x-www-form-urlencoded\")) {\n    return parseFormData(request, { all, dot });\n  }\n  return {};\n};\nasync function parseFormData(request, options) {\n  const formData = await request.formData();\n  if (formData) {\n    return convertFormDataToBodyData(formData, options);\n  }\n  return {};\n}\nfunction convertFormDataToBodyData(formData, options) {\n  const form = /* @__PURE__ */ Object.create(null);\n  formData.forEach((value, key) => {\n    const shouldParseAllValues = options.all || key.endsWith(\"[]\");\n    if (!shouldParseAllValues) {\n      form[key] = value;\n    } else {\n      handleParsingAllValues(form, key, value);\n    }\n  });\n  if (options.dot) {\n    Object.entries(form).forEach(([key, value]) => {\n      const shouldParseDotValues = key.includes(\".\");\n      if (shouldParseDotValues) {\n        handleParsingNestedValues(form, key, value);\n        delete form[key];\n      }\n    });\n  }\n  return form;\n}\nvar handleParsingAllValues = (form, key, value) => {\n  if (form[key] !== void 0) {\n    if (Array.isArray(form[key])) {\n      ;\n      form[key].push(value);\n    } else {\n      form[key] = [form[key], value];\n    }\n  } else {\n    if (!key.endsWith(\"[]\")) {\n      form[key] = value;\n    } else {\n      form[key] = [value];\n    }\n  }\n};\nvar handleParsingNestedValues = (form, key, value) => {\n  let nestedForm = form;\n  const keys = key.split(\".\");\n  keys.forEach((key2, index) => {\n    if (index === keys.length - 1) {\n      nestedForm[key2] = value;\n    } else {\n      if (!nestedForm[key2] || typeof nestedForm[key2] !== \"object\" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {\n        nestedForm[key2] = /* @__PURE__ */ Object.create(null);\n      }\n      nestedForm = nestedForm[key2];\n    }\n  });\n};\nexport {\n  parseBody\n};\n", "// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label, next) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    const cacheKey = `${label}#${next}`;\n    if (!patternCache[cacheKey]) {\n      if (match[2]) {\n        patternCache[cacheKey] = next && next[0] !== \":\" && next[0] !== \"*\" ? [cacheKey, match[1], new RegExp(`^${match[2]}(?=/${next})`)] : [label, match[1], new RegExp(`^${match[2]}$`)];\n      } else {\n        patternCache[cacheKey] = [label, match[1], true];\n      }\n    }\n    return patternCache[cacheKey];\n  }\n  return null;\n};\nvar tryDecode = (str, decoder) => {\n  try {\n    return decoder(str);\n  } catch {\n    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {\n      try {\n        return decoder(match);\n      } catch {\n        return match;\n      }\n    });\n  }\n};\nvar tryDecodeURI = (str) => tryDecode(str, decodeURI);\nvar getPath = (request) => {\n  const url = request.url;\n  const start = url.indexOf(\n    \"/\",\n    url.charCodeAt(9) === 58 ? 13 : 8\n  );\n  let i = start;\n  for (; i < url.length; i++) {\n    const charCode = url.charCodeAt(i);\n    if (charCode === 37) {\n      const queryIndex = url.indexOf(\"?\", i);\n      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);\n      return tryDecodeURI(path.includes(\"%25\") ? path.replace(/%25/g, \"%2525\") : path);\n    } else if (charCode === 63) {\n      break;\n    }\n  }\n  return url.slice(start, i);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result.at(-1) === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (base, sub, ...rest) => {\n  if (rest.length) {\n    sub = mergePath(sub, ...rest);\n  }\n  return `${base?.[0] === \"/\" ? \"\" : \"/\"}${base}${sub === \"/\" ? \"\" : `${base?.at(-1) === \"/\" ? \"\" : \"/\"}${sub?.[0] === \"/\" ? sub.slice(1) : sub}`}`;\n};\nvar checkOptionalParameter = (path) => {\n  if (path.charCodeAt(path.length - 1) !== 63 || !path.includes(\":\")) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return value.indexOf(\"%\") !== -1 ? tryDecode(value, decodeURIComponent_) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\nexport {\n  checkOptionalParameter,\n  decodeURIComponent_,\n  getPath,\n  getPathNoStrict,\n  getPattern,\n  getQueryParam,\n  getQueryParams,\n  getQueryStrings,\n  mergePath,\n  splitPath,\n  splitRoutingPath,\n  tryDecode\n};\n", "// src/request.ts\nimport { GET_MATCH_RESULT } from \"./request/constants.js\";\nimport { parseBody } from \"./utils/body.js\";\nimport { decodeURIComponent_, getQueryParam, getQueryParams, tryDecode } from \"./utils/url.js\";\nvar tryDecodeURIComponent = (str) => tryDecode(str, decodeURIComponent_);\nvar HonoRequest = class {\n  raw;\n  #validatedData;\n  #matchResult;\n  routeIndex = 0;\n  path;\n  bodyCache = {};\n  constructor(request, path = \"/\", matchResult = [[]]) {\n    this.raw = request;\n    this.path = path;\n    this.#matchResult = matchResult;\n    this.#validatedData = {};\n  }\n  param(key) {\n    return key ? this.#getDecodedParam(key) : this.#getAllDecodedParams();\n  }\n  #getDecodedParam(key) {\n    const paramKey = this.#matchResult[0][this.routeIndex][1][key];\n    const param = this.#getParamValue(paramKey);\n    return param ? /\\%/.test(param) ? tryDecodeURIComponent(param) : param : void 0;\n  }\n  #getAllDecodedParams() {\n    const decoded = {};\n    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);\n    for (const key of keys) {\n      const value = this.#getParamValue(this.#matchResult[0][this.routeIndex][1][key]);\n      if (value && typeof value === \"string\") {\n        decoded[key] = /\\%/.test(value) ? tryDecodeURIComponent(value) : value;\n      }\n    }\n    return decoded;\n  }\n  #getParamValue(paramKey) {\n    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;\n  }\n  query(key) {\n    return getQueryParam(this.url, key);\n  }\n  queries(key) {\n    return getQueryParams(this.url, key);\n  }\n  header(name) {\n    if (name) {\n      return this.raw.headers.get(name) ?? void 0;\n    }\n    const headerData = {};\n    this.raw.headers.forEach((value, key) => {\n      headerData[key] = value;\n    });\n    return headerData;\n  }\n  async parseBody(options) {\n    return this.bodyCache.parsedBody ??= await parseBody(this, options);\n  }\n  #cachedBody = (key) => {\n    const { bodyCache, raw } = this;\n    const cachedBody = bodyCache[key];\n    if (cachedBody) {\n      return cachedBody;\n    }\n    const anyCachedKey = Object.keys(bodyCache)[0];\n    if (anyCachedKey) {\n      return bodyCache[anyCachedKey].then((body) => {\n        if (anyCachedKey === \"json\") {\n          body = JSON.stringify(body);\n        }\n        return new Response(body)[key]();\n      });\n    }\n    return bodyCache[key] = raw[key]();\n  };\n  json() {\n    return this.#cachedBody(\"json\");\n  }\n  text() {\n    return this.#cachedBody(\"text\");\n  }\n  arrayBuffer() {\n    return this.#cachedBody(\"arrayBuffer\");\n  }\n  blob() {\n    return this.#cachedBody(\"blob\");\n  }\n  formData() {\n    return this.#cachedBody(\"formData\");\n  }\n  addValidatedData(target, data) {\n    this.#validatedData[target] = data;\n  }\n  valid(target) {\n    return this.#validatedData[target];\n  }\n  get url() {\n    return this.raw.url;\n  }\n  get method() {\n    return this.raw.method;\n  }\n  get [GET_MATCH_RESULT]() {\n    return this.#matchResult;\n  }\n  get matchedRoutes() {\n    return this.#matchResult[0].map(([[, route]]) => route);\n  }\n  get routePath() {\n    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;\n  }\n};\nexport {\n  HonoRequest\n};\n", "// src/utils/html.ts\nvar HtmlEscapedCallbackPhase = {\n  Stringify: 1,\n  BeforeStream: 2,\n  Stream: 3\n};\nvar raw = (value, callbacks) => {\n  const escapedString = new String(value);\n  escapedString.isEscaped = true;\n  escapedString.callbacks = callbacks;\n  return escapedString;\n};\nvar escapeRe = /[&<>'\"]/;\nvar stringBufferToString = async (buffer, callbacks) => {\n  let str = \"\";\n  callbacks ||= [];\n  const resolvedBuffer = await Promise.all(buffer);\n  for (let i = resolvedBuffer.length - 1; ; i--) {\n    str += resolvedBuffer[i];\n    i--;\n    if (i < 0) {\n      break;\n    }\n    let r = resolvedBuffer[i];\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    const isEscaped = r.isEscaped;\n    r = await (typeof r === \"object\" ? r.toString() : r);\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    if (r.isEscaped ?? isEscaped) {\n      str += r;\n    } else {\n      const buf = [str];\n      escapeToBuffer(r, buf);\n      str = buf[0];\n    }\n  }\n  return raw(str, callbacks);\n};\nvar escapeToBuffer = (str, buffer) => {\n  const match = str.search(escapeRe);\n  if (match === -1) {\n    buffer[0] += str;\n    return;\n  }\n  let escape;\n  let index;\n  let lastIndex = 0;\n  for (index = match; index < str.length; index++) {\n    switch (str.charCodeAt(index)) {\n      case 34:\n        escape = \"&quot;\";\n        break;\n      case 39:\n        escape = \"&#39;\";\n        break;\n      case 38:\n        escape = \"&amp;\";\n        break;\n      case 60:\n        escape = \"&lt;\";\n        break;\n      case 62:\n        escape = \"&gt;\";\n        break;\n      default:\n        continue;\n    }\n    buffer[0] += str.substring(lastIndex, index) + escape;\n    lastIndex = index + 1;\n  }\n  buffer[0] += str.substring(lastIndex, index);\n};\nvar resolveCallbackSync = (str) => {\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return str;\n  }\n  const buffer = [str];\n  const context = {};\n  callbacks.forEach((c) => c({ phase: HtmlEscapedCallbackPhase.Stringify, buffer, context }));\n  return buffer[0];\n};\nvar resolveCallback = async (str, phase, preserveCallbacks, context, buffer) => {\n  if (typeof str === \"object\" && !(str instanceof String)) {\n    if (!(str instanceof Promise)) {\n      str = str.toString();\n    }\n    if (str instanceof Promise) {\n      str = await str;\n    }\n  }\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return Promise.resolve(str);\n  }\n  if (buffer) {\n    buffer[0] += str;\n  } else {\n    buffer = [str];\n  }\n  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(\n    (res) => Promise.all(\n      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))\n    ).then(() => buffer[0])\n  );\n  if (preserveCallbacks) {\n    return raw(await resStr, callbacks);\n  } else {\n    return resStr;\n  }\n};\nexport {\n  HtmlEscapedCallbackPhase,\n  escapeToBuffer,\n  raw,\n  resolveCallback,\n  resolveCallbackSync,\n  stringBufferToString\n};\n", "// src/context.ts\nimport { HonoRequest } from \"./request.js\";\nimport { HtmlEscapedCallbackPhase, resolveCallback } from \"./utils/html.js\";\nvar TEXT_PLAIN = \"text/plain; charset=UTF-8\";\nvar setDefaultContentType = (contentType, headers) => {\n  return {\n    \"Content-Type\": contentType,\n    ...headers\n  };\n};\nvar Context = class {\n  #rawRequest;\n  #req;\n  env = {};\n  #var;\n  finalized = false;\n  error;\n  #status;\n  #executionCtx;\n  #res;\n  #layout;\n  #renderer;\n  #notFoundHandler;\n  #preparedHeaders;\n  #matchResult;\n  #path;\n  constructor(req, options) {\n    this.#rawRequest = req;\n    if (options) {\n      this.#executionCtx = options.executionCtx;\n      this.env = options.env;\n      this.#notFoundHandler = options.notFoundHandler;\n      this.#path = options.path;\n      this.#matchResult = options.matchResult;\n    }\n  }\n  get req() {\n    this.#req ??= new HonoRequest(this.#rawRequest, this.#path, this.#matchResult);\n    return this.#req;\n  }\n  get event() {\n    if (this.#executionCtx && \"respondWith\" in this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no FetchEvent\");\n    }\n  }\n  get executionCtx() {\n    if (this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no ExecutionContext\");\n    }\n  }\n  get res() {\n    return this.#res ||= new Response(null, {\n      headers: this.#preparedHeaders ??= new Headers()\n    });\n  }\n  set res(_res) {\n    if (this.#res && _res) {\n      _res = new Response(_res.body, _res);\n      for (const [k, v] of this.#res.headers.entries()) {\n        if (k === \"content-type\") {\n          continue;\n        }\n        if (k === \"set-cookie\") {\n          const cookies = this.#res.headers.getSetCookie();\n          _res.headers.delete(\"set-cookie\");\n          for (const cookie of cookies) {\n            _res.headers.append(\"set-cookie\", cookie);\n          }\n        } else {\n          _res.headers.set(k, v);\n        }\n      }\n    }\n    this.#res = _res;\n    this.finalized = true;\n  }\n  render = (...args) => {\n    this.#renderer ??= (content) => this.html(content);\n    return this.#renderer(...args);\n  };\n  setLayout = (layout) => this.#layout = layout;\n  getLayout = () => this.#layout;\n  setRenderer = (renderer) => {\n    this.#renderer = renderer;\n  };\n  header = (name, value, options) => {\n    if (this.finalized) {\n      this.#res = new Response(this.#res.body, this.#res);\n    }\n    const headers = this.#res ? this.#res.headers : this.#preparedHeaders ??= new Headers();\n    if (value === void 0) {\n      headers.delete(name);\n    } else if (options?.append) {\n      headers.append(name, value);\n    } else {\n      headers.set(name, value);\n    }\n  };\n  status = (status) => {\n    this.#status = status;\n  };\n  set = (key, value) => {\n    this.#var ??= /* @__PURE__ */ new Map();\n    this.#var.set(key, value);\n  };\n  get = (key) => {\n    return this.#var ? this.#var.get(key) : void 0;\n  };\n  get var() {\n    if (!this.#var) {\n      return {};\n    }\n    return Object.fromEntries(this.#var);\n  }\n  #newResponse(data, arg, headers) {\n    const responseHeaders = this.#res ? new Headers(this.#res.headers) : this.#preparedHeaders ?? new Headers();\n    if (typeof arg === \"object\" && \"headers\" in arg) {\n      const argHeaders = arg.headers instanceof Headers ? arg.headers : new Headers(arg.headers);\n      for (const [key, value] of argHeaders) {\n        if (key.toLowerCase() === \"set-cookie\") {\n          responseHeaders.append(key, value);\n        } else {\n          responseHeaders.set(key, value);\n        }\n      }\n    }\n    if (headers) {\n      for (const [k, v] of Object.entries(headers)) {\n        if (typeof v === \"string\") {\n          responseHeaders.set(k, v);\n        } else {\n          responseHeaders.delete(k);\n          for (const v2 of v) {\n            responseHeaders.append(k, v2);\n          }\n        }\n      }\n    }\n    const status = typeof arg === \"number\" ? arg : arg?.status ?? this.#status;\n    return new Response(data, { status, headers: responseHeaders });\n  }\n  newResponse = (...args) => this.#newResponse(...args);\n  body = (data, arg, headers) => this.#newResponse(data, arg, headers);\n  text = (text, arg, headers) => {\n    return !this.#preparedHeaders && !this.#status && !arg && !headers && !this.finalized ? new Response(text) : this.#newResponse(\n      text,\n      arg,\n      setDefaultContentType(TEXT_PLAIN, headers)\n    );\n  };\n  json = (object, arg, headers) => {\n    return this.#newResponse(\n      JSON.stringify(object),\n      arg,\n      setDefaultContentType(\"application/json\", headers)\n    );\n  };\n  html = (html, arg, headers) => {\n    const res = (html2) => this.#newResponse(html2, arg, setDefaultContentType(\"text/html; charset=UTF-8\", headers));\n    return typeof html === \"object\" ? resolveCallback(html, HtmlEscapedCallbackPhase.Stringify, false, {}).then(res) : res(html);\n  };\n  redirect = (location, status) => {\n    this.header(\"Location\", String(location));\n    return this.newResponse(null, status ?? 302);\n  };\n  notFound = () => {\n    this.#notFoundHandler ??= () => new Response();\n    return this.#notFoundHandler(this);\n  };\n};\nexport {\n  Context,\n  TEXT_PLAIN\n};\n", "// src/router.ts\nvar METHOD_NAME_ALL = \"ALL\";\nvar METHOD_NAME_ALL_LOWERCASE = \"all\";\nvar METHODS = [\"get\", \"post\", \"put\", \"delete\", \"options\", \"patch\"];\nvar MESSAGE_MATCHER_IS_ALREADY_BUILT = \"Can not add a route since the matcher is already built.\";\nvar UnsupportedPathError = class extends Error {\n};\nexport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHODS,\n  METHOD_NAME_ALL,\n  METHOD_NAME_ALL_LOWERCASE,\n  UnsupportedPathError\n};\n", "// src/utils/constants.ts\nvar COMPOSED_HANDLER = \"__COMPOSED_HANDLER\";\nexport {\n  COMPOSED_HANDLER\n};\n", "// src/hono-base.ts\nimport { compose } from \"./compose.js\";\nimport { Context } from \"./context.js\";\nimport { METHODS, METHOD_NAME_ALL, METHOD_NAME_ALL_LOWERCASE } from \"./router.js\";\nimport { COMPOSED_HANDLER } from \"./utils/constants.js\";\nimport { getPath, getPathNoStrict, mergePath } from \"./utils/url.js\";\nvar notFoundHandler = (c) => {\n  return c.text(\"404 Not Found\", 404);\n};\nvar errorHandler = (err, c) => {\n  if (\"getResponse\" in err) {\n    const res = err.getResponse();\n    return c.newResponse(res.body, res);\n  }\n  console.error(err);\n  return c.text(\"Internal Server Error\", 500);\n};\nvar Hono = class {\n  get;\n  post;\n  put;\n  delete;\n  options;\n  patch;\n  all;\n  on;\n  use;\n  router;\n  getPath;\n  _basePath = \"/\";\n  #path = \"/\";\n  routes = [];\n  constructor(options = {}) {\n    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];\n    allMethods.forEach((method) => {\n      this[method] = (args1, ...args) => {\n        if (typeof args1 === \"string\") {\n          this.#path = args1;\n        } else {\n          this.#addRoute(method, this.#path, args1);\n        }\n        args.forEach((handler) => {\n          this.#addRoute(method, this.#path, handler);\n        });\n        return this;\n      };\n    });\n    this.on = (method, path, ...handlers) => {\n      for (const p of [path].flat()) {\n        this.#path = p;\n        for (const m of [method].flat()) {\n          handlers.map((handler) => {\n            this.#addRoute(m.toUpperCase(), this.#path, handler);\n          });\n        }\n      }\n      return this;\n    };\n    this.use = (arg1, ...handlers) => {\n      if (typeof arg1 === \"string\") {\n        this.#path = arg1;\n      } else {\n        this.#path = \"*\";\n        handlers.unshift(arg1);\n      }\n      handlers.forEach((handler) => {\n        this.#addRoute(METHOD_NAME_ALL, this.#path, handler);\n      });\n      return this;\n    };\n    const { strict, ...optionsWithoutStrict } = options;\n    Object.assign(this, optionsWithoutStrict);\n    this.getPath = strict ?? true ? options.getPath ?? getPath : getPathNoStrict;\n  }\n  #clone() {\n    const clone = new Hono({\n      router: this.router,\n      getPath: this.getPath\n    });\n    clone.errorHandler = this.errorHandler;\n    clone.#notFoundHandler = this.#notFoundHandler;\n    clone.routes = this.routes;\n    return clone;\n  }\n  #notFoundHandler = notFoundHandler;\n  errorHandler = errorHandler;\n  route(path, app) {\n    const subApp = this.basePath(path);\n    app.routes.map((r) => {\n      let handler;\n      if (app.errorHandler === errorHandler) {\n        handler = r.handler;\n      } else {\n        handler = async (c, next) => (await compose([], app.errorHandler)(c, () => r.handler(c, next))).res;\n        handler[COMPOSED_HANDLER] = r.handler;\n      }\n      subApp.#addRoute(r.method, r.path, handler);\n    });\n    return this;\n  }\n  basePath(path) {\n    const subApp = this.#clone();\n    subApp._basePath = mergePath(this._basePath, path);\n    return subApp;\n  }\n  onError = (handler) => {\n    this.errorHandler = handler;\n    return this;\n  };\n  notFound = (handler) => {\n    this.#notFoundHandler = handler;\n    return this;\n  };\n  mount(path, applicationHandler, options) {\n    let replaceRequest;\n    let optionHandler;\n    if (options) {\n      if (typeof options === \"function\") {\n        optionHandler = options;\n      } else {\n        optionHandler = options.optionHandler;\n        if (options.replaceRequest === false) {\n          replaceRequest = (request) => request;\n        } else {\n          replaceRequest = options.replaceRequest;\n        }\n      }\n    }\n    const getOptions = optionHandler ? (c) => {\n      const options2 = optionHandler(c);\n      return Array.isArray(options2) ? options2 : [options2];\n    } : (c) => {\n      let executionContext = void 0;\n      try {\n        executionContext = c.executionCtx;\n      } catch {\n      }\n      return [c.env, executionContext];\n    };\n    replaceRequest ||= (() => {\n      const mergedPath = mergePath(this._basePath, path);\n      const pathPrefixLength = mergedPath === \"/\" ? 0 : mergedPath.length;\n      return (request) => {\n        const url = new URL(request.url);\n        url.pathname = url.pathname.slice(pathPrefixLength) || \"/\";\n        return new Request(url, request);\n      };\n    })();\n    const handler = async (c, next) => {\n      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));\n      if (res) {\n        return res;\n      }\n      await next();\n    };\n    this.#addRoute(METHOD_NAME_ALL, mergePath(path, \"*\"), handler);\n    return this;\n  }\n  #addRoute(method, path, handler) {\n    method = method.toUpperCase();\n    path = mergePath(this._basePath, path);\n    const r = { basePath: this._basePath, path, method, handler };\n    this.router.add(method, path, [handler, r]);\n    this.routes.push(r);\n  }\n  #handleError(err, c) {\n    if (err instanceof Error) {\n      return this.errorHandler(err, c);\n    }\n    throw err;\n  }\n  #dispatch(request, executionCtx, env, method) {\n    if (method === \"HEAD\") {\n      return (async () => new Response(null, await this.#dispatch(request, executionCtx, env, \"GET\")))();\n    }\n    const path = this.getPath(request, { env });\n    const matchResult = this.router.match(method, path);\n    const c = new Context(request, {\n      path,\n      matchResult,\n      env,\n      executionCtx,\n      notFoundHandler: this.#notFoundHandler\n    });\n    if (matchResult[0].length === 1) {\n      let res;\n      try {\n        res = matchResult[0][0][0][0](c, async () => {\n          c.res = await this.#notFoundHandler(c);\n        });\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n      return res instanceof Promise ? res.then(\n        (resolved) => resolved || (c.finalized ? c.res : this.#notFoundHandler(c))\n      ).catch((err) => this.#handleError(err, c)) : res ?? this.#notFoundHandler(c);\n    }\n    const composed = compose(matchResult[0], this.errorHandler, this.#notFoundHandler);\n    return (async () => {\n      try {\n        const context = await composed(c);\n        if (!context.finalized) {\n          throw new Error(\n            \"Context is not finalized. Did you forget to return a Response object or `await next()`?\"\n          );\n        }\n        return context.res;\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n    })();\n  }\n  fetch = (request, ...rest) => {\n    return this.#dispatch(request, rest[1], rest[0], request.method);\n  };\n  request = (input, requestInit, Env, executionCtx) => {\n    if (input instanceof Request) {\n      return this.fetch(requestInit ? new Request(input, requestInit) : input, Env, executionCtx);\n    }\n    input = input.toString();\n    return this.fetch(\n      new Request(\n        /^https?:\\/\\//.test(input) ? input : `http://localhost${mergePath(\"/\", input)}`,\n        requestInit\n      ),\n      Env,\n      executionCtx\n    );\n  };\n  fire = () => {\n    addEventListener(\"fetch\", (event) => {\n      event.respondWith(this.#dispatch(event.request, event, void 0, event.request.method));\n    });\n  };\n};\nexport {\n  Hono as HonoBase\n};\n", "// src/router/reg-exp-router/node.ts\nvar LABEL_REG_EXP_STR = \"[^/]+\";\nvar ONLY_WILDCARD_REG_EXP_STR = \".*\";\nvar TAIL_WILDCARD_REG_EXP_STR = \"(?:|/.*)\";\nvar PATH_ERROR = Symbol();\nvar regExpMetaChars = new Set(\".\\\\+*[^]$()\");\nfunction compareKey(a, b) {\n  if (a.length === 1) {\n    return b.length === 1 ? a < b ? -1 : 1 : -1;\n  }\n  if (b.length === 1) {\n    return 1;\n  }\n  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {\n    return 1;\n  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {\n    return -1;\n  }\n  if (a === LABEL_REG_EXP_STR) {\n    return 1;\n  } else if (b === LABEL_REG_EXP_STR) {\n    return -1;\n  }\n  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;\n}\nvar Node = class {\n  #index;\n  #varIndex;\n  #children = /* @__PURE__ */ Object.create(null);\n  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {\n    if (tokens.length === 0) {\n      if (this.#index !== void 0) {\n        throw PATH_ERROR;\n      }\n      if (pathErrorCheckOnly) {\n        return;\n      }\n      this.#index = index;\n      return;\n    }\n    const [token, ...restTokens] = tokens;\n    const pattern = token === \"*\" ? restTokens.length === 0 ? [\"\", \"\", ONLY_WILDCARD_REG_EXP_STR] : [\"\", \"\", LABEL_REG_EXP_STR] : token === \"/*\" ? [\"\", \"\", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n    let node;\n    if (pattern) {\n      const name = pattern[1];\n      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;\n      if (name && pattern[2]) {\n        regexpStr = regexpStr.replace(/^\\((?!\\?:)(?=[^)]+\\)$)/, \"(?:\");\n        if (/\\((?!\\?:)/.test(regexpStr)) {\n          throw PATH_ERROR;\n        }\n      }\n      node = this.#children[regexpStr];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[regexpStr] = new Node();\n        if (name !== \"\") {\n          node.#varIndex = context.varIndex++;\n        }\n      }\n      if (!pathErrorCheckOnly && name !== \"\") {\n        paramMap.push([name, node.#varIndex]);\n      }\n    } else {\n      node = this.#children[token];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[token] = new Node();\n      }\n    }\n    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);\n  }\n  buildRegExpStr() {\n    const childKeys = Object.keys(this.#children).sort(compareKey);\n    const strList = childKeys.map((k) => {\n      const c = this.#children[k];\n      return (typeof c.#varIndex === \"number\" ? `(${k})@${c.#varIndex}` : regExpMetaChars.has(k) ? `\\\\${k}` : k) + c.buildRegExpStr();\n    });\n    if (typeof this.#index === \"number\") {\n      strList.unshift(`#${this.#index}`);\n    }\n    if (strList.length === 0) {\n      return \"\";\n    }\n    if (strList.length === 1) {\n      return strList[0];\n    }\n    return \"(?:\" + strList.join(\"|\") + \")\";\n  }\n};\nexport {\n  Node,\n  PATH_ERROR\n};\n", "// src/router/reg-exp-router/trie.ts\nimport { Node } from \"./node.js\";\nvar Trie = class {\n  #context = { varIndex: 0 };\n  #root = new Node();\n  insert(path, index, pathErrorCheckOnly) {\n    const paramAssoc = [];\n    const groups = [];\n    for (let i = 0; ; ) {\n      let replaced = false;\n      path = path.replace(/\\{[^}]+\\}/g, (m) => {\n        const mark = `@\\\\${i}`;\n        groups[i] = [mark, m];\n        i++;\n        replaced = true;\n        return mark;\n      });\n      if (!replaced) {\n        break;\n      }\n    }\n    const tokens = path.match(/(?::[^\\/]+)|(?:\\/\\*$)|./g) || [];\n    for (let i = groups.length - 1; i >= 0; i--) {\n      const [mark] = groups[i];\n      for (let j = tokens.length - 1; j >= 0; j--) {\n        if (tokens[j].indexOf(mark) !== -1) {\n          tokens[j] = tokens[j].replace(mark, groups[i][1]);\n          break;\n        }\n      }\n    }\n    this.#root.insert(tokens, index, paramAssoc, this.#context, pathErrorCheckOnly);\n    return paramAssoc;\n  }\n  buildRegExp() {\n    let regexp = this.#root.buildRegExpStr();\n    if (regexp === \"\") {\n      return [/^$/, [], []];\n    }\n    let captureIndex = 0;\n    const indexReplacementMap = [];\n    const paramReplacementMap = [];\n    regexp = regexp.replace(/#(\\d+)|@(\\d+)|\\.\\*\\$/g, (_, handlerIndex, paramIndex) => {\n      if (handlerIndex !== void 0) {\n        indexReplacementMap[++captureIndex] = Number(handlerIndex);\n        return \"$()\";\n      }\n      if (paramIndex !== void 0) {\n        paramReplacementMap[Number(paramIndex)] = ++captureIndex;\n        return \"\";\n      }\n      return \"\";\n    });\n    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];\n  }\n};\nexport {\n  Trie\n};\n", "// src/router/reg-exp-router/router.ts\nimport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHOD_NAME_ALL,\n  UnsupportedPathError\n} from \"../../router.js\";\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { PATH_ERROR } from \"./node.js\";\nimport { Trie } from \"./trie.js\";\nvar emptyParam = [];\nvar nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];\nvar wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\nfunction buildWildcardRegExp(path) {\n  return wildcardRegExpCache[path] ??= new RegExp(\n    path === \"*\" ? \"\" : `^${path.replace(\n      /\\/\\*$|([.\\\\+*[^\\]$()])/g,\n      (_, metaChar) => metaChar ? `\\\\${metaChar}` : \"(?:|/.*)\"\n    )}$`\n  );\n}\nfunction clearWildcardRegExpCache() {\n  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\n}\nfunction buildMatcherFromPreprocessedRoutes(routes) {\n  const trie = new Trie();\n  const handlerData = [];\n  if (routes.length === 0) {\n    return nullMatcher;\n  }\n  const routesWithStaticPathFlag = routes.map(\n    (route) => [!/\\*|\\/:/.test(route[0]), ...route]\n  ).sort(\n    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length\n  );\n  const staticMap = /* @__PURE__ */ Object.create(null);\n  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {\n    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];\n    if (pathErrorCheckOnly) {\n      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];\n    } else {\n      j++;\n    }\n    let paramAssoc;\n    try {\n      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);\n    } catch (e) {\n      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;\n    }\n    if (pathErrorCheckOnly) {\n      continue;\n    }\n    handlerData[j] = handlers.map(([h, paramCount]) => {\n      const paramIndexMap = /* @__PURE__ */ Object.create(null);\n      paramCount -= 1;\n      for (; paramCount >= 0; paramCount--) {\n        const [key, value] = paramAssoc[paramCount];\n        paramIndexMap[key] = value;\n      }\n      return [h, paramIndexMap];\n    });\n  }\n  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();\n  for (let i = 0, len = handlerData.length; i < len; i++) {\n    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {\n      const map = handlerData[i][j]?.[1];\n      if (!map) {\n        continue;\n      }\n      const keys = Object.keys(map);\n      for (let k = 0, len3 = keys.length; k < len3; k++) {\n        map[keys[k]] = paramReplacementMap[map[keys[k]]];\n      }\n    }\n  }\n  const handlerMap = [];\n  for (const i in indexReplacementMap) {\n    handlerMap[i] = handlerData[indexReplacementMap[i]];\n  }\n  return [regexp, handlerMap, staticMap];\n}\nfunction findMiddleware(middleware, path) {\n  if (!middleware) {\n    return void 0;\n  }\n  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {\n    if (buildWildcardRegExp(k).test(path)) {\n      return [...middleware[k]];\n    }\n  }\n  return void 0;\n}\nvar RegExpRouter = class {\n  name = \"RegExpRouter\";\n  #middleware;\n  #routes;\n  constructor() {\n    this.#middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n    this.#routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n  }\n  add(method, path, handler) {\n    const middleware = this.#middleware;\n    const routes = this.#routes;\n    if (!middleware || !routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    if (!middleware[method]) {\n      ;\n      [middleware, routes].forEach((handlerMap) => {\n        handlerMap[method] = /* @__PURE__ */ Object.create(null);\n        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {\n          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];\n        });\n      });\n    }\n    if (path === \"/*\") {\n      path = \"*\";\n    }\n    const paramCount = (path.match(/\\/:/g) || []).length;\n    if (/\\*$/.test(path)) {\n      const re = buildWildcardRegExp(path);\n      if (method === METHOD_NAME_ALL) {\n        Object.keys(middleware).forEach((m) => {\n          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n        });\n      } else {\n        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n      }\n      Object.keys(middleware).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(middleware[m]).forEach((p) => {\n            re.test(p) && middleware[m][p].push([handler, paramCount]);\n          });\n        }\n      });\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(routes[m]).forEach(\n            (p) => re.test(p) && routes[m][p].push([handler, paramCount])\n          );\n        }\n      });\n      return;\n    }\n    const paths = checkOptionalParameter(path) || [path];\n    for (let i = 0, len = paths.length; i < len; i++) {\n      const path2 = paths[i];\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          routes[m][path2] ||= [\n            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []\n          ];\n          routes[m][path2].push([handler, paramCount - len + i + 1]);\n        }\n      });\n    }\n  }\n  match(method, path) {\n    clearWildcardRegExpCache();\n    const matchers = this.#buildAllMatchers();\n    this.match = (method2, path2) => {\n      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];\n      const staticMatch = matcher[2][path2];\n      if (staticMatch) {\n        return staticMatch;\n      }\n      const match = path2.match(matcher[0]);\n      if (!match) {\n        return [[], emptyParam];\n      }\n      const index = match.indexOf(\"\", 1);\n      return [matcher[1][index], match];\n    };\n    return this.match(method, path);\n  }\n  #buildAllMatchers() {\n    const matchers = /* @__PURE__ */ Object.create(null);\n    Object.keys(this.#routes).concat(Object.keys(this.#middleware)).forEach((method) => {\n      matchers[method] ||= this.#buildMatcher(method);\n    });\n    this.#middleware = this.#routes = void 0;\n    return matchers;\n  }\n  #buildMatcher(method) {\n    const routes = [];\n    let hasOwnRoute = method === METHOD_NAME_ALL;\n    [this.#middleware, this.#routes].forEach((r) => {\n      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];\n      if (ownRoute.length !== 0) {\n        hasOwnRoute ||= true;\n        routes.push(...ownRoute);\n      } else if (method !== METHOD_NAME_ALL) {\n        routes.push(\n          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])\n        );\n      }\n    });\n    if (!hasOwnRoute) {\n      return null;\n    } else {\n      return buildMatcherFromPreprocessedRoutes(routes);\n    }\n  }\n};\nexport {\n  RegExpRouter\n};\n", "// src/router/smart-router/router.ts\nimport { MESSAGE_MATCHER_IS_ALREADY_BUILT, UnsupportedPathError } from \"../../router.js\";\nvar SmartRouter = class {\n  name = \"SmartRouter\";\n  #routers = [];\n  #routes = [];\n  constructor(init) {\n    this.#routers = init.routers;\n  }\n  add(method, path, handler) {\n    if (!this.#routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    this.#routes.push([method, path, handler]);\n  }\n  match(method, path) {\n    if (!this.#routes) {\n      throw new Error(\"Fatal error\");\n    }\n    const routers = this.#routers;\n    const routes = this.#routes;\n    const len = routers.length;\n    let i = 0;\n    let res;\n    for (; i < len; i++) {\n      const router = routers[i];\n      try {\n        for (let i2 = 0, len2 = routes.length; i2 < len2; i2++) {\n          router.add(...routes[i2]);\n        }\n        res = router.match(method, path);\n      } catch (e) {\n        if (e instanceof UnsupportedPathError) {\n          continue;\n        }\n        throw e;\n      }\n      this.match = router.match.bind(router);\n      this.#routers = [router];\n      this.#routes = void 0;\n      break;\n    }\n    if (i === len) {\n      throw new Error(\"Fatal error\");\n    }\n    this.name = `SmartRouter + ${this.activeRouter.name}`;\n    return res;\n  }\n  get activeRouter() {\n    if (this.#routes || this.#routers.length !== 1) {\n      throw new Error(\"No active router has been determined yet.\");\n    }\n    return this.#routers[0];\n  }\n};\nexport {\n  SmartRouter\n};\n", "// src/router/trie-router/node.ts\nimport { METHOD_NAME_ALL } from \"../../router.js\";\nimport { getPattern, splitPath, splitRoutingPath } from \"../../utils/url.js\";\nvar emptyParams = /* @__PURE__ */ Object.create(null);\nvar Node = class {\n  #methods;\n  #children;\n  #patterns;\n  #order = 0;\n  #params = emptyParams;\n  constructor(method, handler, children) {\n    this.#children = children || /* @__PURE__ */ Object.create(null);\n    this.#methods = [];\n    if (method && handler) {\n      const m = /* @__PURE__ */ Object.create(null);\n      m[method] = { handler, possibleKeys: [], score: 0 };\n      this.#methods = [m];\n    }\n    this.#patterns = [];\n  }\n  insert(method, path, handler) {\n    this.#order = ++this.#order;\n    let curNode = this;\n    const parts = splitRoutingPath(path);\n    const possibleKeys = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const p = parts[i];\n      const nextP = parts[i + 1];\n      const pattern = getPattern(p, nextP);\n      const key = Array.isArray(pattern) ? pattern[0] : p;\n      if (key in curNode.#children) {\n        curNode = curNode.#children[key];\n        if (pattern) {\n          possibleKeys.push(pattern[1]);\n        }\n        continue;\n      }\n      curNode.#children[key] = new Node();\n      if (pattern) {\n        curNode.#patterns.push(pattern);\n        possibleKeys.push(pattern[1]);\n      }\n      curNode = curNode.#children[key];\n    }\n    curNode.#methods.push({\n      [method]: {\n        handler,\n        possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),\n        score: this.#order\n      }\n    });\n    return curNode;\n  }\n  #getHandlerSets(node, method, nodeParams, params) {\n    const handlerSets = [];\n    for (let i = 0, len = node.#methods.length; i < len; i++) {\n      const m = node.#methods[i];\n      const handlerSet = m[method] || m[METHOD_NAME_ALL];\n      const processedSet = {};\n      if (handlerSet !== void 0) {\n        handlerSet.params = /* @__PURE__ */ Object.create(null);\n        handlerSets.push(handlerSet);\n        if (nodeParams !== emptyParams || params && params !== emptyParams) {\n          for (let i2 = 0, len2 = handlerSet.possibleKeys.length; i2 < len2; i2++) {\n            const key = handlerSet.possibleKeys[i2];\n            const processed = processedSet[handlerSet.score];\n            handlerSet.params[key] = params?.[key] && !processed ? params[key] : nodeParams[key] ?? params?.[key];\n            processedSet[handlerSet.score] = true;\n          }\n        }\n      }\n    }\n    return handlerSets;\n  }\n  search(method, path) {\n    const handlerSets = [];\n    this.#params = emptyParams;\n    const curNode = this;\n    let curNodes = [curNode];\n    const parts = splitPath(path);\n    const curNodesQueue = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const part = parts[i];\n      const isLast = i === len - 1;\n      const tempNodes = [];\n      for (let j = 0, len2 = curNodes.length; j < len2; j++) {\n        const node = curNodes[j];\n        const nextNode = node.#children[part];\n        if (nextNode) {\n          nextNode.#params = node.#params;\n          if (isLast) {\n            if (nextNode.#children[\"*\"]) {\n              handlerSets.push(\n                ...this.#getHandlerSets(nextNode.#children[\"*\"], method, node.#params)\n              );\n            }\n            handlerSets.push(...this.#getHandlerSets(nextNode, method, node.#params));\n          } else {\n            tempNodes.push(nextNode);\n          }\n        }\n        for (let k = 0, len3 = node.#patterns.length; k < len3; k++) {\n          const pattern = node.#patterns[k];\n          const params = node.#params === emptyParams ? {} : { ...node.#params };\n          if (pattern === \"*\") {\n            const astNode = node.#children[\"*\"];\n            if (astNode) {\n              handlerSets.push(...this.#getHandlerSets(astNode, method, node.#params));\n              astNode.#params = params;\n              tempNodes.push(astNode);\n            }\n            continue;\n          }\n          if (!part) {\n            continue;\n          }\n          const [key, name, matcher] = pattern;\n          const child = node.#children[key];\n          const restPathString = parts.slice(i).join(\"/\");\n          if (matcher instanceof RegExp) {\n            const m = matcher.exec(restPathString);\n            if (m) {\n              params[name] = m[0];\n              handlerSets.push(...this.#getHandlerSets(child, method, node.#params, params));\n              if (Object.keys(child.#children).length) {\n                child.#params = params;\n                const componentCount = m[0].match(/\\//)?.length ?? 0;\n                const targetCurNodes = curNodesQueue[componentCount] ||= [];\n                targetCurNodes.push(child);\n              }\n              continue;\n            }\n          }\n          if (matcher === true || matcher.test(part)) {\n            params[name] = part;\n            if (isLast) {\n              handlerSets.push(...this.#getHandlerSets(child, method, params, node.#params));\n              if (child.#children[\"*\"]) {\n                handlerSets.push(\n                  ...this.#getHandlerSets(child.#children[\"*\"], method, params, node.#params)\n                );\n              }\n            } else {\n              child.#params = params;\n              tempNodes.push(child);\n            }\n          }\n        }\n      }\n      curNodes = tempNodes.concat(curNodesQueue.shift() ?? []);\n    }\n    if (handlerSets.length > 1) {\n      handlerSets.sort((a, b) => {\n        return a.score - b.score;\n      });\n    }\n    return [handlerSets.map(({ handler, params }) => [handler, params])];\n  }\n};\nexport {\n  Node\n};\n", "// src/router/trie-router/router.ts\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { Node } from \"./node.js\";\nvar TrieRouter = class {\n  name = \"TrieRouter\";\n  #node;\n  constructor() {\n    this.#node = new Node();\n  }\n  add(method, path, handler) {\n    const results = checkOptionalParameter(path);\n    if (results) {\n      for (let i = 0, len = results.length; i < len; i++) {\n        this.#node.insert(method, results[i], handler);\n      }\n      return;\n    }\n    this.#node.insert(method, path, handler);\n  }\n  match(method, path) {\n    return this.#node.search(method, path);\n  }\n};\nexport {\n  TrieRouter\n};\n", "// src/hono.ts\nimport { HonoBase } from \"./hono-base.js\";\nimport { RegExpRouter } from \"./router/reg-exp-router/index.js\";\nimport { SmartRouter } from \"./router/smart-router/index.js\";\nimport { TrieRouter } from \"./router/trie-router/index.js\";\nvar Hono = class extends HonoBase {\n  constructor(options = {}) {\n    super(options);\n    this.router = options.router ?? new SmartRouter({\n      routers: [new RegExpRouter(), new TrieRouter()]\n    });\n  }\n};\nexport {\n  Hono\n};\n", "// src/middleware/cors/index.ts\nvar cors = (options) => {\n  const defaults = {\n    origin: \"*\",\n    allowMethods: [\"GET\", \"HEAD\", \"PUT\", \"POST\", \"DELETE\", \"PATCH\"],\n    allowHeaders: [],\n    exposeHeaders: []\n  };\n  const opts = {\n    ...defaults,\n    ...options\n  };\n  const findAllowOrigin = ((optsOrigin) => {\n    if (typeof optsOrigin === \"string\") {\n      if (optsOrigin === \"*\") {\n        return () => optsOrigin;\n      } else {\n        return (origin) => optsOrigin === origin ? origin : null;\n      }\n    } else if (typeof optsOrigin === \"function\") {\n      return optsOrigin;\n    } else {\n      return (origin) => optsOrigin.includes(origin) ? origin : null;\n    }\n  })(opts.origin);\n  const findAllowMethods = ((optsAllowMethods) => {\n    if (typeof optsAllowMethods === \"function\") {\n      return optsAllowMethods;\n    } else if (Array.isArray(optsAllowMethods)) {\n      return () => optsAllowMethods;\n    } else {\n      return () => [];\n    }\n  })(opts.allowMethods);\n  return async function cors2(c, next) {\n    function set(key, value) {\n      c.res.headers.set(key, value);\n    }\n    const allowOrigin = findAllowOrigin(c.req.header(\"origin\") || \"\", c);\n    if (allowOrigin) {\n      set(\"Access-Control-Allow-Origin\", allowOrigin);\n    }\n    if (opts.origin !== \"*\") {\n      const existingVary = c.req.header(\"Vary\");\n      if (existingVary) {\n        set(\"Vary\", existingVary);\n      } else {\n        set(\"Vary\", \"Origin\");\n      }\n    }\n    if (opts.credentials) {\n      set(\"Access-Control-Allow-Credentials\", \"true\");\n    }\n    if (opts.exposeHeaders?.length) {\n      set(\"Access-Control-Expose-Headers\", opts.exposeHeaders.join(\",\"));\n    }\n    if (c.req.method === \"OPTIONS\") {\n      if (opts.maxAge != null) {\n        set(\"Access-Control-Max-Age\", opts.maxAge.toString());\n      }\n      const allowMethods = findAllowMethods(c.req.header(\"origin\") || \"\", c);\n      if (allowMethods.length) {\n        set(\"Access-Control-Allow-Methods\", allowMethods.join(\",\"));\n      }\n      let headers = opts.allowHeaders;\n      if (!headers?.length) {\n        const requestHeaders = c.req.header(\"Access-Control-Request-Headers\");\n        if (requestHeaders) {\n          headers = requestHeaders.split(/\\s*,\\s*/);\n        }\n      }\n      if (headers?.length) {\n        set(\"Access-Control-Allow-Headers\", headers.join(\",\"));\n        c.res.headers.append(\"Vary\", \"Access-Control-Request-Headers\");\n      }\n      c.res.headers.delete(\"Content-Length\");\n      c.res.headers.delete(\"Content-Type\");\n      return new Response(null, {\n        headers: c.res.headers,\n        status: 204,\n        statusText: \"No Content\"\n      });\n    }\n    await next();\n  };\n};\nexport {\n  cors\n};\n", "// src/utils/color.ts\nfunction getColorEnabled() {\n  const { process, Deno } = globalThis;\n  const isNoColor = typeof Deno?.noColor === \"boolean\" ? Deno.noColor : process !== void 0 ? \"NO_COLOR\" in process?.env : false;\n  return !isNoColor;\n}\nasync function getColorEnabledAsync() {\n  const { navigator } = globalThis;\n  const isNoColor = navigator !== void 0 && navigator.userAgent === \"Cloudflare-Workers\" ? \"NO_COLOR\" in ((await import(\"cloudflare:workers\")).env ?? {}) : !getColorEnabled();\n  return !isNoColor;\n}\nexport {\n  getColorEnabled,\n  getColorEnabledAsync\n};\n", "// src/middleware/logger/index.ts\nimport { getColorEnabledAsync } from \"../../utils/color.js\";\nvar humanize = (times) => {\n  const [delimiter, separator] = [\",\", \".\"];\n  const orderTimes = times.map((v) => v.replace(/(\\d)(?=(\\d\\d\\d)+(?!\\d))/g, \"$1\" + delimiter));\n  return orderTimes.join(separator);\n};\nvar time = (start) => {\n  const delta = Date.now() - start;\n  return humanize([delta < 1e3 ? delta + \"ms\" : Math.round(delta / 1e3) + \"s\"]);\n};\nvar colorStatus = async (status) => {\n  const colorEnabled = await getColorEnabledAsync();\n  if (colorEnabled) {\n    switch (status / 100 | 0) {\n      case 5:\n        return `\\x1B[31m${status}\\x1B[0m`;\n      case 4:\n        return `\\x1B[33m${status}\\x1B[0m`;\n      case 3:\n        return `\\x1B[36m${status}\\x1B[0m`;\n      case 2:\n        return `\\x1B[32m${status}\\x1B[0m`;\n    }\n  }\n  return `${status}`;\n};\nasync function log(fn, prefix, method, path, status = 0, elapsed) {\n  const out = prefix === \"<--\" /* Incoming */ ? `${prefix} ${method} ${path}` : `${prefix} ${method} ${path} ${await colorStatus(status)} ${elapsed}`;\n  fn(out);\n}\nvar logger = (fn = console.log) => {\n  return async function logger2(c, next) {\n    const { method, url } = c.req;\n    const path = url.slice(url.indexOf(\"/\", 8));\n    await log(fn, \"<--\" /* Incoming */, method, path);\n    const start = Date.now();\n    await next();\n    await log(fn, \"-->\" /* Outgoing */, method, path, c.res.status, time(start));\n  };\n};\nexport {\n  logger\n};\n", "// src/middleware/pretty-json/index.ts\nvar prettyJSON = (options) => {\n  const targetQuery = options?.query ?? \"pretty\";\n  return async function prettyJSON2(c, next) {\n    const pretty = c.req.query(targetQuery) || c.req.query(targetQuery) === \"\";\n    await next();\n    if (pretty && c.res.headers.get(\"Content-Type\")?.startsWith(\"application/json\")) {\n      const obj = await c.res.json();\n      c.res = new Response(JSON.stringify(obj, null, options?.space ?? 2), c.res);\n    }\n  };\n};\nexport {\n  prettyJSON\n};\n", "// src/middleware/secure-headers/secure-headers.ts\nimport { encodeBase64 } from \"../../utils/encode.js\";\nvar HEADERS_MAP = {\n  crossOriginEmbedderPolicy: [\"Cross-Origin-Embedder-Policy\", \"require-corp\"],\n  crossOriginResourcePolicy: [\"Cross-Origin-Resource-Policy\", \"same-origin\"],\n  crossOriginOpenerPolicy: [\"Cross-Origin-Opener-Policy\", \"same-origin\"],\n  originAgentCluster: [\"Origin-Agent-Cluster\", \"?1\"],\n  referrerPolicy: [\"Referrer-Policy\", \"no-referrer\"],\n  strictTransportSecurity: [\"Strict-Transport-Security\", \"max-age=15552000; includeSubDomains\"],\n  xContentTypeOptions: [\"X-Content-Type-Options\", \"nosniff\"],\n  xDnsPrefetchControl: [\"X-DNS-Prefetch-Control\", \"off\"],\n  xDownloadOptions: [\"X-Download-Options\", \"noopen\"],\n  xFrameOptions: [\"X-Frame-Options\", \"SAMEORIGIN\"],\n  xPermittedCrossDomainPolicies: [\"X-Permitted-Cross-Domain-Policies\", \"none\"],\n  xXssProtection: [\"X-XSS-Protection\", \"0\"]\n};\nvar DEFAULT_OPTIONS = {\n  crossOriginEmbedderPolicy: false,\n  crossOriginResourcePolicy: true,\n  crossOriginOpenerPolicy: true,\n  originAgentCluster: true,\n  referrerPolicy: true,\n  strictTransportSecurity: true,\n  xContentTypeOptions: true,\n  xDnsPrefetchControl: true,\n  xDownloadOptions: true,\n  xFrameOptions: true,\n  xPermittedCrossDomainPolicies: true,\n  xXssProtection: true,\n  removePoweredBy: true,\n  permissionsPolicy: {}\n};\nvar generateNonce = () => {\n  const arrayBuffer = new Uint8Array(16);\n  crypto.getRandomValues(arrayBuffer);\n  return encodeBase64(arrayBuffer.buffer);\n};\nvar NONCE = (ctx) => {\n  const key = \"secureHeadersNonce\";\n  const init = ctx.get(key);\n  const nonce = init || generateNonce();\n  if (init == null) {\n    ctx.set(key, nonce);\n  }\n  return `'nonce-${nonce}'`;\n};\nvar secureHeaders = (customOptions) => {\n  const options = { ...DEFAULT_OPTIONS, ...customOptions };\n  const headersToSet = getFilteredHeaders(options);\n  const callbacks = [];\n  if (options.contentSecurityPolicy) {\n    const [callback, value] = getCSPDirectives(options.contentSecurityPolicy);\n    if (callback) {\n      callbacks.push(callback);\n    }\n    headersToSet.push([\"Content-Security-Policy\", value]);\n  }\n  if (options.contentSecurityPolicyReportOnly) {\n    const [callback, value] = getCSPDirectives(options.contentSecurityPolicyReportOnly);\n    if (callback) {\n      callbacks.push(callback);\n    }\n    headersToSet.push([\"Content-Security-Policy-Report-Only\", value]);\n  }\n  if (options.permissionsPolicy && Object.keys(options.permissionsPolicy).length > 0) {\n    headersToSet.push([\n      \"Permissions-Policy\",\n      getPermissionsPolicyDirectives(options.permissionsPolicy)\n    ]);\n  }\n  if (options.reportingEndpoints) {\n    headersToSet.push([\"Reporting-Endpoints\", getReportingEndpoints(options.reportingEndpoints)]);\n  }\n  if (options.reportTo) {\n    headersToSet.push([\"Report-To\", getReportToOptions(options.reportTo)]);\n  }\n  return async function secureHeaders2(ctx, next) {\n    const headersToSetForReq = callbacks.length === 0 ? headersToSet : callbacks.reduce((acc, cb) => cb(ctx, acc), headersToSet);\n    await next();\n    setHeaders(ctx, headersToSetForReq);\n    if (options?.removePoweredBy) {\n      ctx.res.headers.delete(\"X-Powered-By\");\n    }\n  };\n};\nfunction getFilteredHeaders(options) {\n  return Object.entries(HEADERS_MAP).filter(([key]) => options[key]).map(([key, defaultValue]) => {\n    const overrideValue = options[key];\n    return typeof overrideValue === \"string\" ? [defaultValue[0], overrideValue] : defaultValue;\n  });\n}\nfunction getCSPDirectives(contentSecurityPolicy) {\n  const callbacks = [];\n  const resultValues = [];\n  for (const [directive, value] of Object.entries(contentSecurityPolicy)) {\n    const valueArray = Array.isArray(value) ? value : [value];\n    valueArray.forEach((value2, i) => {\n      if (typeof value2 === \"function\") {\n        const index = i * 2 + 2 + resultValues.length;\n        callbacks.push((ctx, values) => {\n          values[index] = value2(ctx, directive);\n        });\n      }\n    });\n    resultValues.push(\n      directive.replace(\n        /[A-Z]+(?![a-z])|[A-Z]/g,\n        (match, offset) => offset ? \"-\" + match.toLowerCase() : match.toLowerCase()\n      ),\n      ...valueArray.flatMap((value2) => [\" \", value2]),\n      \"; \"\n    );\n  }\n  resultValues.pop();\n  return callbacks.length === 0 ? [void 0, resultValues.join(\"\")] : [\n    (ctx, headersToSet) => headersToSet.map((values) => {\n      if (values[0] === \"Content-Security-Policy\" || values[0] === \"Content-Security-Policy-Report-Only\") {\n        const clone = values[1].slice();\n        callbacks.forEach((cb) => {\n          cb(ctx, clone);\n        });\n        return [values[0], clone.join(\"\")];\n      } else {\n        return values;\n      }\n    }),\n    resultValues\n  ];\n}\nfunction getPermissionsPolicyDirectives(policy) {\n  return Object.entries(policy).map(([directive, value]) => {\n    const kebabDirective = camelToKebab(directive);\n    if (typeof value === \"boolean\") {\n      return `${kebabDirective}=${value ? \"*\" : \"none\"}`;\n    }\n    if (Array.isArray(value)) {\n      if (value.length === 0) {\n        return `${kebabDirective}=()`;\n      }\n      if (value.length === 1 && (value[0] === \"*\" || value[0] === \"none\")) {\n        return `${kebabDirective}=${value[0]}`;\n      }\n      const allowlist = value.map((item) => [\"self\", \"src\"].includes(item) ? item : `\"${item}\"`);\n      return `${kebabDirective}=(${allowlist.join(\" \")})`;\n    }\n    return \"\";\n  }).filter(Boolean).join(\", \");\n}\nfunction camelToKebab(str) {\n  return str.replace(/([a-z\\d])([A-Z])/g, \"$1-$2\").toLowerCase();\n}\nfunction getReportingEndpoints(reportingEndpoints = []) {\n  return reportingEndpoints.map((endpoint) => `${endpoint.name}=\"${endpoint.url}\"`).join(\", \");\n}\nfunction getReportToOptions(reportTo = []) {\n  return reportTo.map((option) => JSON.stringify(option)).join(\", \");\n}\nfunction setHeaders(ctx, headersToSet) {\n  headersToSet.forEach(([header, value]) => {\n    ctx.res.headers.set(header, value);\n  });\n}\nexport {\n  NONCE,\n  secureHeaders\n};\n", "// CloudNav 2.0 - KV 存储服务\nimport type { KVData, Bookmark, Category, Stats, Config } from '@/types';\n\nexport class KVService {\n  private kv: KVNamespace;\n  private readonly VERSION = '2.0.0';\n\n  constructor(kv: KVNamespace) {\n    this.kv = kv;\n  }\n\n  // 通用 KV 操作\n  private async get<T>(key: string): Promise<T | null> {\n    try {\n      const data = await this.kv.get(key, 'json');\n      return data as T;\n    } catch (error) {\n      console.error(`KV get error for key ${key}:`, error);\n      return null;\n    }\n  }\n\n  private async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {\n    try {\n      const kvData: KVData<T> = {\n        data: value,\n        lastUpdated: Date.now(),\n        version: this.VERSION\n      };\n      \n      await this.kv.put(key, JSON.stringify(kvData), {\n        expirationTtl: ttl\n      });\n      return true;\n    } catch (error) {\n      console.error(`KV set error for key ${key}:`, error);\n      return false;\n    }\n  }\n\n  private async getWithMetadata<T>(key: string): Promise<KVData<T> | null> {\n    try {\n      const result = await this.kv.get(key, 'json');\n      return result as KVData<T>;\n    } catch (error) {\n      console.error(`KV getWithMetadata error for key ${key}:`, error);\n      return null;\n    }\n  }\n\n  // 书签操作\n  async getBookmarks(): Promise<Bookmark[]> {\n    const result = await this.getWithMetadata<Bookmark[]>('cloudnav:bookmarks');\n    return result?.data || [];\n  }\n\n  async setBookmarks(bookmarks: Bookmark[]): Promise<boolean> {\n    return await this.set('cloudnav:bookmarks', bookmarks);\n  }\n\n  async addBookmark(bookmark: Bookmark): Promise<boolean> {\n    const bookmarks = await this.getBookmarks();\n    bookmarks.push(bookmark);\n    return await this.setBookmarks(bookmarks);\n  }\n\n  async updateBookmark(id: string, updates: Partial<Bookmark>): Promise<boolean> {\n    const bookmarks = await this.getBookmarks();\n    const index = bookmarks.findIndex(b => b.id === id);\n    \n    if (index === -1) return false;\n    \n    bookmarks[index] = { ...bookmarks[index], ...updates, updatedAt: Date.now() };\n    return await this.setBookmarks(bookmarks);\n  }\n\n  async deleteBookmark(id: string): Promise<boolean> {\n    const bookmarks = await this.getBookmarks();\n    const filtered = bookmarks.filter(b => b.id !== id);\n    \n    if (filtered.length === bookmarks.length) return false;\n    \n    return await this.setBookmarks(filtered);\n  }\n\n  // 分类操作\n  async getCategories(): Promise<Category[]> {\n    const result = await this.getWithMetadata<Category[]>('cloudnav:categories');\n    return result?.data || [];\n  }\n\n  async setCategories(categories: Category[]): Promise<boolean> {\n    return await this.set('cloudnav:categories', categories);\n  }\n\n  async addCategory(category: Category): Promise<boolean> {\n    const categories = await this.getCategories();\n    categories.push(category);\n    return await this.setCategories(categories);\n  }\n\n  async updateCategory(id: string, updates: Partial<Category>): Promise<boolean> {\n    const categories = await this.getCategories();\n    const index = categories.findIndex(c => c.id === id);\n    \n    if (index === -1) return false;\n    \n    categories[index] = { ...categories[index], ...updates };\n    return await this.setCategories(categories);\n  }\n\n  async deleteCategory(id: string): Promise<boolean> {\n    const categories = await this.getCategories();\n    const filtered = categories.filter(c => c.id !== id);\n    \n    if (filtered.length === categories.length) return false;\n    \n    return await this.setCategories(filtered);\n  }\n\n  // 统计操作\n  async getStats(): Promise<Stats> {\n    const result = await this.getWithMetadata<Stats>('cloudnav:stats');\n    return result?.data || {\n      totalClicks: 0,\n      totalViews: 0,\n      bookmarkStats: {},\n      categoryStats: {},\n      searchStats: {},\n      dailyStats: {},\n      deviceStats: { mobile: 0, desktop: 0, tablet: 0 },\n      lastUpdated: Date.now()\n    };\n  }\n\n  async updateStats(updates: Partial<Stats>): Promise<boolean> {\n    const stats = await this.getStats();\n    const updatedStats = { ...stats, ...updates, lastUpdated: Date.now() };\n    return await this.set('cloudnav:stats', updatedStats);\n  }\n\n  async incrementBookmarkClick(bookmarkId: string): Promise<boolean> {\n    const stats = await this.getStats();\n    stats.totalClicks++;\n    stats.bookmarkStats[bookmarkId] = (stats.bookmarkStats[bookmarkId] || 0) + 1;\n    \n    // 更新日统计\n    const today = new Date().toISOString().split('T')[0];\n    stats.dailyStats[today] = (stats.dailyStats[today] || 0) + 1;\n    \n    return await this.updateStats(stats);\n  }\n\n  // 配置操作\n  async getConfig(): Promise<Config> {\n    const result = await this.getWithMetadata<Config>('cloudnav:config');\n    return result?.data || {\n      siteName: 'CloudNav',\n      siteDescription: '智能书签导航站',\n      aiConfig: { enabled: false },\n      features: { stats: true, ai: false, import: true, export: true },\n      theme: { primaryColor: '#7C3AED', darkMode: false }\n    };\n  }\n\n  async updateConfig(updates: Partial<Config>): Promise<boolean> {\n    const config = await this.getConfig();\n    const updatedConfig = { ...config, ...updates };\n    return await this.set('cloudnav:config', updatedConfig);\n  }\n\n  // 数据备份和恢复\n  async backup(): Promise<{ bookmarks: Bookmark[], categories: Category[], stats: Stats, config: Config }> {\n    const [bookmarks, categories, stats, config] = await Promise.all([\n      this.getBookmarks(),\n      this.getCategories(),\n      this.getStats(),\n      this.getConfig()\n    ]);\n\n    return { bookmarks, categories, stats, config };\n  }\n\n  async restore(data: { bookmarks?: Bookmark[], categories?: Category[], stats?: Stats, config?: Config }): Promise<boolean> {\n    try {\n      const promises = [];\n      \n      if (data.bookmarks) promises.push(this.setBookmarks(data.bookmarks));\n      if (data.categories) promises.push(this.setCategories(data.categories));\n      if (data.stats) promises.push(this.updateStats(data.stats));\n      if (data.config) promises.push(this.updateConfig(data.config));\n      \n      await Promise.all(promises);\n      return true;\n    } catch (error) {\n      console.error('Restore error:', error);\n      return false;\n    }\n  }\n\n  // 清理和维护\n  async cleanup(): Promise<boolean> {\n    try {\n      // 清理过期的统计数据（保留30天）\n      const stats = await this.getStats();\n      const thirtyDaysAgo = new Date();\n      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n      \n      Object.keys(stats.dailyStats).forEach(date => {\n        if (new Date(date) < thirtyDaysAgo) {\n          delete stats.dailyStats[date];\n        }\n      });\n      \n      await this.updateStats(stats);\n      return true;\n    } catch (error) {\n      console.error('Cleanup error:', error);\n      return false;\n    }\n  }\n}\n", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "/* @ts-self-types=\"./index.d.ts\" */\nimport { url<PERSON>lphabet as scopedUrlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet } from './url-alphabet/index.js'\nexport let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log2(alphabet.length - 1)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size | 0, random)\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let bytes = crypto.getRandomValues(new Uint8Array((size |= 0)))\n  while (size--) {\n    id += scopedUrlAlphabet[bytes[size] & 63]\n  }\n  return id\n}\n", "// CloudNav 2.0 - 书签管理服务\nimport { nanoid } from 'nanoid';\nimport type { Bookmark, Category } from '@/types';\nimport { KVService } from './kv';\n\nexport class BookmarkService {\n  private kvService: KVService;\n\n  constructor(kvService: KVService) {\n    this.kvService = kvService;\n  }\n\n  // 获取所有书签\n  async getAllBookmarks(): Promise<Bookmark[]> {\n    return await this.kvService.getBookmarks();\n  }\n\n  // 根据ID获取书签\n  async getBookmarkById(id: string): Promise<Bookmark | null> {\n    const bookmarks = await this.getAllBookmarks();\n    return bookmarks.find(bookmark => bookmark.id === id) || null;\n  }\n\n  // 根据分类获取书签\n  async getBookmarksByCategory(categoryId: string): Promise<Bookmark[]> {\n    const bookmarks = await this.getAllBookmarks();\n    return bookmarks.filter(bookmark => bookmark.category === categoryId);\n  }\n\n  // 搜索书签\n  async searchBookmarks(query: string): Promise<Bookmark[]> {\n    const bookmarks = await this.getAllBookmarks();\n    const searchTerm = query.toLowerCase().trim();\n    \n    if (!searchTerm) return bookmarks;\n\n    return bookmarks.filter(bookmark => \n      bookmark.title.toLowerCase().includes(searchTerm) ||\n      bookmark.description?.toLowerCase().includes(searchTerm) ||\n      bookmark.shortDesc?.toLowerCase().includes(searchTerm) ||\n      bookmark.url.toLowerCase().includes(searchTerm) ||\n      bookmark.tags?.some(tag => tag.toLowerCase().includes(searchTerm))\n    );\n  }\n\n  // 创建新书签\n  async createBookmark(bookmarkData: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt' | 'clickCount'>): Promise<Bookmark> {\n    const now = Date.now();\n    const newBookmark: Bookmark = {\n      id: nanoid(),\n      ...bookmarkData,\n      createdAt: now,\n      updatedAt: now,\n      clickCount: 0\n    };\n\n    const success = await this.kvService.addBookmark(newBookmark);\n    if (!success) {\n      throw new Error('Failed to create bookmark');\n    }\n\n    return newBookmark;\n  }\n\n  // 更新书签\n  async updateBookmark(id: string, updates: Partial<Omit<Bookmark, 'id' | 'createdAt'>>): Promise<Bookmark | null> {\n    const bookmark = await this.getBookmarkById(id);\n    if (!bookmark) {\n      return null;\n    }\n\n    const updatedData = {\n      ...updates,\n      updatedAt: Date.now()\n    };\n\n    const success = await this.kvService.updateBookmark(id, updatedData);\n    if (!success) {\n      throw new Error('Failed to update bookmark');\n    }\n\n    return { ...bookmark, ...updatedData };\n  }\n\n  // 删除书签\n  async deleteBookmark(id: string): Promise<boolean> {\n    const bookmark = await this.getBookmarkById(id);\n    if (!bookmark) {\n      return false;\n    }\n\n    return await this.kvService.deleteBookmark(id);\n  }\n\n  // 批量删除书签\n  async deleteBookmarks(ids: string[]): Promise<{ success: string[], failed: string[] }> {\n    const results: { success: string[], failed: string[] } = { success: [], failed: [] };\n\n    for (const id of ids) {\n      const deleted = await this.deleteBookmark(id);\n      if (deleted) {\n        results.success.push(id);\n      } else {\n        results.failed.push(id);\n      }\n    }\n\n    return results;\n  }\n\n  // 移动书签到不同分类\n  async moveBookmarkToCategory(bookmarkId: string, newCategoryId: string): Promise<boolean> {\n    return await this.updateBookmark(bookmarkId, { category: newCategoryId }) !== null;\n  }\n\n  // 批量移动书签\n  async moveBookmarksToCategory(bookmarkIds: string[], newCategoryId: string): Promise<{ success: string[], failed: string[] }> {\n    const results: { success: string[], failed: string[] } = { success: [], failed: [] };\n\n    for (const id of bookmarkIds) {\n      const moved = await this.moveBookmarkToCategory(id, newCategoryId);\n      if (moved) {\n        results.success.push(id);\n      } else {\n        results.failed.push(id);\n      }\n    }\n\n    return results;\n  }\n\n  // 增加点击计数\n  async incrementClickCount(id: string): Promise<boolean> {\n    const bookmark = await this.getBookmarkById(id);\n    if (!bookmark) {\n      return false;\n    }\n\n    const success = await this.kvService.updateBookmark(id, {\n      clickCount: bookmark.clickCount + 1,\n      updatedAt: Date.now()\n    });\n\n    // 同时更新统计数据\n    if (success) {\n      await this.kvService.incrementBookmarkClick(id);\n    }\n\n    return success;\n  }\n\n  // 获取热门书签\n  async getPopularBookmarks(limit: number = 10): Promise<Bookmark[]> {\n    const bookmarks = await this.getAllBookmarks();\n    return bookmarks\n      .sort((a, b) => b.clickCount - a.clickCount)\n      .slice(0, limit);\n  }\n\n  // 获取最近添加的书签\n  async getRecentBookmarks(limit: number = 10): Promise<Bookmark[]> {\n    const bookmarks = await this.getAllBookmarks();\n    return bookmarks\n      .sort((a, b) => b.createdAt - a.createdAt)\n      .slice(0, limit);\n  }\n\n  // 获取最近更新的书签\n  async getRecentlyUpdatedBookmarks(limit: number = 10): Promise<Bookmark[]> {\n    const bookmarks = await this.getAllBookmarks();\n    return bookmarks\n      .sort((a, b) => b.updatedAt - a.updatedAt)\n      .slice(0, limit);\n  }\n\n  // 验证书签数据\n  validateBookmarkData(data: any): { valid: boolean; errors: string[] } {\n    const errors: string[] = [];\n\n    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {\n      errors.push('标题是必需的');\n    }\n\n    if (!data.url || typeof data.url !== 'string') {\n      errors.push('URL是必需的');\n    } else {\n      try {\n        new URL(data.url);\n      } catch {\n        errors.push('URL格式无效');\n      }\n    }\n\n    if (!data.category || typeof data.category !== 'string') {\n      errors.push('分类是必需的');\n    }\n\n    if (data.tags && !Array.isArray(data.tags)) {\n      errors.push('标签必须是数组');\n    }\n\n    return {\n      valid: errors.length === 0,\n      errors\n    };\n  }\n\n  // 检查重复书签\n  async checkDuplicateBookmark(url: string, excludeId?: string): Promise<Bookmark | null> {\n    const bookmarks = await this.getAllBookmarks();\n    return bookmarks.find(bookmark => \n      bookmark.url === url && bookmark.id !== excludeId\n    ) || null;\n  }\n\n  // 获取书签统计信息\n  async getBookmarkStats(): Promise<{\n    total: number;\n    byCategory: Record<string, number>;\n    totalClicks: number;\n    averageClicksPerBookmark: number;\n  }> {\n    const bookmarks = await this.getAllBookmarks();\n    const total = bookmarks.length;\n    const totalClicks = bookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);\n    \n    const byCategory: Record<string, number> = {};\n    bookmarks.forEach(bookmark => {\n      byCategory[bookmark.category] = (byCategory[bookmark.category] || 0) + 1;\n    });\n\n    return {\n      total,\n      byCategory,\n      totalClicks,\n      averageClicksPerBookmark: total > 0 ? totalClicks / total : 0\n    };\n  }\n\n  // 导出书签数据\n  async exportBookmarks(): Promise<Bookmark[]> {\n    return await this.getAllBookmarks();\n  }\n\n  // 导入书签数据\n  async importBookmarks(bookmarks: Bookmark[], options: { \n    overwrite?: boolean; \n    skipDuplicates?: boolean \n  } = {}): Promise<{ \n    imported: number; \n    skipped: number; \n    errors: string[] \n  }> {\n    const result: { imported: number; skipped: number; errors: string[] } = { imported: 0, skipped: 0, errors: [] };\n    \n    for (const bookmark of bookmarks) {\n      try {\n        // 验证数据\n        const validation = this.validateBookmarkData(bookmark);\n        if (!validation.valid) {\n          result.errors.push(`书签 \"${bookmark.title}\" 验证失败: ${validation.errors.join(', ')}`);\n          continue;\n        }\n\n        // 检查重复\n        if (options.skipDuplicates) {\n          const duplicate = await this.checkDuplicateBookmark(bookmark.url);\n          if (duplicate) {\n            result.skipped++;\n            continue;\n          }\n        }\n\n        // 创建书签\n        await this.createBookmark({\n          title: bookmark.title,\n          url: bookmark.url,\n          description: bookmark.description,\n          shortDesc: bookmark.shortDesc,\n          category: bookmark.category,\n          icon: bookmark.icon,\n          tags: bookmark.tags\n        });\n\n        result.imported++;\n      } catch (error) {\n        result.errors.push(`导入书签 \"${bookmark.title}\" 失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      }\n    }\n\n    return result;\n  }\n}\n", "// CloudNav 2.0 - 分类管理服务\nimport { nanoid } from 'nanoid';\nimport type { Category, Bookmark } from '@/types';\nimport { KVService } from './kv';\n\nexport class CategoryService {\n  private kvService: KVService;\n\n  constructor(kvService: KVService) {\n    this.kvService = kvService;\n  }\n\n  // 获取所有分类\n  async getAllCategories(): Promise<Category[]> {\n    const categories = await this.kvService.getCategories();\n    return categories.sort((a, b) => a.order - b.order);\n  }\n\n  // 根据ID获取分类\n  async getCategoryById(id: string): Promise<Category | null> {\n    const categories = await this.getAllCategories();\n    return categories.find(category => category.id === id) || null;\n  }\n\n  // 创建新分类\n  async createCategory(categoryData: Omit<Category, 'id' | 'createdAt'>): Promise<Category> {\n    const categories = await this.getAllCategories();\n    const maxOrder = categories.length > 0 ? Math.max(...categories.map(c => c.order)) : 0;\n    \n    const newCategory: Category = {\n      id: nanoid(),\n      ...categoryData,\n      order: categoryData.order ?? maxOrder + 1,\n      createdAt: Date.now()\n    };\n\n    const success = await this.kvService.addCategory(newCategory);\n    if (!success) {\n      throw new Error('Failed to create category');\n    }\n\n    return newCategory;\n  }\n\n  // 更新分类\n  async updateCategory(id: string, updates: Partial<Omit<Category, 'id' | 'createdAt'>>): Promise<Category | null> {\n    const category = await this.getCategoryById(id);\n    if (!category) {\n      return null;\n    }\n\n    const success = await this.kvService.updateCategory(id, updates);\n    if (!success) {\n      throw new Error('Failed to update category');\n    }\n\n    return { ...category, ...updates };\n  }\n\n  // 删除分类\n  async deleteCategory(id: string, options: { \n    moveBookmarksTo?: string; \n    deleteBookmarks?: boolean \n  } = {}): Promise<{ success: boolean; movedBookmarks?: number; deletedBookmarks?: number }> {\n    const category = await this.getCategoryById(id);\n    if (!category) {\n      return { success: false };\n    }\n\n    // 获取该分类下的所有书签\n    const bookmarks = await this.kvService.getBookmarks();\n    const categoryBookmarks = bookmarks.filter(bookmark => bookmark.category === id);\n\n    let movedBookmarks = 0;\n    let deletedBookmarks = 0;\n\n    // 处理分类下的书签\n    if (categoryBookmarks.length > 0) {\n      if (options.moveBookmarksTo) {\n        // 移动书签到其他分类\n        for (const bookmark of categoryBookmarks) {\n          const success = await this.kvService.updateBookmark(bookmark.id, {\n            category: options.moveBookmarksTo,\n            updatedAt: Date.now()\n          });\n          if (success) movedBookmarks++;\n        }\n      } else if (options.deleteBookmarks) {\n        // 删除所有书签\n        for (const bookmark of categoryBookmarks) {\n          const success = await this.kvService.deleteBookmark(bookmark.id);\n          if (success) deletedBookmarks++;\n        }\n      } else {\n        // 如果没有指定处理方式，不允许删除\n        throw new Error('Cannot delete category with bookmarks. Please specify how to handle existing bookmarks.');\n      }\n    }\n\n    // 删除分类\n    const success = await this.kvService.deleteCategory(id);\n    \n    return { \n      success, \n      movedBookmarks: movedBookmarks > 0 ? movedBookmarks : undefined,\n      deletedBookmarks: deletedBookmarks > 0 ? deletedBookmarks : undefined\n    };\n  }\n\n  // 重新排序分类\n  async reorderCategories(categoryOrders: { id: string; order: number }[]): Promise<boolean> {\n    try {\n      for (const { id, order } of categoryOrders) {\n        await this.kvService.updateCategory(id, { order });\n      }\n      return true;\n    } catch (error) {\n      console.error('Failed to reorder categories:', error);\n      return false;\n    }\n  }\n\n  // 获取分类统计信息\n  async getCategoryStats(): Promise<Array<{\n    category: Category;\n    bookmarkCount: number;\n    totalClicks: number;\n    lastUpdated: number;\n  }>> {\n    const [categories, bookmarks, stats] = await Promise.all([\n      this.getAllCategories(),\n      this.kvService.getBookmarks(),\n      this.kvService.getStats()\n    ]);\n\n    return categories.map(category => {\n      const categoryBookmarks = bookmarks.filter(bookmark => bookmark.category === category.id);\n      const totalClicks = categoryBookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);\n      const lastUpdated = categoryBookmarks.length > 0 \n        ? Math.max(...categoryBookmarks.map(bookmark => bookmark.updatedAt))\n        : category.createdAt;\n\n      return {\n        category,\n        bookmarkCount: categoryBookmarks.length,\n        totalClicks,\n        lastUpdated\n      };\n    });\n  }\n\n  // 验证分类数据\n  validateCategoryData(data: any): { valid: boolean; errors: string[] } {\n    const errors: string[] = [];\n\n    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {\n      errors.push('分类名称是必需的');\n    }\n\n    if (data.order !== undefined && (typeof data.order !== 'number' || data.order < 0)) {\n      errors.push('排序必须是非负数');\n    }\n\n    return {\n      valid: errors.length === 0,\n      errors\n    };\n  }\n\n  // 检查分类名称是否重复\n  async checkDuplicateCategoryName(name: string, excludeId?: string): Promise<Category | null> {\n    const categories = await this.getAllCategories();\n    return categories.find(category => \n      category.name.toLowerCase() === name.toLowerCase() && category.id !== excludeId\n    ) || null;\n  }\n\n  // 获取空分类（没有书签的分类）\n  async getEmptyCategories(): Promise<Category[]> {\n    const [categories, bookmarks] = await Promise.all([\n      this.getAllCategories(),\n      this.kvService.getBookmarks()\n    ]);\n\n    return categories.filter(category => \n      !bookmarks.some(bookmark => bookmark.category === category.id)\n    );\n  }\n\n  // 获取热门分类\n  async getPopularCategories(limit: number = 10): Promise<Array<{\n    category: Category;\n    bookmarkCount: number;\n    totalClicks: number;\n  }>> {\n    const stats = await this.getCategoryStats();\n    return stats\n      .sort((a, b) => b.totalClicks - a.totalClicks)\n      .slice(0, limit)\n      .map(({ category, bookmarkCount, totalClicks }) => ({\n        category,\n        bookmarkCount,\n        totalClicks\n      }));\n  }\n\n  // 创建默认分类\n  async createDefaultCategories(): Promise<Category[]> {\n    const defaultCategories = [\n      { name: '开发工具', icon: '🛠️', description: '开发相关的工具和资源', order: 1 },\n      { name: '设计资源', icon: '🎨', description: '设计工具和素材', order: 2 },\n      { name: '学习资料', icon: '📚', description: '学习和教育相关资源', order: 3 },\n      { name: '娱乐休闲', icon: '🎮', description: '娱乐和休闲网站', order: 4 },\n      { name: '新闻资讯', icon: '📰', description: '新闻和资讯网站', order: 5 },\n      { name: '社交媒体', icon: '💬', description: '社交网络和通讯工具', order: 6 },\n      { name: '购物网站', icon: '🛒', description: '电商和购物平台', order: 7 },\n      { name: '其他', icon: '📁', description: '其他未分类的网站', order: 8 }\n    ];\n\n    const createdCategories: Category[] = [];\n\n    for (const categoryData of defaultCategories) {\n      try {\n        // 检查是否已存在同名分类\n        const existing = await this.checkDuplicateCategoryName(categoryData.name);\n        if (!existing) {\n          const category = await this.createCategory(categoryData);\n          createdCategories.push(category);\n        }\n      } catch (error) {\n        console.error(`Failed to create default category ${categoryData.name}:`, error);\n      }\n    }\n\n    return createdCategories;\n  }\n\n  // 导出分类数据\n  async exportCategories(): Promise<Category[]> {\n    return await this.getAllCategories();\n  }\n\n  // 导入分类数据\n  async importCategories(categories: Category[], options: { \n    overwrite?: boolean; \n    skipDuplicates?: boolean \n  } = {}): Promise<{ \n    imported: number; \n    skipped: number; \n    errors: string[] \n  }> {\n    const result: { imported: number; skipped: number; errors: string[] } = { imported: 0, skipped: 0, errors: [] };\n    \n    for (const category of categories) {\n      try {\n        // 验证数据\n        const validation = this.validateCategoryData(category);\n        if (!validation.valid) {\n          result.errors.push(`分类 \"${category.name}\" 验证失败: ${validation.errors.join(', ')}`);\n          continue;\n        }\n\n        // 检查重复\n        if (options.skipDuplicates) {\n          const duplicate = await this.checkDuplicateCategoryName(category.name);\n          if (duplicate) {\n            result.skipped++;\n            continue;\n          }\n        }\n\n        // 创建分类\n        await this.createCategory({\n          name: category.name,\n          icon: category.icon,\n          description: category.description,\n          order: category.order\n        });\n\n        result.imported++;\n      } catch (error) {\n        result.errors.push(`导入分类 \"${category.name}\" 失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      }\n    }\n\n    return result;\n  }\n\n  // 合并分类\n  async mergeCategories(sourceId: string, targetId: string): Promise<{ success: boolean; movedBookmarks: number }> {\n    const [sourceCategory, targetCategory] = await Promise.all([\n      this.getCategoryById(sourceId),\n      this.getCategoryById(targetId)\n    ]);\n\n    if (!sourceCategory || !targetCategory) {\n      throw new Error('Source or target category not found');\n    }\n\n    // 移动所有书签到目标分类\n    const bookmarks = await this.kvService.getBookmarks();\n    const sourceBookmarks = bookmarks.filter(bookmark => bookmark.category === sourceId);\n    \n    let movedBookmarks = 0;\n    for (const bookmark of sourceBookmarks) {\n      const success = await this.kvService.updateBookmark(bookmark.id, {\n        category: targetId,\n        updatedAt: Date.now()\n      });\n      if (success) movedBookmarks++;\n    }\n\n    // 删除源分类\n    const deleteSuccess = await this.kvService.deleteCategory(sourceId);\n\n    return {\n      success: deleteSuccess,\n      movedBookmarks\n    };\n  }\n}\n", "// CloudNav 2.0 - 数据迁移服务\nimport type { Bookmark, Category } from '@/types';\nimport { KVService } from './kv';\nimport { BookmarkService } from './bookmarks';\nimport { CategoryService } from './categories';\n\n// 原始数据格式（来自 navLinks.js）\ninterface LegacySite {\n  id: string;\n  title: string;\n  description?: string;\n  shortDesc?: string;\n  url: string;\n  category: string;\n  icon?: string;\n}\n\ninterface LegacyCategory {\n  id: string;\n  name: string;\n  icon?: string;\n}\n\ninterface LegacyData {\n  categories: LegacyCategory[];\n  sites: LegacySite[];\n}\n\nexport class MigrationService {\n  private kvService: KVService;\n  private bookmarkService: BookmarkService;\n  private categoryService: CategoryService;\n\n  constructor(kvService: KVService) {\n    this.kvService = kvService;\n    this.bookmarkService = new BookmarkService(kvService);\n    this.categoryService = new CategoryService(kvService);\n  }\n\n  // 从 navLinks.js 迁移数据\n  async migrateFromNavLinks(legacyData: LegacyData): Promise<{\n    success: boolean;\n    categoriesImported: number;\n    bookmarksImported: number;\n    errors: string[];\n  }> {\n    const result: {\n      success: boolean;\n      categoriesImported: number;\n      bookmarksImported: number;\n      errors: string[];\n    } = {\n      success: false,\n      categoriesImported: 0,\n      bookmarksImported: 0,\n      errors: []\n    };\n\n    try {\n      console.log('开始数据迁移...');\n\n      // 1. 迁移分类\n      console.log('迁移分类数据...');\n      for (let i = 0; i < legacyData.categories.length; i++) {\n        const legacyCategory = legacyData.categories[i];\n        try {\n          const category: Omit<Category, 'id' | 'createdAt'> = {\n            name: legacyCategory.name,\n            icon: legacyCategory.icon,\n            description: `从原系统迁移的分类: ${legacyCategory.name}`,\n            order: i + 1\n          };\n\n          // 检查是否已存在\n          const existing = await this.categoryService.checkDuplicateCategoryName(category.name);\n          if (!existing) {\n            await this.categoryService.createCategory(category);\n            result.categoriesImported++;\n            console.log(`✓ 分类迁移成功: ${category.name}`);\n          } else {\n            console.log(`- 分类已存在，跳过: ${category.name}`);\n          }\n        } catch (error) {\n          const errorMsg = `分类迁移失败 ${legacyCategory.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;\n          result.errors.push(errorMsg);\n          console.error(errorMsg);\n        }\n      }\n\n      // 2. 迁移书签\n      console.log('迁移书签数据...');\n      for (const legacySite of legacyData.sites) {\n        try {\n          const bookmark: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt' | 'clickCount'> = {\n            title: legacySite.title,\n            url: legacySite.url,\n            description: legacySite.description,\n            shortDesc: legacySite.shortDesc,\n            category: legacySite.category,\n            icon: legacySite.icon,\n            tags: []\n          };\n\n          // 验证数据\n          const validation = this.bookmarkService.validateBookmarkData(bookmark);\n          if (!validation.valid) {\n            result.errors.push(`书签验证失败 ${bookmark.title}: ${validation.errors.join(', ')}`);\n            continue;\n          }\n\n          // 检查重复\n          const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmark.url);\n          if (!duplicate) {\n            await this.bookmarkService.createBookmark(bookmark);\n            result.bookmarksImported++;\n            console.log(`✓ 书签迁移成功: ${bookmark.title}`);\n          } else {\n            console.log(`- 书签已存在，跳过: ${bookmark.title}`);\n          }\n        } catch (error) {\n          const errorMsg = `书签迁移失败 ${legacySite.title}: ${error instanceof Error ? error.message : 'Unknown error'}`;\n          result.errors.push(errorMsg);\n          console.error(errorMsg);\n        }\n      }\n\n      result.success = true;\n      console.log(`迁移完成: ${result.categoriesImported} 个分类, ${result.bookmarksImported} 个书签`);\n\n    } catch (error) {\n      result.errors.push(`迁移过程出错: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      console.error('迁移失败:', error);\n    }\n\n    return result;\n  }\n\n  // 从当前项目的 navLinks.js 读取数据\n  async loadNavLinksData(): Promise<LegacyData | null> {\n    try {\n      // 这里需要动态导入原始的 navLinks.js 文件\n      // 由于在 Workers 环境中，我们需要手动解析数据\n      \n      // 示例数据结构，实际使用时需要从文件中读取\n      const sampleData: LegacyData = {\n        categories: [\n          { id: 'opensource', name: '开源项目', icon: '🔓' },\n          { id: 'ai', name: 'AI工具', icon: '🤖' },\n          { id: 'design', name: '设计工具', icon: '🎨' },\n          { id: 'dev', name: '开发工具', icon: '⚡' },\n          { id: 'learn', name: '学习资源', icon: '📚' },\n          { id: 'life', name: '生活服务', icon: '🏠' }\n        ],\n        sites: [\n          {\n            id: 'github',\n            title: 'GitHub',\n            description: '全球最大的代码托管平台',\n            shortDesc: '代码托管',\n            url: 'https://github.com',\n            category: 'opensource',\n            icon: '/icons/github.webp'\n          },\n          {\n            id: 'chatgpt',\n            title: 'ChatGPT',\n            description: 'OpenAI开发的AI聊天机器人',\n            shortDesc: 'AI聊天',\n            url: 'https://chat.openai.com',\n            category: 'ai',\n            icon: '/icons/chatgpt.webp'\n          }\n        ]\n      };\n\n      return sampleData;\n    } catch (error) {\n      console.error('读取 navLinks 数据失败:', error);\n      return null;\n    }\n  }\n\n  // 执行完整迁移流程\n  async performMigration(): Promise<{\n    success: boolean;\n    message: string;\n    details: {\n      categoriesImported: number;\n      bookmarksImported: number;\n      errors: string[];\n    };\n  }> {\n    try {\n      // 1. 检查是否已有数据\n      const [existingBookmarks, existingCategories] = await Promise.all([\n        this.bookmarkService.getAllBookmarks(),\n        this.categoryService.getAllCategories()\n      ]);\n\n      if (existingBookmarks.length > 0 || existingCategories.length > 0) {\n        return {\n          success: false,\n          message: '系统中已存在数据，请先清空数据或选择强制迁移',\n          details: { categoriesImported: 0, bookmarksImported: 0, errors: [] }\n        };\n      }\n\n      // 2. 读取原始数据\n      const legacyData = await this.loadNavLinksData();\n      if (!legacyData) {\n        return {\n          success: false,\n          message: '无法读取原始数据文件',\n          details: { categoriesImported: 0, bookmarksImported: 0, errors: ['数据文件读取失败'] }\n        };\n      }\n\n      // 3. 执行迁移\n      const migrationResult = await this.migrateFromNavLinks(legacyData);\n\n      return {\n        success: migrationResult.success,\n        message: migrationResult.success \n          ? `迁移成功完成！导入了 ${migrationResult.categoriesImported} 个分类和 ${migrationResult.bookmarksImported} 个书签`\n          : '迁移过程中出现错误',\n        details: {\n          categoriesImported: migrationResult.categoriesImported,\n          bookmarksImported: migrationResult.bookmarksImported,\n          errors: migrationResult.errors\n        }\n      };\n\n    } catch (error) {\n      return {\n        success: false,\n        message: '迁移过程中发生未知错误',\n        details: {\n          categoriesImported: 0,\n          bookmarksImported: 0,\n          errors: [error instanceof Error ? error.message : 'Unknown error']\n        }\n      };\n    }\n  }\n\n  // 清空所有数据\n  async clearAllData(): Promise<boolean> {\n    try {\n      const [bookmarks, categories] = await Promise.all([\n        this.bookmarkService.getAllBookmarks(),\n        this.categoryService.getAllCategories()\n      ]);\n\n      // 删除所有书签\n      for (const bookmark of bookmarks) {\n        await this.bookmarkService.deleteBookmark(bookmark.id);\n      }\n\n      // 删除所有分类\n      for (const category of categories) {\n        await this.categoryService.deleteCategory(category.id, { deleteBookmarks: true });\n      }\n\n      // 清理统计数据\n      await this.kvService.updateStats({\n        totalClicks: 0,\n        totalViews: 0,\n        bookmarkStats: {},\n        categoryStats: {},\n        searchStats: {},\n        dailyStats: {},\n        deviceStats: { mobile: 0, desktop: 0, tablet: 0 },\n        lastUpdated: Date.now()\n      });\n\n      return true;\n    } catch (error) {\n      console.error('清空数据失败:', error);\n      return false;\n    }\n  }\n\n  // 创建示例数据\n  async createSampleData(): Promise<{\n    success: boolean;\n    categoriesCreated: number;\n    bookmarksCreated: number;\n    errors: string[];\n  }> {\n    const result: {\n      success: boolean;\n      categoriesCreated: number;\n      bookmarksCreated: number;\n      errors: string[];\n    } = {\n      success: false,\n      categoriesCreated: 0,\n      bookmarksCreated: 0,\n      errors: []\n    };\n\n    try {\n      // 创建默认分类\n      const defaultCategories = await this.categoryService.createDefaultCategories();\n      result.categoriesCreated = defaultCategories.length;\n\n      // 创建示例书签\n      const sampleBookmarks = [\n        {\n          title: 'GitHub',\n          url: 'https://github.com',\n          description: '全球最大的代码托管平台，开发者的必备工具',\n          shortDesc: '代码托管平台',\n          category: defaultCategories.find(c => c.name === '开发工具')?.id || 'dev',\n          icon: 'https://github.com/favicon.ico',\n          tags: ['代码', '开源', 'Git']\n        },\n        {\n          title: 'Figma',\n          url: 'https://figma.com',\n          description: '现代化的界面设计工具，支持团队协作',\n          shortDesc: '界面设计工具',\n          category: defaultCategories.find(c => c.name === '设计资源')?.id || 'design',\n          icon: 'https://figma.com/favicon.ico',\n          tags: ['设计', 'UI', '协作']\n        },\n        {\n          title: 'MDN Web Docs',\n          url: 'https://developer.mozilla.org',\n          description: 'Web开发者的权威参考文档',\n          shortDesc: 'Web开发文档',\n          category: defaultCategories.find(c => c.name === '学习资料')?.id || 'learn',\n          icon: 'https://developer.mozilla.org/favicon.ico',\n          tags: ['文档', 'Web', '学习']\n        }\n      ];\n\n      for (const bookmarkData of sampleBookmarks) {\n        try {\n          await this.bookmarkService.createBookmark(bookmarkData);\n          result.bookmarksCreated++;\n        } catch (error) {\n          result.errors.push(`创建示例书签失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n      }\n\n      result.success = true;\n    } catch (error) {\n      result.errors.push(`创建示例数据失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n\n    return result;\n  }\n\n  // 获取迁移状态\n  async getMigrationStatus(): Promise<{\n    hasData: boolean;\n    bookmarkCount: number;\n    categoryCount: number;\n    lastMigration?: number;\n  }> {\n    const [bookmarks, categories, config] = await Promise.all([\n      this.bookmarkService.getAllBookmarks(),\n      this.categoryService.getAllCategories(),\n      this.kvService.getConfig()\n    ]);\n\n    return {\n      hasData: bookmarks.length > 0 || categories.length > 0,\n      bookmarkCount: bookmarks.length,\n      categoryCount: categories.length,\n      lastMigration: undefined // 可以从配置中读取上次迁移时间\n    };\n  }\n}\n", "// CloudNav 2.0 - Chrome 书签导入导出服务\nimport type { Bookmark, Category, ChromeBookmark, ChromeBookmarkRoot, ImportResult } from '@/types';\nimport { BookmarkService } from './bookmarks';\nimport { CategoryService } from './categories';\n\nexport class ChromeBookmarkService {\n  private bookmarkService: BookmarkService;\n  private categoryService: CategoryService;\n\n  constructor(bookmarkService: BookmarkService, categoryService: CategoryService) {\n    this.bookmarkService = bookmarkService;\n    this.categoryService = categoryService;\n  }\n\n  // 解析 Chrome 书签 JSON 文件\n  parseChromeBoomarks(jsonContent: string): ChromeBookmarkRoot | null {\n    try {\n      const data = JSON.parse(jsonContent);\n\n      // 验证 Chrome 书签格式\n      if (!data.roots || !data.roots.bookmark_bar || !data.roots.other) {\n        throw new Error('Invalid Chrome bookmarks format');\n      }\n\n      return data as ChromeBookmarkRoot;\n    } catch (error) {\n      console.error('Failed to parse Chrome bookmarks:', error);\n      return null;\n    }\n  }\n\n  // 解析 HTML 书签文件（Netscape 格式）\n  parseHtmlBookmarks(htmlContent: string): { bookmarks: any[], folders: string[] } | null {\n    try {\n      const bookmarks: any[] = [];\n      const folders: string[] = [];\n\n      // 简单的 HTML 解析，提取书签和文件夹\n      const lines = htmlContent.split('\\n');\n      let currentFolder = '';\n      let folderStack: string[] = [];\n\n      for (const line of lines) {\n        const trimmedLine = line.trim();\n\n        // 检测文件夹开始\n        if (trimmedLine.includes('<DT><H3')) {\n          const folderMatch = trimmedLine.match(/>([^<]+)</);\n          if (folderMatch) {\n            currentFolder = folderMatch[1];\n            folderStack.push(currentFolder);\n            folders.push(currentFolder);\n          }\n        }\n\n        // 检测文件夹结束\n        else if (trimmedLine.includes('</DL>')) {\n          folderStack.pop();\n          currentFolder = folderStack[folderStack.length - 1] || '';\n        }\n\n        // 检测书签\n        else if (trimmedLine.includes('<DT><A HREF=')) {\n          const urlMatch = trimmedLine.match(/HREF=\"([^\"]+)\"/);\n          const titleMatch = trimmedLine.match(/>([^<]+)</);\n          const addDateMatch = trimmedLine.match(/ADD_DATE=\"([^\"]+)\"/);\n          const iconMatch = trimmedLine.match(/ICON=\"([^\"]+)\"/);\n\n          if (urlMatch && titleMatch) {\n            const url = urlMatch[1];\n            const title = titleMatch[1];\n            const addDate = addDateMatch ? parseInt(addDateMatch[1]) * 1000 : Date.now();\n            const icon = iconMatch ? iconMatch[1] : undefined;\n\n            bookmarks.push({\n              title: title,\n              url: url,\n              description: `从 HTML 书签导入: ${title}`,\n              shortDesc: title,\n              category: currentFolder || 'imported',\n              tags: currentFolder ? [currentFolder] : ['imported'],\n              icon: icon,\n              dateAdded: addDate,\n              dateModified: addDate\n            });\n          }\n        }\n      }\n\n      return { bookmarks, folders };\n    } catch (error) {\n      console.error('Failed to parse HTML bookmarks:', error);\n      return null;\n    }\n  }\n\n  // 从 Chrome 书签节点提取书签和文件夹\n  private extractBookmarksFromNode(\n    node: ChromeBookmark, \n    parentPath: string = ''\n  ): { bookmarks: any[], folders: string[] } {\n    const bookmarks: any[] = [];\n    const folders: string[] = [];\n\n    if (node.type === 'url' && node.url) {\n      // 这是一个书签\n      bookmarks.push({\n        title: node.name,\n        url: node.url,\n        description: `从 Chrome 导入的书签`,\n        shortDesc: node.name,\n        category: parentPath || 'imported',\n        tags: parentPath ? [parentPath] : ['imported'],\n        dateAdded: node.date_added ? parseInt(node.date_added) : Date.now(),\n        dateModified: node.date_modified ? parseInt(node.date_modified) : Date.now()\n      });\n    } else if (node.type === 'folder' && node.children) {\n      // 这是一个文件夹\n      const folderName = node.name;\n      const folderPath = parentPath ? `${parentPath}/${folderName}` : folderName;\n      \n      folders.push(folderName);\n\n      // 递归处理子节点\n      for (const child of node.children) {\n        const childResult = this.extractBookmarksFromNode(child, folderPath);\n        bookmarks.push(...childResult.bookmarks);\n        folders.push(...childResult.folders);\n      }\n    }\n\n    return { bookmarks, folders };\n  }\n\n  // 通用书签导入（自动检测格式）\n  async importBookmarks(\n    content: string,\n    options: {\n      createCategories?: boolean;\n      skipDuplicates?: boolean;\n      defaultCategory?: string;\n      format?: 'auto' | 'json' | 'html';\n    } = {}\n  ): Promise<ImportResult> {\n    const format = options.format || this.detectFileFormat(content);\n\n    if (format === 'json') {\n      return this.importChromeBookmarks(content, options);\n    } else if (format === 'html') {\n      return this.importHtmlBookmarks(content, options);\n    } else {\n      return {\n        success: false,\n        imported: 0,\n        skipped: 0,\n        errors: ['无法识别的文件格式，请确保是 Chrome JSON 或 HTML 书签文件']\n      };\n    }\n  }\n\n  // 检测文件格式\n  private detectFileFormat(content: string): 'json' | 'html' | 'unknown' {\n    const trimmedContent = content.trim();\n\n    // 检测 JSON 格式\n    if (trimmedContent.startsWith('{') && trimmedContent.includes('\"roots\"')) {\n      return 'json';\n    }\n\n    // 检测 HTML 格式\n    if (trimmedContent.includes('<!DOCTYPE NETSCAPE-Bookmark-file-1>') ||\n        trimmedContent.includes('<DT><A HREF=') ||\n        trimmedContent.includes('<H3>')) {\n      return 'html';\n    }\n\n    return 'unknown';\n  }\n\n  // 导入 Chrome JSON 书签\n  async importChromeBookmarks(\n    jsonContent: string,\n    options: {\n      createCategories?: boolean;\n      skipDuplicates?: boolean;\n      defaultCategory?: string;\n    } = {}\n  ): Promise<ImportResult> {\n    const result: ImportResult = {\n      success: false,\n      imported: 0,\n      skipped: 0,\n      errors: []\n    };\n\n    try {\n      // 解析 Chrome 书签文件\n      const chromeData = this.parseChromeBoomarks(jsonContent);\n      if (!chromeData) {\n        result.errors.push('无法解析 Chrome 书签文件');\n        return result;\n      }\n\n      // 提取所有书签和文件夹\n      const allBookmarks: any[] = [];\n      const allFolders: string[] = [];\n\n      // 处理书签栏\n      if (chromeData.roots.bookmark_bar) {\n        const barResult = this.extractBookmarksFromNode(chromeData.roots.bookmark_bar, '书签栏');\n        allBookmarks.push(...barResult.bookmarks);\n        allFolders.push(...barResult.folders);\n      }\n\n      // 处理其他书签\n      if (chromeData.roots.other) {\n        const otherResult = this.extractBookmarksFromNode(chromeData.roots.other, '其他书签');\n        allBookmarks.push(...otherResult.bookmarks);\n        allFolders.push(...otherResult.folders);\n      }\n\n      // 处理同步书签（如果存在）\n      if (chromeData.roots.synced) {\n        const syncedResult = this.extractBookmarksFromNode(chromeData.roots.synced, '同步书签');\n        allBookmarks.push(...syncedResult.bookmarks);\n        allFolders.push(...syncedResult.folders);\n      }\n\n      // 创建分类（如果启用）\n      const categoryMap: Record<string, string> = {};\n      if (options.createCategories) {\n        const uniqueFolders = [...new Set(allFolders)];\n        \n        for (const folderName of uniqueFolders) {\n          try {\n            // 检查分类是否已存在\n            const existingCategory = await this.categoryService.checkDuplicateCategoryName(folderName);\n            \n            if (!existingCategory) {\n              const category = await this.categoryService.createCategory({\n                name: folderName,\n                description: `从 Chrome 导入的分类: ${folderName}`,\n                order: 999 // 放在最后\n              });\n              categoryMap[folderName] = category.id;\n            } else {\n              categoryMap[folderName] = existingCategory.id;\n            }\n          } catch (error) {\n            result.errors.push(`创建分类失败: ${folderName}`);\n          }\n        }\n      }\n\n      // 获取现有分类\n      const existingCategories = await this.categoryService.getAllCategories();\n      const defaultCategoryId = options.defaultCategory || \n        existingCategories.find(c => c.name === '导入书签')?.id ||\n        existingCategories[0]?.id;\n\n      // 导入书签\n      for (const bookmarkData of allBookmarks) {\n        try {\n          // 确定分类\n          let categoryId = defaultCategoryId;\n          if (options.createCategories && bookmarkData.category) {\n            categoryId = categoryMap[bookmarkData.category] || defaultCategoryId;\n          }\n\n          // 验证数据\n          const validation = this.bookmarkService.validateBookmarkData({\n            ...bookmarkData,\n            category: categoryId\n          });\n\n          if (!validation.valid) {\n            result.errors.push(`书签验证失败 \"${bookmarkData.title}\": ${validation.errors.join(', ')}`);\n            continue;\n          }\n\n          // 检查重复\n          if (options.skipDuplicates) {\n            const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmarkData.url);\n            if (duplicate) {\n              result.skipped++;\n              continue;\n            }\n          }\n\n          // 创建书签\n          await this.bookmarkService.createBookmark({\n            title: bookmarkData.title,\n            url: bookmarkData.url,\n            description: bookmarkData.description,\n            shortDesc: bookmarkData.shortDesc,\n            category: categoryId,\n            tags: bookmarkData.tags\n          });\n\n          result.imported++;\n        } catch (error) {\n          result.errors.push(`导入书签失败 \"${bookmarkData.title}\": ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n      }\n\n      result.success = true;\n      console.log(`Chrome 书签导入完成: ${result.imported} 个书签, ${result.skipped} 个跳过, ${result.errors.length} 个错误`);\n\n    } catch (error) {\n      result.errors.push(`导入过程出错: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      console.error('Chrome bookmarks import failed:', error);\n    }\n\n    return result;\n  }\n\n  // 导入 HTML 书签\n  async importHtmlBookmarks(\n    htmlContent: string,\n    options: {\n      createCategories?: boolean;\n      skipDuplicates?: boolean;\n      defaultCategory?: string;\n    } = {}\n  ): Promise<ImportResult> {\n    const result: ImportResult = {\n      success: false,\n      imported: 0,\n      skipped: 0,\n      errors: []\n    };\n\n    try {\n      // 解析 HTML 书签文件\n      const htmlData = this.parseHtmlBookmarks(htmlContent);\n      if (!htmlData) {\n        result.errors.push('无法解析 HTML 书签文件');\n        return result;\n      }\n\n      const { bookmarks: allBookmarks, folders: allFolders } = htmlData;\n\n      // 创建分类（如果启用）\n      const categoryMap: Record<string, string> = {};\n      if (options.createCategories) {\n        const uniqueFolders = [...new Set(allFolders)];\n\n        for (const folderName of uniqueFolders) {\n          try {\n            // 检查分类是否已存在\n            const existingCategory = await this.categoryService.checkDuplicateCategoryName(folderName);\n\n            if (!existingCategory) {\n              const category = await this.categoryService.createCategory({\n                name: folderName,\n                description: `从 HTML 书签导入的分类: ${folderName}`,\n                order: 999 // 放在最后\n              });\n              categoryMap[folderName] = category.id;\n            } else {\n              categoryMap[folderName] = existingCategory.id;\n            }\n          } catch (error) {\n            result.errors.push(`创建分类失败: ${folderName}`);\n          }\n        }\n      }\n\n      // 获取现有分类\n      const existingCategories = await this.categoryService.getAllCategories();\n      const defaultCategoryId = options.defaultCategory ||\n        existingCategories.find(c => c.name === '导入书签')?.id ||\n        existingCategories[0]?.id;\n\n      // 导入书签\n      for (const bookmarkData of allBookmarks) {\n        try {\n          // 确定分类\n          let categoryId = defaultCategoryId;\n          if (options.createCategories && bookmarkData.category) {\n            categoryId = categoryMap[bookmarkData.category] || defaultCategoryId;\n          }\n\n          // 验证数据\n          const validation = this.bookmarkService.validateBookmarkData({\n            ...bookmarkData,\n            category: categoryId\n          });\n\n          if (!validation.valid) {\n            result.errors.push(`书签验证失败 \"${bookmarkData.title}\": ${validation.errors.join(', ')}`);\n            continue;\n          }\n\n          // 检查重复\n          if (options.skipDuplicates) {\n            const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmarkData.url);\n            if (duplicate) {\n              result.skipped++;\n              continue;\n            }\n          }\n\n          // 创建书签\n          await this.bookmarkService.createBookmark({\n            title: bookmarkData.title,\n            url: bookmarkData.url,\n            description: bookmarkData.description,\n            shortDesc: bookmarkData.shortDesc,\n            category: categoryId,\n            icon: bookmarkData.icon,\n            tags: bookmarkData.tags\n          });\n\n          result.imported++;\n        } catch (error) {\n          result.errors.push(`导入书签失败 \"${bookmarkData.title}\": ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n      }\n\n      result.success = true;\n      console.log(`HTML 书签导入完成: ${result.imported} 个书签, ${result.skipped} 个跳过, ${result.errors.length} 个错误`);\n\n    } catch (error) {\n      result.errors.push(`导入过程出错: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      console.error('HTML bookmarks import failed:', error);\n    }\n\n    return result;\n  }\n\n  // 导出为 Chrome 书签格式\n  async exportToChromeFormat(): Promise<string> {\n    try {\n      const [bookmarks, categories] = await Promise.all([\n        this.bookmarkService.getAllBookmarks(),\n        this.categoryService.getAllCategories()\n      ]);\n\n      // 按分类分组书签\n      const bookmarksByCategory: Record<string, Bookmark[]> = {};\n      bookmarks.forEach(bookmark => {\n        if (!bookmarksByCategory[bookmark.category]) {\n          bookmarksByCategory[bookmark.category] = [];\n        }\n        bookmarksByCategory[bookmark.category].push(bookmark);\n      });\n\n      // 创建 Chrome 书签结构\n      const chromeBookmarks: ChromeBookmarkRoot = {\n        checksum: this.generateChecksum(),\n        roots: {\n          bookmark_bar: {\n            date_added: Date.now().toString(),\n            date_modified: Date.now().toString(),\n            guid: this.generateGuid(),\n            id: '1',\n            name: 'CloudNav 书签',\n            type: 'folder',\n            children: []\n          },\n          other: {\n            date_added: Date.now().toString(),\n            date_modified: Date.now().toString(),\n            guid: this.generateGuid(),\n            id: '2',\n            name: '其他书签',\n            type: 'folder',\n            children: []\n          },\n          synced: {\n            date_added: Date.now().toString(),\n            date_modified: Date.now().toString(),\n            guid: this.generateGuid(),\n            id: '3',\n            name: '移动设备书签',\n            type: 'folder',\n            children: []\n          }\n        },\n        version: 1\n      };\n\n      let idCounter = 4;\n\n      // 为每个分类创建文件夹\n      categories.forEach(category => {\n        const categoryBookmarks = bookmarksByCategory[category.id] || [];\n        \n        if (categoryBookmarks.length > 0) {\n          const folderNode: ChromeBookmark = {\n            date_added: category.createdAt.toString(),\n            date_modified: Date.now().toString(),\n            guid: this.generateGuid(),\n            id: (idCounter++).toString(),\n            name: category.name,\n            type: 'folder',\n            children: categoryBookmarks.map(bookmark => ({\n              date_added: bookmark.createdAt.toString(),\n              date_modified: bookmark.updatedAt.toString(),\n              guid: this.generateGuid(),\n              id: (idCounter++).toString(),\n              name: bookmark.title,\n              type: 'url',\n              url: bookmark.url\n            }))\n          };\n\n          chromeBookmarks.roots.bookmark_bar.children!.push(folderNode);\n        }\n      });\n\n      return JSON.stringify(chromeBookmarks, null, 2);\n    } catch (error) {\n      console.error('Export to Chrome format failed:', error);\n      throw new Error('导出 Chrome 书签格式失败');\n    }\n  }\n\n  // 导出为 Netscape 书签格式（通用格式）\n  async exportToNetscapeFormat(): Promise<string> {\n    try {\n      const [bookmarks, categories] = await Promise.all([\n        this.bookmarkService.getAllBookmarks(),\n        this.categoryService.getAllCategories()\n      ]);\n\n      let html = `<!DOCTYPE NETSCAPE-Bookmark-file-1>\n<!-- This is an automatically generated file.\n     It will be read and overwritten.\n     DO NOT EDIT! -->\n<META HTTP-EQUIV=\"Content-Type\" CONTENT=\"text/html; charset=UTF-8\">\n<TITLE>CloudNav 书签</TITLE>\n<H1>CloudNav 书签</H1>\n<DL><p>\n`;\n\n      // 按分类分组书签\n      const bookmarksByCategory: Record<string, Bookmark[]> = {};\n      bookmarks.forEach(bookmark => {\n        if (!bookmarksByCategory[bookmark.category]) {\n          bookmarksByCategory[bookmark.category] = [];\n        }\n        bookmarksByCategory[bookmark.category].push(bookmark);\n      });\n\n      // 为每个分类生成 HTML\n      categories.forEach(category => {\n        const categoryBookmarks = bookmarksByCategory[category.id] || [];\n        \n        if (categoryBookmarks.length > 0) {\n          html += `    <DT><H3 ADD_DATE=\"${Math.floor(category.createdAt / 1000)}\" LAST_MODIFIED=\"${Math.floor(Date.now() / 1000)}\">${this.escapeHtml(category.name)}</H3>\\n`;\n          html += `    <DL><p>\\n`;\n          \n          categoryBookmarks.forEach(bookmark => {\n            const addDate = Math.floor(bookmark.createdAt / 1000);\n            const lastModified = Math.floor(bookmark.updatedAt / 1000);\n            html += `        <DT><A HREF=\"${this.escapeHtml(bookmark.url)}\" ADD_DATE=\"${addDate}\" LAST_MODIFIED=\"${lastModified}\"`;\n            if (bookmark.icon) {\n              html += ` ICON=\"${this.escapeHtml(bookmark.icon)}\"`;\n            }\n            html += `>${this.escapeHtml(bookmark.title)}</A>\\n`;\n            if (bookmark.description) {\n              html += `        <DD>${this.escapeHtml(bookmark.description)}\\n`;\n            }\n          });\n          \n          html += `    </DL><p>\\n`;\n        }\n      });\n\n      html += `</DL><p>\\n`;\n      \n      return html;\n    } catch (error) {\n      console.error('Export to Netscape format failed:', error);\n      throw new Error('导出 Netscape 书签格式失败');\n    }\n  }\n\n  // 生成校验和\n  private generateChecksum(): string {\n    return Math.random().toString(36).substring(2, 15);\n  }\n\n  // 生成 GUID\n  private generateGuid(): string {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0;\n      const v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  }\n\n  // HTML 转义（Workers 环境兼容）\n  private escapeHtml(text: string): string {\n    return text\n      .replace(/&/g, '&amp;')\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;')\n      .replace(/\"/g, '&quot;')\n      .replace(/'/g, '&#39;');\n  }\n\n  // 获取导入统计信息\n  async getImportStats(): Promise<{\n    totalImported: number;\n    lastImportDate?: number;\n    importSources: string[];\n  }> {\n    try {\n      const bookmarks = await this.bookmarkService.getAllBookmarks();\n      const importedBookmarks = bookmarks.filter(b => \n        b.tags?.includes('imported') || \n        b.description?.includes('从 Chrome 导入')\n      );\n\n      return {\n        totalImported: importedBookmarks.length,\n        lastImportDate: importedBookmarks.length > 0 \n          ? Math.max(...importedBookmarks.map(b => b.createdAt))\n          : undefined,\n        importSources: ['Chrome', 'Netscape', 'Manual']\n      };\n    } catch (error) {\n      console.error('Get import stats failed:', error);\n      return {\n        totalImported: 0,\n        importSources: []\n      };\n    }\n  }\n}\n", "// CloudNav 2.0 - AI 智能整理服务\nimport type { Bookmark, Category, AIConfig } from '@/types';\nimport { BookmarkService } from './bookmarks';\nimport { CategoryService } from './categories';\n\nexport interface AIAnalysisResult {\n  suggestions: {\n    categoryRecommendations: Array<{\n      bookmarkId: string;\n      currentCategory: string;\n      suggestedCategory: string;\n      confidence: number;\n      reason: string;\n    }>;\n    duplicateDetection: Array<{\n      bookmarks: Bookmark[];\n      similarity: number;\n      reason: string;\n    }>;\n    descriptionSuggestions: Array<{\n      bookmarkId: string;\n      currentDescription: string;\n      suggestedDescription: string;\n      confidence: number;\n    }>;\n    tagSuggestions: Array<{\n      bookmarkId: string;\n      suggestedTags: string[];\n      confidence: number;\n    }>;\n    newCategorySuggestions: Array<{\n      name: string;\n      description: string;\n      bookmarkIds: string[];\n      confidence: number;\n    }>;\n  };\n  statistics: {\n    totalAnalyzed: number;\n    categorySuggestions: number;\n    duplicatesFound: number;\n    descriptionSuggestions: number;\n    tagSuggestions: number;\n  };\n}\n\nexport interface AIOrganizeOptions {\n  enableCategoryRecommendations: boolean;\n  enableDuplicateDetection: boolean;\n  enableDescriptionGeneration: boolean;\n  enableTagSuggestions: boolean;\n  enableNewCategoryCreation: boolean;\n  confidenceThreshold: number; // 0-1\n  autoApply: boolean;\n}\n\nexport class AIService {\n  private bookmarkService: BookmarkService;\n  private categoryService: CategoryService;\n  private aiConfig: AIConfig;\n\n  constructor(\n    bookmarkService: BookmarkService, \n    categoryService: CategoryService,\n    aiConfig: AIConfig\n  ) {\n    this.bookmarkService = bookmarkService;\n    this.categoryService = categoryService;\n    this.aiConfig = aiConfig;\n  }\n\n  // 智能分析书签\n  async analyzeBookmarks(options: AIOrganizeOptions): Promise<AIAnalysisResult> {\n    const [bookmarks, categories] = await Promise.all([\n      this.bookmarkService.getAllBookmarks(),\n      this.categoryService.getAllCategories()\n    ]);\n\n    const result: AIAnalysisResult = {\n      suggestions: {\n        categoryRecommendations: [],\n        duplicateDetection: [],\n        descriptionSuggestions: [],\n        tagSuggestions: [],\n        newCategorySuggestions: []\n      },\n      statistics: {\n        totalAnalyzed: bookmarks.length,\n        categorySuggestions: 0,\n        duplicatesFound: 0,\n        descriptionSuggestions: 0,\n        tagSuggestions: 0\n      }\n    };\n\n    // 分类推荐\n    if (options.enableCategoryRecommendations) {\n      result.suggestions.categoryRecommendations = await this.analyzeCategoryRecommendations(\n        bookmarks, \n        categories, \n        options.confidenceThreshold\n      );\n      result.statistics.categorySuggestions = result.suggestions.categoryRecommendations.length;\n    }\n\n    // 重复检测\n    if (options.enableDuplicateDetection) {\n      result.suggestions.duplicateDetection = await this.detectDuplicates(\n        bookmarks, \n        options.confidenceThreshold\n      );\n      result.statistics.duplicatesFound = result.suggestions.duplicateDetection.length;\n    }\n\n    // 描述生成\n    if (options.enableDescriptionGeneration) {\n      result.suggestions.descriptionSuggestions = await this.generateDescriptions(\n        bookmarks, \n        options.confidenceThreshold\n      );\n      result.statistics.descriptionSuggestions = result.suggestions.descriptionSuggestions.length;\n    }\n\n    // 标签建议\n    if (options.enableTagSuggestions) {\n      result.suggestions.tagSuggestions = await this.suggestTags(\n        bookmarks, \n        options.confidenceThreshold\n      );\n      result.statistics.tagSuggestions = result.suggestions.tagSuggestions.length;\n    }\n\n    // 新分类建议\n    if (options.enableNewCategoryCreation) {\n      result.suggestions.newCategorySuggestions = await this.suggestNewCategories(\n        bookmarks, \n        categories, \n        options.confidenceThreshold\n      );\n    }\n\n    return result;\n  }\n\n  // 分析分类推荐\n  private async analyzeCategoryRecommendations(\n    bookmarks: Bookmark[], \n    categories: Category[], \n    threshold: number\n  ) {\n    const recommendations: any[] = [];\n\n    for (const bookmark of bookmarks) {\n      const suggestion = this.suggestCategoryForBookmark(bookmark, categories);\n      \n      if (suggestion && suggestion.confidence >= threshold && \n          suggestion.suggestedCategory !== bookmark.category) {\n        recommendations.push({\n          bookmarkId: bookmark.id,\n          currentCategory: bookmark.category,\n          suggestedCategory: suggestion.suggestedCategory,\n          confidence: suggestion.confidence,\n          reason: suggestion.reason\n        });\n      }\n    }\n\n    return recommendations;\n  }\n\n  // 为单个书签建议分类\n  private suggestCategoryForBookmark(bookmark: Bookmark, categories: Category[]) {\n    const url = bookmark.url.toLowerCase();\n    const title = bookmark.title.toLowerCase();\n    const description = (bookmark.description || '').toLowerCase();\n    \n    // 基于 URL 域名的分类规则\n    const domainRules = [\n      { domains: ['github.com', 'gitlab.com', 'bitbucket.org'], category: '开发工具', confidence: 0.9 },\n      { domains: ['stackoverflow.com', 'stackexchange.com'], category: '技术问答', confidence: 0.9 },\n      { domains: ['youtube.com', 'youtu.be'], category: '视频', confidence: 0.9 },\n      { domains: ['twitter.com', 'facebook.com', 'instagram.com'], category: '社交媒体', confidence: 0.8 },\n      { domains: ['amazon.com', 'taobao.com', 'jd.com'], category: '购物', confidence: 0.8 },\n      { domains: ['news.', 'bbc.com', 'cnn.com'], category: '新闻', confidence: 0.8 },\n      { domains: ['wikipedia.org'], category: '百科', confidence: 0.9 },\n      { domains: ['docs.', 'documentation'], category: '文档', confidence: 0.8 }\n    ];\n\n    // 基于关键词的分类规则\n    const keywordRules = [\n      { keywords: ['api', 'documentation', 'docs'], category: '文档', confidence: 0.7 },\n      { keywords: ['tutorial', 'guide', 'how to'], category: '教程', confidence: 0.7 },\n      { keywords: ['tool', 'utility', 'generator'], category: '工具', confidence: 0.7 },\n      { keywords: ['blog', 'article'], category: '博客', confidence: 0.6 },\n      { keywords: ['design', 'ui', 'ux'], category: '设计', confidence: 0.7 },\n      { keywords: ['game', 'gaming'], category: '游戏', confidence: 0.8 }\n    ];\n\n    let bestMatch = null;\n    let maxConfidence = 0;\n\n    // 检查域名规则\n    for (const rule of domainRules) {\n      for (const domain of rule.domains) {\n        if (url.includes(domain)) {\n          const category = categories.find(c => c.name.includes(rule.category));\n          if (category && rule.confidence > maxConfidence) {\n            bestMatch = {\n              suggestedCategory: category.id,\n              confidence: rule.confidence,\n              reason: `基于域名 ${domain} 的智能分类`\n            };\n            maxConfidence = rule.confidence;\n          }\n        }\n      }\n    }\n\n    // 检查关键词规则\n    for (const rule of keywordRules) {\n      for (const keyword of rule.keywords) {\n        if (title.includes(keyword) || description.includes(keyword)) {\n          const category = categories.find(c => c.name.includes(rule.category));\n          if (category && rule.confidence > maxConfidence) {\n            bestMatch = {\n              suggestedCategory: category.id,\n              confidence: rule.confidence,\n              reason: `基于关键词 \"${keyword}\" 的智能分类`\n            };\n            maxConfidence = rule.confidence;\n          }\n        }\n      }\n    }\n\n    return bestMatch;\n  }\n\n  // 检测重复书签\n  private async detectDuplicates(bookmarks: Bookmark[], threshold: number) {\n    const duplicates: any[] = [];\n    const processed = new Set<string>();\n\n    for (let i = 0; i < bookmarks.length; i++) {\n      if (processed.has(bookmarks[i].id)) continue;\n\n      const similarBookmarks = [bookmarks[i]];\n      \n      for (let j = i + 1; j < bookmarks.length; j++) {\n        if (processed.has(bookmarks[j].id)) continue;\n\n        const similarity = this.calculateSimilarity(bookmarks[i], bookmarks[j]);\n        \n        if (similarity >= threshold) {\n          similarBookmarks.push(bookmarks[j]);\n          processed.add(bookmarks[j].id);\n        }\n      }\n\n      if (similarBookmarks.length > 1) {\n        duplicates.push({\n          bookmarks: similarBookmarks,\n          similarity: this.calculateGroupSimilarity(similarBookmarks),\n          reason: this.getDuplicateReason(similarBookmarks)\n        });\n        \n        similarBookmarks.forEach(b => processed.add(b.id));\n      }\n    }\n\n    return duplicates;\n  }\n\n  // 计算两个书签的相似度\n  private calculateSimilarity(bookmark1: Bookmark, bookmark2: Bookmark): number {\n    // URL 完全相同\n    if (bookmark1.url === bookmark2.url) {\n      return 1.0;\n    }\n\n    // 标题相似度\n    const titleSimilarity = this.calculateTextSimilarity(bookmark1.title, bookmark2.title);\n    \n    // URL 相似度（去除协议和参数）\n    const url1Clean = this.cleanUrl(bookmark1.url);\n    const url2Clean = this.cleanUrl(bookmark2.url);\n    const urlSimilarity = this.calculateTextSimilarity(url1Clean, url2Clean);\n\n    // 综合相似度\n    return (titleSimilarity * 0.4 + urlSimilarity * 0.6);\n  }\n\n  // 计算文本相似度（简单的 Jaccard 相似度）\n  private calculateTextSimilarity(text1: string, text2: string): number {\n    const words1 = new Set(text1.toLowerCase().split(/\\s+/));\n    const words2 = new Set(text2.toLowerCase().split(/\\s+/));\n    \n    const intersection = new Set([...words1].filter(x => words2.has(x)));\n    const union = new Set([...words1, ...words2]);\n    \n    return intersection.size / union.size;\n  }\n\n  // 清理 URL\n  private cleanUrl(url: string): string {\n    try {\n      const urlObj = new URL(url);\n      return `${urlObj.hostname}${urlObj.pathname}`;\n    } catch {\n      return url;\n    }\n  }\n\n  // 计算组相似度\n  private calculateGroupSimilarity(bookmarks: Bookmark[]): number {\n    if (bookmarks.length < 2) return 0;\n    \n    let totalSimilarity = 0;\n    let comparisons = 0;\n    \n    for (let i = 0; i < bookmarks.length; i++) {\n      for (let j = i + 1; j < bookmarks.length; j++) {\n        totalSimilarity += this.calculateSimilarity(bookmarks[i], bookmarks[j]);\n        comparisons++;\n      }\n    }\n    \n    return comparisons > 0 ? totalSimilarity / comparisons : 0;\n  }\n\n  // 获取重复原因\n  private getDuplicateReason(bookmarks: Bookmark[]): string {\n    if (bookmarks.length < 2) return '';\n    \n    // 检查是否有完全相同的 URL\n    const urls = bookmarks.map(b => b.url);\n    const uniqueUrls = new Set(urls);\n    \n    if (uniqueUrls.size < urls.length) {\n      return '发现完全相同的 URL';\n    }\n    \n    // 检查标题相似度\n    const titles = bookmarks.map(b => b.title);\n    const titleSimilarity = this.calculateTextSimilarity(titles[0], titles[1]);\n    \n    if (titleSimilarity > 0.8) {\n      return '标题高度相似';\n    }\n    \n    return 'URL 和内容相似';\n  }\n\n  // 生成描述建议\n  private async generateDescriptions(bookmarks: Bookmark[], threshold: number) {\n    const suggestions: any[] = [];\n\n    for (const bookmark of bookmarks) {\n      if (!bookmark.description || bookmark.description.length < 10) {\n        const suggestion = this.generateDescriptionForBookmark(bookmark);\n        \n        if (suggestion && suggestion.confidence >= threshold) {\n          suggestions.push({\n            bookmarkId: bookmark.id,\n            currentDescription: bookmark.description || '',\n            suggestedDescription: suggestion.description,\n            confidence: suggestion.confidence\n          });\n        }\n      }\n    }\n\n    return suggestions;\n  }\n\n  // 为单个书签生成描述\n  private generateDescriptionForBookmark(bookmark: Bookmark) {\n    const url = bookmark.url.toLowerCase();\n    const title = bookmark.title;\n    \n    // 基于域名生成描述\n    const domainDescriptions: Record<string, string> = {\n      'github.com': '开源代码仓库和协作平台',\n      'stackoverflow.com': '程序员问答社区',\n      'youtube.com': '视频分享平台',\n      'wikipedia.org': '在线百科全书',\n      'twitter.com': '社交媒体平台',\n      'linkedin.com': '职业社交网络',\n      'medium.com': '在线发布平台',\n      'dev.to': '开发者社区'\n    };\n\n    for (const [domain, description] of Object.entries(domainDescriptions)) {\n      if (url.includes(domain)) {\n        return {\n          description: `${title} - ${description}`,\n          confidence: 0.8\n        };\n      }\n    }\n\n    // 基于 URL 路径生成描述\n    if (url.includes('/docs/') || url.includes('/documentation/')) {\n      return {\n        description: `${title} - 技术文档和使用指南`,\n        confidence: 0.7\n      };\n    }\n\n    if (url.includes('/blog/') || url.includes('/article/')) {\n      return {\n        description: `${title} - 博客文章`,\n        confidence: 0.7\n      };\n    }\n\n    if (url.includes('/tutorial/') || url.includes('/guide/')) {\n      return {\n        description: `${title} - 教程指南`,\n        confidence: 0.7\n      };\n    }\n\n    // 默认描述\n    return {\n      description: `${title} - 网页书签`,\n      confidence: 0.5\n    };\n  }\n\n  // 建议标签\n  private async suggestTags(bookmarks: Bookmark[], threshold: number) {\n    const suggestions: any[] = [];\n\n    for (const bookmark of bookmarks) {\n      const tags = this.generateTagsForBookmark(bookmark);\n      \n      if (tags && tags.confidence >= threshold) {\n        suggestions.push({\n          bookmarkId: bookmark.id,\n          suggestedTags: tags.tags,\n          confidence: tags.confidence\n        });\n      }\n    }\n\n    return suggestions;\n  }\n\n  // 为单个书签生成标签\n  private generateTagsForBookmark(bookmark: Bookmark) {\n    const url = bookmark.url.toLowerCase();\n    const title = bookmark.title.toLowerCase();\n    const tags: string[] = [];\n    let confidence = 0;\n\n    // 基于域名的标签\n    const domainTags: Record<string, string[]> = {\n      'github.com': ['开源', '代码', 'Git'],\n      'stackoverflow.com': ['编程', '问答', '技术'],\n      'youtube.com': ['视频', '娱乐'],\n      'twitter.com': ['社交', '新闻'],\n      'linkedin.com': ['职业', '社交', '求职'],\n      'medium.com': ['博客', '文章'],\n      'wikipedia.org': ['百科', '知识']\n    };\n\n    for (const [domain, domainTagList] of Object.entries(domainTags)) {\n      if (url.includes(domain)) {\n        tags.push(...domainTagList);\n        confidence = Math.max(confidence, 0.8);\n      }\n    }\n\n    // 基于关键词的标签\n    const keywordTags: Record<string, string[]> = {\n      'api': ['API', '接口', '开发'],\n      'tutorial': ['教程', '学习'],\n      'documentation': ['文档', '参考'],\n      'tool': ['工具', '实用'],\n      'framework': ['框架', '开发'],\n      'library': ['库', '开发'],\n      'design': ['设计', 'UI'],\n      'css': ['CSS', '样式', '前端'],\n      'javascript': ['JavaScript', 'JS', '前端'],\n      'python': ['Python', '编程'],\n      'react': ['React', '前端', '框架']\n    };\n\n    for (const [keyword, keywordTagList] of Object.entries(keywordTags)) {\n      if (title.includes(keyword) || url.includes(keyword)) {\n        tags.push(...keywordTagList);\n        confidence = Math.max(confidence, 0.7);\n      }\n    }\n\n    // 去重\n    const uniqueTags = [...new Set(tags)];\n\n    return uniqueTags.length > 0 ? {\n      tags: uniqueTags,\n      confidence\n    } : null;\n  }\n\n  // 建议新分类\n  private async suggestNewCategories(\n    bookmarks: Bookmark[], \n    categories: Category[], \n    threshold: number\n  ) {\n    const suggestions: any[] = [];\n    \n    // 分析未分类或分类不当的书签\n    const uncategorizedBookmarks = bookmarks.filter(b => {\n      const category = categories.find(c => c.id === b.category);\n      return !category || category.name === '未分类' || category.name === '其他';\n    });\n\n    // 基于域名聚类\n    const domainGroups = this.groupBookmarksByDomain(uncategorizedBookmarks);\n    \n    for (const [domain, domainBookmarks] of Object.entries(domainGroups)) {\n      if (domainBookmarks.length >= 3) { // 至少3个书签才建议新分类\n        const categoryName = this.suggestCategoryNameForDomain(domain);\n        if (categoryName) {\n          suggestions.push({\n            name: categoryName,\n            description: `基于 ${domain} 域名的自动分类`,\n            bookmarkIds: domainBookmarks.map(b => b.id),\n            confidence: 0.8\n          });\n        }\n      }\n    }\n\n    return suggestions;\n  }\n\n  // 按域名分组书签\n  private groupBookmarksByDomain(bookmarks: Bookmark[]): Record<string, Bookmark[]> {\n    const groups: Record<string, Bookmark[]> = {};\n    \n    for (const bookmark of bookmarks) {\n      try {\n        const domain = new URL(bookmark.url).hostname;\n        if (!groups[domain]) {\n          groups[domain] = [];\n        }\n        groups[domain].push(bookmark);\n      } catch {\n        // 忽略无效 URL\n      }\n    }\n    \n    return groups;\n  }\n\n  // 为域名建议分类名称\n  private suggestCategoryNameForDomain(domain: string): string | null {\n    const domainCategories: Record<string, string> = {\n      'github.com': '开源项目',\n      'stackoverflow.com': '技术问答',\n      'youtube.com': '视频资源',\n      'medium.com': '技术博客',\n      'dev.to': '开发社区',\n      'twitter.com': '社交媒体',\n      'linkedin.com': '职业网络'\n    };\n\n    return domainCategories[domain] || null;\n  }\n\n  // 应用 AI 建议\n  async applyAISuggestions(\n    analysisResult: AIAnalysisResult,\n    selectedSuggestions: {\n      categoryRecommendations?: string[];\n      duplicateRemovals?: string[];\n      descriptionUpdates?: string[];\n      tagUpdates?: string[];\n      newCategories?: string[];\n    }\n  ): Promise<{ applied: number; errors: string[] }> {\n    let applied = 0;\n    const errors: string[] = [];\n\n    try {\n      // 应用分类建议\n      if (selectedSuggestions.categoryRecommendations) {\n        for (const suggestionId of selectedSuggestions.categoryRecommendations) {\n          const suggestion = analysisResult.suggestions.categoryRecommendations.find(\n            s => `${s.bookmarkId}-${s.suggestedCategory}` === suggestionId\n          );\n          \n          if (suggestion) {\n            try {\n              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {\n                category: suggestion.suggestedCategory\n              });\n              applied++;\n            } catch (error) {\n              errors.push(`更新书签分类失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n          }\n        }\n      }\n\n      // 应用描述建议\n      if (selectedSuggestions.descriptionUpdates) {\n        for (const suggestionId of selectedSuggestions.descriptionUpdates) {\n          const suggestion = analysisResult.suggestions.descriptionSuggestions.find(\n            s => s.bookmarkId === suggestionId\n          );\n          \n          if (suggestion) {\n            try {\n              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {\n                description: suggestion.suggestedDescription\n              });\n              applied++;\n            } catch (error) {\n              errors.push(`更新书签描述失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n          }\n        }\n      }\n\n      // 应用标签建议\n      if (selectedSuggestions.tagUpdates) {\n        for (const suggestionId of selectedSuggestions.tagUpdates) {\n          const suggestion = analysisResult.suggestions.tagSuggestions.find(\n            s => s.bookmarkId === suggestionId\n          );\n          \n          if (suggestion) {\n            try {\n              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {\n                tags: suggestion.suggestedTags\n              });\n              applied++;\n            } catch (error) {\n              errors.push(`更新书签标签失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n          }\n        }\n      }\n\n      // 创建新分类\n      if (selectedSuggestions.newCategories) {\n        for (const suggestionId of selectedSuggestions.newCategories) {\n          const suggestion = analysisResult.suggestions.newCategorySuggestions.find(\n            s => s.name === suggestionId\n          );\n          \n          if (suggestion) {\n            try {\n              const newCategory = await this.categoryService.createCategory({\n                name: suggestion.name,\n                description: suggestion.description,\n                order: 999\n              });\n              \n              // 将相关书签移动到新分类\n              for (const bookmarkId of suggestion.bookmarkIds) {\n                await this.bookmarkService.updateBookmark(bookmarkId, {\n                  category: newCategory.id\n                });\n              }\n              \n              applied++;\n            } catch (error) {\n              errors.push(`创建新分类失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n            }\n          }\n        }\n      }\n\n      // 删除重复书签\n      if (selectedSuggestions.duplicateRemovals) {\n        for (const suggestionId of selectedSuggestions.duplicateRemovals) {\n          try {\n            await this.bookmarkService.deleteBookmark(suggestionId);\n            applied++;\n          } catch (error) {\n            errors.push(`删除重复书签失败: ${error instanceof Error ? error.message : 'Unknown error'}`);\n          }\n        }\n      }\n\n    } catch (error) {\n      errors.push(`应用 AI 建议时出错: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n\n    return { applied, errors };\n  }\n\n  // 获取 AI 配置\n  getAIConfig(): AIConfig {\n    return this.aiConfig;\n  }\n\n  // 更新 AI 配置\n  updateAIConfig(config: Partial<AIConfig>): void {\n    this.aiConfig = { ...this.aiConfig, ...config };\n  }\n}\n", "// CloudNav 2.0 - 统计功能服务\nimport type { Stats, Bookmark, Category } from '@/types';\nimport { KVService } from './kv';\nimport { BookmarkService } from './bookmarks';\nimport { CategoryService } from './categories';\n\nexport interface DetailedStats {\n  overview: {\n    totalBookmarks: number;\n    totalCategories: number;\n    totalClicks: number;\n    totalViews: number;\n    averageClicksPerBookmark: number;\n  };\n  bookmarkStats: {\n    topBookmarks: Array<{\n      bookmark: Bookmark;\n      clicks: number;\n      clickRate: number;\n    }>;\n    recentBookmarks: Bookmark[];\n    bookmarksByCategory: Record<string, number>;\n  };\n  categoryStats: {\n    topCategories: Array<{\n      category: Category;\n      bookmarkCount: number;\n      totalClicks: number;\n      averageClicks: number;\n    }>;\n    categoryDistribution: Array<{\n      categoryName: string;\n      percentage: number;\n      count: number;\n    }>;\n  };\n  searchStats: {\n    topSearches: Array<{\n      query: string;\n      count: number;\n      percentage: number;\n    }>;\n    totalSearches: number;\n    uniqueSearches: number;\n  };\n  timeStats: {\n    dailyStats: Record<string, number>;\n    weeklyTrend: Array<{\n      week: string;\n      clicks: number;\n      views: number;\n    }>;\n    monthlyTrend: Array<{\n      month: string;\n      clicks: number;\n      views: number;\n    }>;\n  };\n  deviceStats: {\n    mobile: number;\n    desktop: number;\n    tablet: number;\n    mobilePercentage: number;\n    desktopPercentage: number;\n    tabletPercentage: number;\n  };\n  performanceStats: {\n    averageLoadTime?: number;\n    errorRate?: number;\n    uptime?: number;\n  };\n}\n\nexport class StatisticsService {\n  private kvService: KVService;\n  private bookmarkService: BookmarkService;\n  private categoryService: CategoryService;\n\n  constructor(kvService: KVService) {\n    this.kvService = kvService;\n    this.bookmarkService = new BookmarkService(kvService);\n    this.categoryService = new CategoryService(kvService);\n  }\n\n  // 获取详细统计数据\n  async getDetailedStats(): Promise<DetailedStats> {\n    const [stats, bookmarks, categories] = await Promise.all([\n      this.kvService.getStats(),\n      this.bookmarkService.getAllBookmarks(),\n      this.categoryService.getAllCategories()\n    ]);\n\n    return {\n      overview: this.calculateOverviewStats(stats, bookmarks, categories),\n      bookmarkStats: this.calculateBookmarkStats(stats, bookmarks, categories),\n      categoryStats: this.calculateCategoryStats(stats, bookmarks, categories),\n      searchStats: this.calculateSearchStats(stats),\n      timeStats: this.calculateTimeStats(stats),\n      deviceStats: this.calculateDeviceStats(stats),\n      performanceStats: this.calculatePerformanceStats()\n    };\n  }\n\n  // 计算概览统计\n  private calculateOverviewStats(stats: Stats, bookmarks: Bookmark[], categories: Category[]) {\n    const totalClicks = bookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);\n    \n    return {\n      totalBookmarks: bookmarks.length,\n      totalCategories: categories.length,\n      totalClicks: totalClicks,\n      totalViews: stats.totalViews,\n      averageClicksPerBookmark: bookmarks.length > 0 ? totalClicks / bookmarks.length : 0\n    };\n  }\n\n  // 计算书签统计\n  private calculateBookmarkStats(stats: Stats, bookmarks: Bookmark[], categories: Category[]) {\n    // 热门书签\n    const topBookmarks = bookmarks\n      .sort((a, b) => b.clickCount - a.clickCount)\n      .slice(0, 10)\n      .map(bookmark => ({\n        bookmark,\n        clicks: bookmark.clickCount,\n        clickRate: stats.totalClicks > 0 ? (bookmark.clickCount / stats.totalClicks) * 100 : 0\n      }));\n\n    // 最近添加的书签\n    const recentBookmarks = bookmarks\n      .sort((a, b) => b.createdAt - a.createdAt)\n      .slice(0, 10);\n\n    // 按分类统计书签数量\n    const bookmarksByCategory: Record<string, number> = {};\n    bookmarks.forEach(bookmark => {\n      bookmarksByCategory[bookmark.category] = (bookmarksByCategory[bookmark.category] || 0) + 1;\n    });\n\n    return {\n      topBookmarks,\n      recentBookmarks,\n      bookmarksByCategory\n    };\n  }\n\n  // 计算分类统计\n  private calculateCategoryStats(stats: Stats, bookmarks: Bookmark[], categories: Category[]) {\n    // 分类点击统计\n    const categoryClickStats: Record<string, number> = {};\n    bookmarks.forEach(bookmark => {\n      categoryClickStats[bookmark.category] = (categoryClickStats[bookmark.category] || 0) + bookmark.clickCount;\n    });\n\n    // 热门分类\n    const topCategories = categories\n      .map(category => {\n        const bookmarkCount = bookmarks.filter(b => b.category === category.id).length;\n        const totalClicks = categoryClickStats[category.id] || 0;\n        return {\n          category,\n          bookmarkCount,\n          totalClicks,\n          averageClicks: bookmarkCount > 0 ? totalClicks / bookmarkCount : 0\n        };\n      })\n      .sort((a, b) => b.totalClicks - a.totalClicks)\n      .slice(0, 10);\n\n    // 分类分布\n    const totalBookmarks = bookmarks.length;\n    const categoryDistribution = categories\n      .map(category => {\n        const count = bookmarks.filter(b => b.category === category.id).length;\n        return {\n          categoryName: category.name,\n          count,\n          percentage: totalBookmarks > 0 ? (count / totalBookmarks) * 100 : 0\n        };\n      })\n      .sort((a, b) => b.count - a.count);\n\n    return {\n      topCategories,\n      categoryDistribution\n    };\n  }\n\n  // 计算搜索统计\n  private calculateSearchStats(stats: Stats) {\n    const searchEntries = Object.entries(stats.searchStats);\n    const totalSearches = searchEntries.reduce((sum, [, count]) => sum + count, 0);\n    \n    const topSearches = searchEntries\n      .sort(([, a], [, b]) => b - a)\n      .slice(0, 20)\n      .map(([query, count]) => ({\n        query,\n        count,\n        percentage: totalSearches > 0 ? (count / totalSearches) * 100 : 0\n      }));\n\n    return {\n      topSearches,\n      totalSearches,\n      uniqueSearches: searchEntries.length\n    };\n  }\n\n  // 计算时间统计\n  private calculateTimeStats(stats: Stats) {\n    const dailyStats = stats.dailyStats;\n    \n    // 计算周统计\n    const weeklyTrend = this.aggregateByWeek(dailyStats);\n    \n    // 计算月统计\n    const monthlyTrend = this.aggregateByMonth(dailyStats);\n\n    return {\n      dailyStats,\n      weeklyTrend,\n      monthlyTrend\n    };\n  }\n\n  // 计算设备统计\n  private calculateDeviceStats(stats: Stats) {\n    const { mobile, desktop, tablet } = stats.deviceStats;\n    const total = mobile + desktop + tablet;\n\n    return {\n      mobile,\n      desktop,\n      tablet,\n      mobilePercentage: total > 0 ? (mobile / total) * 100 : 0,\n      desktopPercentage: total > 0 ? (desktop / total) * 100 : 0,\n      tabletPercentage: total > 0 ? (tablet / total) * 100 : 0\n    };\n  }\n\n  // 计算性能统计\n  private calculatePerformanceStats() {\n    // 这里可以添加性能监控数据\n    return {\n      averageLoadTime: undefined,\n      errorRate: undefined,\n      uptime: undefined\n    };\n  }\n\n  // 按周聚合数据\n  private aggregateByWeek(dailyStats: Record<string, number>) {\n    const weeklyData: Record<string, { clicks: number; views: number }> = {};\n    \n    Object.entries(dailyStats).forEach(([date, clicks]) => {\n      const weekStart = this.getWeekStart(new Date(date));\n      const weekKey = weekStart.toISOString().split('T')[0];\n      \n      if (!weeklyData[weekKey]) {\n        weeklyData[weekKey] = { clicks: 0, views: 0 };\n      }\n      weeklyData[weekKey].clicks += clicks;\n    });\n\n    return Object.entries(weeklyData)\n      .map(([week, data]) => ({ week, ...data }))\n      .sort((a, b) => a.week.localeCompare(b.week));\n  }\n\n  // 按月聚合数据\n  private aggregateByMonth(dailyStats: Record<string, number>) {\n    const monthlyData: Record<string, { clicks: number; views: number }> = {};\n    \n    Object.entries(dailyStats).forEach(([date, clicks]) => {\n      const monthKey = date.substring(0, 7); // YYYY-MM\n      \n      if (!monthlyData[monthKey]) {\n        monthlyData[monthKey] = { clicks: 0, views: 0 };\n      }\n      monthlyData[monthKey].clicks += clicks;\n    });\n\n    return Object.entries(monthlyData)\n      .map(([month, data]) => ({ month, ...data }))\n      .sort((a, b) => a.month.localeCompare(b.month));\n  }\n\n  // 获取周的开始日期\n  private getWeekStart(date: Date): Date {\n    const d = new Date(date);\n    const day = d.getDay();\n    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 周一为一周开始\n    return new Date(d.setDate(diff));\n  }\n\n  // 记录页面访问\n  async recordPageView(userAgent?: string): Promise<void> {\n    try {\n      const stats = await this.kvService.getStats();\n      stats.totalViews++;\n\n      // 检测设备类型\n      if (userAgent) {\n        const deviceType = this.detectDeviceType(userAgent);\n        stats.deviceStats[deviceType]++;\n      }\n\n      // 记录日统计\n      const today = new Date().toISOString().split('T')[0];\n      stats.dailyStats[today] = (stats.dailyStats[today] || 0) + 1;\n\n      await this.kvService.updateStats(stats);\n    } catch (error) {\n      console.error('Failed to record page view:', error);\n    }\n  }\n\n  // 检测设备类型\n  private detectDeviceType(userAgent: string): 'mobile' | 'desktop' | 'tablet' {\n    const ua = userAgent.toLowerCase();\n    \n    if (/tablet|ipad|playbook|silk/.test(ua)) {\n      return 'tablet';\n    }\n    \n    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\\sce|palm|smartphone|iemobile/.test(ua)) {\n      return 'mobile';\n    }\n    \n    return 'desktop';\n  }\n\n  // 记录搜索\n  async recordSearch(query: string): Promise<void> {\n    try {\n      if (!query.trim()) return;\n\n      const stats = await this.kvService.getStats();\n      stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;\n      await this.kvService.updateStats(stats);\n    } catch (error) {\n      console.error('Failed to record search:', error);\n    }\n  }\n\n  // 清理过期统计数据\n  async cleanupOldStats(daysToKeep: number = 90): Promise<void> {\n    try {\n      const stats = await this.kvService.getStats();\n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);\n      const cutoffString = cutoffDate.toISOString().split('T')[0];\n\n      // 清理日统计数据\n      Object.keys(stats.dailyStats).forEach(date => {\n        if (date < cutoffString) {\n          delete stats.dailyStats[date];\n        }\n      });\n\n      await this.kvService.updateStats(stats);\n      console.log(`Cleaned up stats older than ${daysToKeep} days`);\n    } catch (error) {\n      console.error('Failed to cleanup old stats:', error);\n    }\n  }\n\n  // 导出统计数据\n  async exportStats(): Promise<DetailedStats> {\n    return await this.getDetailedStats();\n  }\n\n  // 重置统计数据\n  async resetStats(): Promise<void> {\n    try {\n      await this.kvService.updateStats({\n        totalClicks: 0,\n        totalViews: 0,\n        bookmarkStats: {},\n        categoryStats: {},\n        searchStats: {},\n        dailyStats: {},\n        deviceStats: { mobile: 0, desktop: 0, tablet: 0 },\n        lastUpdated: Date.now()\n      });\n      console.log('Statistics reset successfully');\n    } catch (error) {\n      console.error('Failed to reset stats:', error);\n      throw error;\n    }\n  }\n}\n", "// CloudNav 2.0 - API 路由\nimport { Hono } from 'hono';\nimport type { HonoApp } from '@/types/hono';\nimport type { ApiResponse } from '@/types';\nimport { KVService } from '@/services/kv';\nimport { BookmarkService } from '@/services/bookmarks';\nimport { CategoryService } from '@/services/categories';\nimport { MigrationService } from '@/services/migration';\nimport { ChromeBookmarkService } from '@/services/chrome-bookmarks';\nimport { AIService } from '@/services/ai';\nimport { StatisticsService } from '@/services/statistics';\n\nconst app = new Hono<HonoApp>();\n\n// 中间件：添加服务实例到上下文\napp.use('*', async (c, next) => {\n  const kvService = c.get('kvService') as KVService;\n  const bookmarkService = new BookmarkService(kvService);\n  const categoryService = new CategoryService(kvService);\n\n  // 获取 AI 配置\n  const config = await kvService.getConfig();\n\n  c.set('bookmarkService', bookmarkService);\n  c.set('categoryService', categoryService);\n  c.set('migrationService', new MigrationService(kvService));\n  c.set('chromeBookmarkService', new ChromeBookmarkService(bookmarkService, categoryService));\n  c.set('aiService', new AIService(bookmarkService, categoryService, config.aiConfig));\n  c.set('statisticsService', new StatisticsService(kvService));\n  await next();\n});\n\n// ==================== 书签管理 API ====================\n\n// 获取所有书签\napp.get('/bookmarks', async (c) => {\n  try {\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n    const bookmarks = await bookmarkService.getAllBookmarks();\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: bookmarks\n    });\n  } catch (error) {\n    console.error('Get bookmarks error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get bookmarks'\n    }, 500);\n  }\n});\n\n// 根据ID获取书签\napp.get('/bookmarks/:id', async (c) => {\n  try {\n    const id = c.req.param('id');\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n    const bookmark = await bookmarkService.getBookmarkById(id);\n\n    if (!bookmark) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Bookmark not found'\n      }, 404);\n    }\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: bookmark\n    });\n  } catch (error) {\n    console.error('Get bookmark error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get bookmark'\n    }, 500);\n  }\n});\n\n// 创建新书签\napp.post('/bookmarks', async (c) => {\n  try {\n    const bookmarkData = await c.req.json();\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n\n    // 验证数据\n    const validation = bookmarkService.validateBookmarkData(bookmarkData);\n    if (!validation.valid) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Validation failed',\n        data: { errors: validation.errors }\n      }, 400);\n    }\n\n    // 检查重复\n    const duplicate = await bookmarkService.checkDuplicateBookmark(bookmarkData.url);\n    if (duplicate) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Bookmark with this URL already exists'\n      }, 409);\n    }\n\n    const bookmark = await bookmarkService.createBookmark(bookmarkData);\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: bookmark,\n      message: 'Bookmark created successfully'\n    }, 201);\n  } catch (error) {\n    console.error('Create bookmark error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to create bookmark'\n    }, 500);\n  }\n});\n\n// 更新书签\napp.put('/bookmarks/:id', async (c) => {\n  try {\n    const id = c.req.param('id');\n    const updates = await c.req.json();\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n\n    const bookmark = await bookmarkService.updateBookmark(id, updates);\n\n    if (!bookmark) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Bookmark not found'\n      }, 404);\n    }\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: bookmark,\n      message: 'Bookmark updated successfully'\n    });\n  } catch (error) {\n    console.error('Update bookmark error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to update bookmark'\n    }, 500);\n  }\n});\n\n// 删除书签\napp.delete('/bookmarks/:id', async (c) => {\n  try {\n    const id = c.req.param('id');\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n\n    const success = await bookmarkService.deleteBookmark(id);\n\n    if (!success) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Bookmark not found'\n      }, 404);\n    }\n\n    return c.json<ApiResponse>({\n      success: true,\n      message: 'Bookmark deleted successfully'\n    });\n  } catch (error) {\n    console.error('Delete bookmark error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to delete bookmark'\n    }, 500);\n  }\n});\n\n// 批量删除书签\napp.post('/bookmarks/batch-delete', async (c) => {\n  try {\n    const { ids } = await c.req.json();\n\n    if (!Array.isArray(ids) || ids.length === 0) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Invalid bookmark IDs'\n      }, 400);\n    }\n\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n    const result = await bookmarkService.deleteBookmarks(ids);\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: result,\n      message: `Deleted ${result.success.length} bookmarks`\n    });\n  } catch (error) {\n    console.error('Batch delete bookmarks error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to delete bookmarks'\n    }, 500);\n  }\n});\n\n// ==================== 分类管理 API ====================\n\n// 获取所有分类\napp.get('/categories', async (c) => {\n  try {\n    const categoryService = c.get('categoryService') as CategoryService;\n    const categories = await categoryService.getAllCategories();\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: categories\n    });\n  } catch (error) {\n    console.error('Get categories error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get categories'\n    }, 500);\n  }\n});\n\n// 根据ID获取分类\napp.get('/categories/:id', async (c) => {\n  try {\n    const id = c.req.param('id');\n    const categoryService = c.get('categoryService') as CategoryService;\n    const category = await categoryService.getCategoryById(id);\n\n    if (!category) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Category not found'\n      }, 404);\n    }\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: category\n    });\n  } catch (error) {\n    console.error('Get category error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get category'\n    }, 500);\n  }\n});\n\n// 创建新分类\napp.post('/categories', async (c) => {\n  try {\n    const categoryData = await c.req.json();\n    const categoryService = c.get('categoryService') as CategoryService;\n\n    // 验证数据\n    const validation = categoryService.validateCategoryData(categoryData);\n    if (!validation.valid) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Validation failed',\n        data: { errors: validation.errors }\n      }, 400);\n    }\n\n    // 检查重复\n    const duplicate = await categoryService.checkDuplicateCategoryName(categoryData.name);\n    if (duplicate) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Category with this name already exists'\n      }, 409);\n    }\n\n    const category = await categoryService.createCategory(categoryData);\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: category,\n      message: 'Category created successfully'\n    }, 201);\n  } catch (error) {\n    console.error('Create category error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to create category'\n    }, 500);\n  }\n});\n\n// 更新分类\napp.put('/categories/:id', async (c) => {\n  try {\n    const id = c.req.param('id');\n    const updates = await c.req.json();\n    const categoryService = c.get('categoryService') as CategoryService;\n\n    const category = await categoryService.updateCategory(id, updates);\n\n    if (!category) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Category not found'\n      }, 404);\n    }\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: category,\n      message: 'Category updated successfully'\n    });\n  } catch (error) {\n    console.error('Update category error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to update category'\n    }, 500);\n  }\n});\n\n// 删除分类\napp.delete('/categories/:id', async (c) => {\n  try {\n    const id = c.req.param('id');\n    const { moveBookmarksTo, deleteBookmarks } = c.req.query();\n    const categoryService = c.get('categoryService') as CategoryService;\n\n    const options: any = {};\n    if (moveBookmarksTo) options.moveBookmarksTo = moveBookmarksTo;\n    if (deleteBookmarks === 'true') options.deleteBookmarks = true;\n\n    const result = await categoryService.deleteCategory(id, options);\n\n    if (!result.success) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Category not found'\n      }, 404);\n    }\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: result,\n      message: 'Category deleted successfully'\n    });\n  } catch (error) {\n    console.error('Delete category error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: error instanceof Error ? error.message : 'Failed to delete category'\n    }, 500);\n  }\n});\n\n// 获取分类统计\napp.get('/categories/stats', async (c) => {\n  try {\n    const categoryService = c.get('categoryService') as CategoryService;\n    const stats = await categoryService.getCategoryStats();\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: stats\n    });\n  } catch (error) {\n    console.error('Get category stats error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get category stats'\n    }, 500);\n  }\n});\n\n// 获取统计数据\napp.get('/stats', async (c) => {\n  try {\n    const kvService = c.get('kvService') as KVService;\n    const stats = await kvService.getStats();\n    \n    return c.json<ApiResponse>({\n      success: true,\n      data: stats\n    });\n  } catch (error) {\n    console.error('Get stats error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get stats'\n    }, 500);\n  }\n});\n\n// ==================== 统计和点击 API ====================\n\n// 记录点击统计\napp.post('/stats/click', async (c) => {\n  try {\n    const { bookmarkId } = await c.req.json();\n\n    if (!bookmarkId) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Bookmark ID is required'\n      }, 400);\n    }\n\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n    const success = await bookmarkService.incrementClickCount(bookmarkId);\n\n    if (success) {\n      return c.json<ApiResponse>({\n        success: true,\n        message: 'Click recorded'\n      });\n    } else {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Bookmark not found'\n      }, 404);\n    }\n  } catch (error) {\n    console.error('Record click error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to record click'\n    }, 500);\n  }\n});\n\n// 获取热门书签\napp.get('/bookmarks/popular', async (c) => {\n  try {\n    const limit = parseInt(c.req.query('limit') || '10');\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n    const bookmarks = await bookmarkService.getPopularBookmarks(limit);\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: bookmarks\n    });\n  } catch (error) {\n    console.error('Get popular bookmarks error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get popular bookmarks'\n    }, 500);\n  }\n});\n\n// 获取最近添加的书签\napp.get('/bookmarks/recent', async (c) => {\n  try {\n    const limit = parseInt(c.req.query('limit') || '10');\n    const bookmarkService = c.get('bookmarkService') as BookmarkService;\n    const bookmarks = await bookmarkService.getRecentBookmarks(limit);\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: bookmarks\n    });\n  } catch (error) {\n    console.error('Get recent bookmarks error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get recent bookmarks'\n    }, 500);\n  }\n});\n\n// ==================== 数据迁移 API ====================\n\n// 获取迁移状态\napp.get('/migration/status', async (c) => {\n  try {\n    const migrationService = c.get('migrationService') as MigrationService;\n    const status = await migrationService.getMigrationStatus();\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: status\n    });\n  } catch (error) {\n    console.error('Get migration status error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get migration status'\n    }, 500);\n  }\n});\n\n// 执行数据迁移\napp.post('/migration/migrate', async (c) => {\n  try {\n    const migrationService = c.get('migrationService') as MigrationService;\n    const result = await migrationService.performMigration();\n\n    return c.json<ApiResponse>({\n      success: result.success,\n      data: result.details,\n      message: result.message\n    });\n  } catch (error) {\n    console.error('Migration error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Migration failed'\n    }, 500);\n  }\n});\n\n// 创建示例数据\napp.post('/migration/sample-data', async (c) => {\n  try {\n    const migrationService = c.get('migrationService') as MigrationService;\n    const result = await migrationService.createSampleData();\n\n    return c.json<ApiResponse>({\n      success: result.success,\n      data: {\n        categoriesCreated: result.categoriesCreated,\n        bookmarksCreated: result.bookmarksCreated,\n        errors: result.errors\n      },\n      message: `Created ${result.categoriesCreated} categories and ${result.bookmarksCreated} bookmarks`\n    });\n  } catch (error) {\n    console.error('Create sample data error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to create sample data'\n    }, 500);\n  }\n});\n\n// 清空所有数据\napp.post('/migration/clear', async (c) => {\n  try {\n    const migrationService = c.get('migrationService') as MigrationService;\n    const success = await migrationService.clearAllData();\n\n    return c.json<ApiResponse>({\n      success,\n      message: success ? 'All data cleared successfully' : 'Failed to clear data'\n    });\n  } catch (error) {\n    console.error('Clear data error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to clear data'\n    }, 500);\n  }\n});\n\n// ==================== Chrome 书签导入导出 API ====================\n\n// 通用书签导入（支持 JSON 和 HTML 格式）\napp.post('/import/bookmarks', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { content, options = {} } = body;\n\n    if (!content) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Bookmark file content is required'\n      }, 400);\n    }\n\n    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;\n    const result = await chromeBookmarkService.importBookmarks(content, {\n      createCategories: options.createCategories !== false,\n      skipDuplicates: options.skipDuplicates !== false,\n      defaultCategory: options.defaultCategory,\n      format: options.format || 'auto'\n    });\n\n    return c.json<ApiResponse>({\n      success: result.success,\n      data: {\n        imported: result.imported,\n        skipped: result.skipped,\n        errors: result.errors\n      },\n      message: result.success\n        ? `成功导入 ${result.imported} 个书签，跳过 ${result.skipped} 个重复项`\n        : '导入失败'\n    });\n  } catch (error) {\n    console.error('Bookmark import error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to import bookmarks'\n    }, 500);\n  }\n});\n\n// 导入 Chrome 书签（保持向后兼容）\napp.post('/import/chrome', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { jsonContent, options = {} } = body;\n\n    if (!jsonContent) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Chrome bookmarks JSON content is required'\n      }, 400);\n    }\n\n    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;\n    const result = await chromeBookmarkService.importChromeBookmarks(jsonContent, {\n      createCategories: options.createCategories !== false,\n      skipDuplicates: options.skipDuplicates !== false,\n      defaultCategory: options.defaultCategory\n    });\n\n    return c.json<ApiResponse>({\n      success: result.success,\n      data: {\n        imported: result.imported,\n        skipped: result.skipped,\n        errors: result.errors\n      },\n      message: result.success\n        ? `成功导入 ${result.imported} 个书签，跳过 ${result.skipped} 个重复项`\n        : '导入失败'\n    });\n  } catch (error) {\n    console.error('Chrome import error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to import Chrome bookmarks'\n    }, 500);\n  }\n});\n\n// 导出为 Chrome 格式\napp.get('/export/chrome', async (c) => {\n  try {\n    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;\n    const chromeJson = await chromeBookmarkService.exportToChromeFormat();\n\n    // 设置下载头\n    c.header('Content-Type', 'application/json');\n    c.header('Content-Disposition', 'attachment; filename=\"cloudnav-bookmarks.json\"');\n\n    return c.text(chromeJson);\n  } catch (error) {\n    console.error('Chrome export error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to export Chrome bookmarks'\n    }, 500);\n  }\n});\n\n// 导出为 Netscape 格式\napp.get('/export/netscape', async (c) => {\n  try {\n    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;\n    const netscapeHtml = await chromeBookmarkService.exportToNetscapeFormat();\n\n    // 设置下载头\n    c.header('Content-Type', 'text/html');\n    c.header('Content-Disposition', 'attachment; filename=\"cloudnav-bookmarks.html\"');\n\n    return c.text(netscapeHtml);\n  } catch (error) {\n    console.error('Netscape export error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to export Netscape bookmarks'\n    }, 500);\n  }\n});\n\n// 获取导入统计\napp.get('/import/stats', async (c) => {\n  try {\n    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;\n    const stats = await chromeBookmarkService.getImportStats();\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: stats\n    });\n  } catch (error) {\n    console.error('Import stats error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get import stats'\n    }, 500);\n  }\n});\n\n// ==================== AI 智能整理 API ====================\n\n// AI 分析书签\napp.post('/ai/analyze', async (c) => {\n  try {\n    const body = await c.req.json();\n    const options = {\n      enableCategoryRecommendations: body.enableCategoryRecommendations !== false,\n      enableDuplicateDetection: body.enableDuplicateDetection !== false,\n      enableDescriptionGeneration: body.enableDescriptionGeneration !== false,\n      enableTagSuggestions: body.enableTagSuggestions !== false,\n      enableNewCategoryCreation: body.enableNewCategoryCreation !== false,\n      confidenceThreshold: body.confidenceThreshold || 0.7,\n      autoApply: body.autoApply || false\n    };\n\n    const aiService = c.get('aiService') as AIService;\n    const result = await aiService.analyzeBookmarks(options);\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: result,\n      message: `AI 分析完成，发现 ${result.statistics.categorySuggestions} 个分类建议，${result.statistics.duplicatesFound} 个重复项`\n    });\n  } catch (error) {\n    console.error('AI analyze error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to analyze bookmarks with AI'\n    }, 500);\n  }\n});\n\n// 应用 AI 建议\napp.post('/ai/apply', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { analysisResult, selectedSuggestions } = body;\n\n    if (!analysisResult || !selectedSuggestions) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Analysis result and selected suggestions are required'\n      }, 400);\n    }\n\n    const aiService = c.get('aiService') as AIService;\n    const result = await aiService.applyAISuggestions(analysisResult, selectedSuggestions);\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: result,\n      message: `成功应用 ${result.applied} 个 AI 建议${result.errors.length > 0 ? `，${result.errors.length} 个错误` : ''}`\n    });\n  } catch (error) {\n    console.error('AI apply error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to apply AI suggestions'\n    }, 500);\n  }\n});\n\n// 获取 AI 配置\napp.get('/ai/config', async (c) => {\n  try {\n    const aiService = c.get('aiService') as AIService;\n    const config = aiService.getAIConfig();\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: config\n    });\n  } catch (error) {\n    console.error('Get AI config error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get AI config'\n    }, 500);\n  }\n});\n\n// 更新 AI 配置\napp.put('/ai/config', async (c) => {\n  try {\n    const body = await c.req.json();\n    const aiService = c.get('aiService') as AIService;\n\n    aiService.updateAIConfig(body);\n\n    return c.json<ApiResponse>({\n      success: true,\n      message: 'AI configuration updated successfully'\n    });\n  } catch (error) {\n    console.error('Update AI config error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to update AI config'\n    }, 500);\n  }\n});\n\n// ==================== 统计功能 API ====================\n\n// 获取详细统计数据\napp.get('/statistics/detailed', async (c) => {\n  try {\n    const statisticsService = c.get('statisticsService') as StatisticsService;\n    const stats = await statisticsService.getDetailedStats();\n\n    return c.json<ApiResponse>({\n      success: true,\n      data: stats\n    });\n  } catch (error) {\n    console.error('Get detailed statistics error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get detailed statistics'\n    }, 500);\n  }\n});\n\n// 记录页面访问\napp.post('/statistics/page-view', async (c) => {\n  try {\n    const userAgent = c.req.header('User-Agent');\n    const statisticsService = c.get('statisticsService') as StatisticsService;\n\n    await statisticsService.recordPageView(userAgent);\n\n    return c.json<ApiResponse>({\n      success: true,\n      message: 'Page view recorded'\n    });\n  } catch (error) {\n    console.error('Record page view error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to record page view'\n    }, 500);\n  }\n});\n\n// 记录搜索\napp.post('/statistics/search', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { query } = body;\n\n    if (!query) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Search query is required'\n      }, 400);\n    }\n\n    const statisticsService = c.get('statisticsService') as StatisticsService;\n    await statisticsService.recordSearch(query);\n\n    return c.json<ApiResponse>({\n      success: true,\n      message: 'Search recorded'\n    });\n  } catch (error) {\n    console.error('Record search error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to record search'\n    }, 500);\n  }\n});\n\n// 导出统计数据\napp.get('/statistics/export', async (c) => {\n  try {\n    const statisticsService = c.get('statisticsService') as StatisticsService;\n    const stats = await statisticsService.exportStats();\n\n    // 设置下载头\n    c.header('Content-Type', 'application/json');\n    c.header('Content-Disposition', 'attachment; filename=\"cloudnav-statistics.json\"');\n\n    return c.text(JSON.stringify(stats, null, 2));\n  } catch (error) {\n    console.error('Export statistics error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to export statistics'\n    }, 500);\n  }\n});\n\n// 重置统计数据\napp.post('/statistics/reset', async (c) => {\n  try {\n    const statisticsService = c.get('statisticsService') as StatisticsService;\n    await statisticsService.resetStats();\n\n    return c.json<ApiResponse>({\n      success: true,\n      message: 'Statistics reset successfully'\n    });\n  } catch (error) {\n    console.error('Reset statistics error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to reset statistics'\n    }, 500);\n  }\n});\n\n// 清理过期统计数据\napp.post('/statistics/cleanup', async (c) => {\n  try {\n    const body = await c.req.json();\n    const daysToKeep = body.daysToKeep || 90;\n\n    const statisticsService = c.get('statisticsService') as StatisticsService;\n    await statisticsService.cleanupOldStats(daysToKeep);\n\n    return c.json<ApiResponse>({\n      success: true,\n      message: `Cleaned up statistics older than ${daysToKeep} days`\n    });\n  } catch (error) {\n    console.error('Cleanup statistics error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to cleanup statistics'\n    }, 500);\n  }\n});\n\n// 获取配置\napp.get('/config', async (c) => {\n  try {\n    const kvService = c.get('kvService') as KVService;\n    const config = await kvService.getConfig();\n    \n    // 不返回敏感信息\n    const publicConfig = {\n      siteName: config.siteName,\n      siteDescription: config.siteDescription,\n      features: config.features,\n      theme: config.theme\n    };\n    \n    return c.json<ApiResponse>({\n      success: true,\n      data: publicConfig\n    });\n  } catch (error) {\n    console.error('Get config error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Failed to get config'\n    }, 500);\n  }\n});\n\n// 搜索书签\napp.get('/search', async (c) => {\n  try {\n    const query = c.req.query('q') || '';\n    \n    if (!query.trim()) {\n      return c.json<ApiResponse>({\n        success: false,\n        error: 'Search query is required'\n      }, 400);\n    }\n\n    const kvService = c.get('kvService') as KVService;\n    const bookmarks = await kvService.getBookmarks();\n    \n    // 搜索逻辑\n    const filteredBookmarks = bookmarks.filter(bookmark => \n      bookmark.title.toLowerCase().includes(query.toLowerCase()) ||\n      bookmark.description?.toLowerCase().includes(query.toLowerCase()) ||\n      bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) ||\n      bookmark.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))\n    );\n\n    // 记录搜索统计\n    const stats = await kvService.getStats();\n    stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;\n    await kvService.updateStats(stats);\n    \n    return c.json<ApiResponse>({\n      success: true,\n      data: {\n        query,\n        results: filteredBookmarks,\n        total: filteredBookmarks.length\n      }\n    });\n  } catch (error) {\n    console.error('Search error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Search failed'\n    }, 500);\n  }\n});\n\n// 数据备份\napp.get('/backup', async (c) => {\n  try {\n    const kvService = c.get('kvService') as KVService;\n    const backup = await kvService.backup();\n    \n    return c.json<ApiResponse>({\n      success: true,\n      data: backup\n    });\n  } catch (error) {\n    console.error('Backup error:', error);\n    return c.json<ApiResponse>({\n      success: false,\n      error: 'Backup failed'\n    }, 500);\n  }\n});\n\n// 健康检查\napp.get('/health', (c) => {\n  return c.json<ApiResponse>({\n    success: true,\n    data: {\n      status: 'healthy',\n      timestamp: new Date().toISOString(),\n      version: '2.0.0'\n    }\n  });\n});\n\n// API 测试页面\napp.get('/test', (c) => {\n  const html = `\n    <!DOCTYPE html>\n    <html lang=\"zh-CN\">\n      <head>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>CloudNav 2.0 - API 测试</title>\n        <link href=\"https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css\" rel=\"stylesheet\">\n      </head>\n      <body class=\"bg-gray-50\">\n        <div class=\"min-h-screen\">\n          <header class=\"bg-white shadow-sm border-b\">\n            <div class=\"max-w-7xl mx-auto px-4 py-4\">\n              <h1 class=\"text-2xl font-bold text-gray-900\">🧪 API 测试页面</h1>\n              <p class=\"text-gray-600\">测试 CloudNav 2.0 的核心 API 功能</p>\n            </div>\n          </header>\n\n          <div class=\"max-w-7xl mx-auto px-4 py-8\">\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 class=\"text-xl font-semibold mb-4\">📊 迁移状态</h2>\n                <button onclick=\"testMigrationStatus()\" class=\"w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mb-4\">\n                  获取迁移状态\n                </button>\n                <pre id=\"migrationStatus\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\"></pre>\n              </div>\n\n              <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 class=\"text-xl font-semibold mb-4\">🎯 创建示例数据</h2>\n                <button onclick=\"createSampleData()\" class=\"w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mb-4\">\n                  创建示例数据\n                </button>\n                <pre id=\"sampleDataResult\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\"></pre>\n              </div>\n\n              <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 class=\"text-xl font-semibold mb-4\">📚 书签列表</h2>\n                <button onclick=\"getBookmarks()\" class=\"w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 mb-4\">\n                  获取书签列表\n                </button>\n                <pre id=\"bookmarksList\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\"></pre>\n              </div>\n\n              <div class=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 class=\"text-xl font-semibold mb-4\">📁 分类列表</h2>\n                <button onclick=\"getCategories()\" class=\"w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 mb-4\">\n                  获取分类列表\n                </button>\n                <pre id=\"categoriesList\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\"></pre>\n              </div>\n            </div>\n\n            <div class=\"mt-8 bg-white rounded-lg shadow-md p-6\">\n              <h2 class=\"text-xl font-semibold mb-4\">➕ 创建新书签</h2>\n              <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                <input type=\"text\" id=\"bookmarkTitle\" placeholder=\"书签标题\" class=\"px-3 py-2 border border-gray-300 rounded\">\n                <input type=\"url\" id=\"bookmarkUrl\" placeholder=\"书签URL\" class=\"px-3 py-2 border border-gray-300 rounded\">\n                <input type=\"text\" id=\"bookmarkCategory\" placeholder=\"分类ID (先创建示例数据获取)\" class=\"px-3 py-2 border border-gray-300 rounded\">\n                <input type=\"text\" id=\"bookmarkDesc\" placeholder=\"描述\" class=\"px-3 py-2 border border-gray-300 rounded\">\n              </div>\n              <button onclick=\"createBookmark()\" class=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4\">\n                创建书签\n              </button>\n              <pre id=\"createBookmarkResult\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\"></pre>\n            </div>\n\n            <div class=\"mt-8 bg-white rounded-lg shadow-md p-6\">\n              <h2 class=\"text-xl font-semibold mb-4\">📥 书签导入（支持多种格式）</h2>\n              <div class=\"mb-4\">\n                <label class=\"block text-sm font-medium text-gray-700 mb-2\">\n                  上传书签文件 (支持 .json 和 .html 格式)\n                </label>\n                <input type=\"file\" id=\"bookmarkFile\" accept=\".json,.html,.htm\" class=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\">\n                <div class=\"mt-2 text-sm text-gray-600\">\n                  <p>支持的格式：</p>\n                  <ul class=\"list-disc list-inside ml-4\">\n                    <li>Chrome JSON 格式 (bookmarks.json)</li>\n                    <li>HTML 书签格式 (bookmarks.html)</li>\n                    <li>Netscape 书签格式</li>\n                  </ul>\n                </div>\n              </div>\n              <div class=\"mb-4 space-y-2\">\n                <label class=\"flex items-center\">\n                  <input type=\"checkbox\" id=\"createCategories\" checked class=\"mr-2\">\n                  <span class=\"text-sm\">自动创建分类</span>\n                </label>\n                <label class=\"flex items-center\">\n                  <input type=\"checkbox\" id=\"skipDuplicates\" checked class=\"mr-2\">\n                  <span class=\"text-sm\">跳过重复书签</span>\n                </label>\n              </div>\n              <button onclick=\"importBookmarks()\" class=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mb-4\">\n                导入书签文件\n              </button>\n              <pre id=\"importResult\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\"></pre>\n            </div>\n\n            <div class=\"mt-8 bg-white rounded-lg shadow-md p-6\">\n              <h2 class=\"text-xl font-semibold mb-4\">📤 书签导出</h2>\n              <div class=\"space-x-4 mb-4\">\n                <button onclick=\"exportChromeBookmarks()\" class=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\">\n                  导出为 Chrome 格式\n                </button>\n                <button onclick=\"exportNetscapeBookmarks()\" class=\"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700\">\n                  导出为 HTML 格式\n                </button>\n                <button onclick=\"getImportStats()\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                  导入统计\n                </button>\n              </div>\n              <pre id=\"exportResult\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\"></pre>\n            </div>\n\n            <div class=\"mt-8 bg-white rounded-lg shadow-md p-6\">\n              <h2 class=\"text-xl font-semibold mb-4\">🤖 AI 智能整理</h2>\n              <div class=\"mb-4 space-y-2\">\n                <label class=\"flex items-center\">\n                  <input type=\"checkbox\" id=\"enableCategoryRecommendations\" checked class=\"mr-2\">\n                  <span class=\"text-sm\">启用分类推荐</span>\n                </label>\n                <label class=\"flex items-center\">\n                  <input type=\"checkbox\" id=\"enableDuplicateDetection\" checked class=\"mr-2\">\n                  <span class=\"text-sm\">启用重复检测</span>\n                </label>\n                <label class=\"flex items-center\">\n                  <input type=\"checkbox\" id=\"enableDescriptionGeneration\" checked class=\"mr-2\">\n                  <span class=\"text-sm\">启用描述生成</span>\n                </label>\n                <label class=\"flex items-center\">\n                  <input type=\"checkbox\" id=\"enableTagSuggestions\" checked class=\"mr-2\">\n                  <span class=\"text-sm\">启用标签建议</span>\n                </label>\n                <label class=\"flex items-center\">\n                  <input type=\"checkbox\" id=\"enableNewCategoryCreation\" class=\"mr-2\">\n                  <span class=\"text-sm\">启用新分类创建</span>\n                </label>\n                <div class=\"flex items-center space-x-2\">\n                  <label class=\"text-sm\">置信度阈值:</label>\n                  <input type=\"range\" id=\"confidenceThreshold\" min=\"0.1\" max=\"1.0\" step=\"0.1\" value=\"0.7\" class=\"flex-1\">\n                  <span id=\"confidenceValue\" class=\"text-sm font-mono\">0.7</span>\n                </div>\n              </div>\n              <div class=\"space-x-4 mb-4\">\n                <button onclick=\"analyzeWithAI()\" class=\"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700\">\n                  AI 分析书签\n                </button>\n                <button onclick=\"getAIConfig()\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                  获取 AI 配置\n                </button>\n              </div>\n              <pre id=\"aiResult\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60\"></pre>\n            </div>\n\n            <div class=\"mt-8 bg-white rounded-lg shadow-md p-6\">\n              <h2 class=\"text-xl font-semibold mb-4\">📊 统计功能</h2>\n              <div class=\"space-x-4 mb-4\">\n                <button onclick=\"getDetailedStats()\" class=\"px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700\">\n                  获取详细统计\n                </button>\n                <button onclick=\"recordPageView()\" class=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\">\n                  记录页面访问\n                </button>\n                <button onclick=\"recordSearch()\" class=\"px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700\">\n                  记录搜索\n                </button>\n                <button onclick=\"exportStats()\" class=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\">\n                  导出统计\n                </button>\n                <button onclick=\"resetStats()\" class=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\">\n                  重置统计\n                </button>\n              </div>\n              <pre id=\"statsResult\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60\"></pre>\n            </div>\n\n            <div class=\"mt-8 bg-white rounded-lg shadow-md p-6\">\n              <h2 class=\"text-xl font-semibold mb-4\">🧹 清理数据</h2>\n              <button onclick=\"clearAllData()\" class=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4\">\n                清空所有数据\n              </button>\n              <pre id=\"clearDataResult\" class=\"bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40\"></pre>\n            </div>\n\n            <div class=\"mt-8 bg-white rounded-lg shadow-md p-6\">\n              <h2 class=\"text-xl font-semibold mb-4\">🔗 快速链接</h2>\n              <div class=\"space-x-4\">\n                <a href=\"/\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">返回主页</a>\n                <a href=\"/admin\" class=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\">管理面板</a>\n                <a href=\"/about\" class=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\">关于页面</a>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <script>\n          // 更新置信度显示\n          document.getElementById('confidenceThreshold').addEventListener('input', function() {\n            document.getElementById('confidenceValue').textContent = this.value;\n          });\n\n          async function testMigrationStatus() {\n            try {\n              const response = await fetch('/api/migration/status');\n              const data = await response.json();\n              document.getElementById('migrationStatus').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('migrationStatus').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function createSampleData() {\n            try {\n              const response = await fetch('/api/migration/sample-data', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' }\n              });\n              const data = await response.json();\n              document.getElementById('sampleDataResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('sampleDataResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function getBookmarks() {\n            try {\n              const response = await fetch('/api/bookmarks');\n              const data = await response.json();\n              document.getElementById('bookmarksList').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('bookmarksList').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function getCategories() {\n            try {\n              const response = await fetch('/api/categories');\n              const data = await response.json();\n              document.getElementById('categoriesList').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('categoriesList').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function createBookmark() {\n            try {\n              const title = document.getElementById('bookmarkTitle').value;\n              const url = document.getElementById('bookmarkUrl').value;\n              const category = document.getElementById('bookmarkCategory').value;\n              const description = document.getElementById('bookmarkDesc').value;\n\n              if (!title || !url || !category) {\n                alert('请填写标题、URL和分类ID');\n                return;\n              }\n\n              const response = await fetch('/api/bookmarks', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' },\n                body: JSON.stringify({\n                  title,\n                  url,\n                  category,\n                  description,\n                  shortDesc: description\n                })\n              });\n              const data = await response.json();\n              document.getElementById('createBookmarkResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('createBookmarkResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function importBookmarks() {\n            try {\n              const fileInput = document.getElementById('bookmarkFile');\n              const file = fileInput.files[0];\n\n              if (!file) {\n                alert('请选择书签文件');\n                return;\n              }\n\n              const content = await file.text();\n              const createCategories = document.getElementById('createCategories').checked;\n              const skipDuplicates = document.getElementById('skipDuplicates').checked;\n\n              // 检测文件类型\n              const fileName = file.name.toLowerCase();\n              let format = 'auto';\n              if (fileName.endsWith('.json')) {\n                format = 'json';\n              } else if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {\n                format = 'html';\n              }\n\n              const response = await fetch('/api/import/bookmarks', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' },\n                body: JSON.stringify({\n                  content,\n                  options: {\n                    createCategories,\n                    skipDuplicates,\n                    format\n                  }\n                })\n              });\n              const data = await response.json();\n              document.getElementById('importResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('importResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function exportChromeBookmarks() {\n            try {\n              const response = await fetch('/api/export/chrome');\n              if (response.ok) {\n                const blob = await response.blob();\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = 'cloudnav-bookmarks.json';\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                URL.revokeObjectURL(url);\n                document.getElementById('exportResult').textContent = '✅ Chrome 格式书签导出成功';\n              } else {\n                const error = await response.json();\n                document.getElementById('exportResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);\n              }\n            } catch (error) {\n              document.getElementById('exportResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function exportNetscapeBookmarks() {\n            try {\n              const response = await fetch('/api/export/netscape');\n              if (response.ok) {\n                const blob = await response.blob();\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = 'cloudnav-bookmarks.html';\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                URL.revokeObjectURL(url);\n                document.getElementById('exportResult').textContent = '✅ HTML 格式书签导出成功';\n              } else {\n                const error = await response.json();\n                document.getElementById('exportResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);\n              }\n            } catch (error) {\n              document.getElementById('exportResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function getImportStats() {\n            try {\n              const response = await fetch('/api/import/stats');\n              const data = await response.json();\n              document.getElementById('exportResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('exportResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function analyzeWithAI() {\n            try {\n              const options = {\n                enableCategoryRecommendations: document.getElementById('enableCategoryRecommendations').checked,\n                enableDuplicateDetection: document.getElementById('enableDuplicateDetection').checked,\n                enableDescriptionGeneration: document.getElementById('enableDescriptionGeneration').checked,\n                enableTagSuggestions: document.getElementById('enableTagSuggestions').checked,\n                enableNewCategoryCreation: document.getElementById('enableNewCategoryCreation').checked,\n                confidenceThreshold: parseFloat(document.getElementById('confidenceThreshold').value)\n              };\n\n              const response = await fetch('/api/ai/analyze', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' },\n                body: JSON.stringify(options)\n              });\n              const data = await response.json();\n\n              // 格式化显示结果\n              if (data.success && data.data) {\n                const result = data.data;\n                const summary = {\n                  message: data.message,\n                  statistics: result.statistics,\n                  suggestions: {\n                    categoryRecommendations: result.suggestions.categoryRecommendations.length,\n                    duplicateDetection: result.suggestions.duplicateDetection.length,\n                    descriptionSuggestions: result.suggestions.descriptionSuggestions.length,\n                    tagSuggestions: result.suggestions.tagSuggestions.length,\n                    newCategorySuggestions: result.suggestions.newCategorySuggestions.length\n                  },\n                  sampleSuggestions: {\n                    categoryRecommendations: result.suggestions.categoryRecommendations.slice(0, 3),\n                    duplicates: result.suggestions.duplicateDetection.slice(0, 2),\n                    descriptions: result.suggestions.descriptionSuggestions.slice(0, 3),\n                    tags: result.suggestions.tagSuggestions.slice(0, 3),\n                    newCategories: result.suggestions.newCategorySuggestions.slice(0, 2)\n                  }\n                };\n                document.getElementById('aiResult').textContent = JSON.stringify(summary, null, 2);\n              } else {\n                document.getElementById('aiResult').textContent = JSON.stringify(data, null, 2);\n              }\n            } catch (error) {\n              document.getElementById('aiResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function getAIConfig() {\n            try {\n              const response = await fetch('/api/ai/config');\n              const data = await response.json();\n              document.getElementById('aiResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('aiResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function getDetailedStats() {\n            try {\n              const response = await fetch('/api/statistics/detailed');\n              const data = await response.json();\n\n              // 格式化显示统计数据\n              if (data.success && data.data) {\n                const stats = data.data;\n                const summary = {\n                  overview: stats.overview,\n                  topBookmarks: stats.bookmarkStats.topBookmarks.slice(0, 5),\n                  topCategories: stats.categoryStats.topCategories.slice(0, 5),\n                  topSearches: stats.searchStats.topSearches.slice(0, 10),\n                  deviceStats: stats.deviceStats,\n                  recentTrends: {\n                    dailyStats: Object.entries(stats.timeStats.dailyStats).slice(-7),\n                    weeklyTrend: stats.timeStats.weeklyTrend.slice(-4),\n                    monthlyTrend: stats.timeStats.monthlyTrend.slice(-6)\n                  }\n                };\n                document.getElementById('statsResult').textContent = JSON.stringify(summary, null, 2);\n              } else {\n                document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);\n              }\n            } catch (error) {\n              document.getElementById('statsResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function recordPageView() {\n            try {\n              const response = await fetch('/api/statistics/page-view', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' }\n              });\n              const data = await response.json();\n              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('statsResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function recordSearch() {\n            const query = prompt('请输入搜索关键词:');\n            if (!query) return;\n\n            try {\n              const response = await fetch('/api/statistics/search', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' },\n                body: JSON.stringify({ query })\n              });\n              const data = await response.json();\n              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('statsResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function exportStats() {\n            try {\n              const response = await fetch('/api/statistics/export');\n              if (response.ok) {\n                const blob = await response.blob();\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = 'cloudnav-statistics.json';\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                URL.revokeObjectURL(url);\n                document.getElementById('statsResult').textContent = '✅ 统计数据导出成功';\n              } else {\n                const error = await response.json();\n                document.getElementById('statsResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);\n              }\n            } catch (error) {\n              document.getElementById('statsResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function resetStats() {\n            if (!confirm('确定要重置所有统计数据吗？此操作不可恢复！')) {\n              return;\n            }\n\n            try {\n              const response = await fetch('/api/statistics/reset', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' }\n              });\n              const data = await response.json();\n              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('statsResult').textContent = 'Error: ' + error.message;\n            }\n          }\n\n          async function clearAllData() {\n            if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {\n              return;\n            }\n\n            try {\n              const response = await fetch('/api/migration/clear', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' }\n              });\n              const data = await response.json();\n              document.getElementById('clearDataResult').textContent = JSON.stringify(data, null, 2);\n            } catch (error) {\n              document.getElementById('clearDataResult').textContent = 'Error: ' + error.message;\n            }\n          }\n        </script>\n      </body>\n    </html>\n  `;\n\n  return c.html(html);\n});\n\nexport { app as apiRoutes };\n", "// CloudNav 2.0 - 现代化主页组件\nimport type { Bookmark, Category, Config } from '@/types';\n\ninterface HomePageProps {\n  bookmarks: Bookmark[];\n  categories: Category[];\n  config: Config;\n  searchQuery?: string;\n  currentCategory?: Category;\n}\n\nexport function HomePage({ bookmarks, categories, config, searchQuery, currentCategory }: HomePageProps): string {\n  // 过滤书签\n  let filteredBookmarks = bookmarks;\n  \n  if (searchQuery) {\n    filteredBookmarks = bookmarks.filter(bookmark => \n      bookmark.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      bookmark.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      bookmark.shortDesc?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      bookmark.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))\n    );\n  }\n  \n  if (currentCategory) {\n    filteredBookmarks = filteredBookmarks.filter(bookmark => bookmark.category === currentCategory.id);\n  }\n\n  // 按分类分组书签\n  const bookmarksByCategory = categories.map(category => ({\n    category,\n    bookmarks: filteredBookmarks.filter(bookmark => bookmark.category === category.id)\n  })).filter(group => group.bookmarks.length > 0);\n\n  // 热门书签（按点击量排序）\n  const popularBookmarks = [...filteredBookmarks]\n    .sort((a, b) => b.clickCount - a.clickCount)\n    .slice(0, 6);\n\n  // 最近添加的书签\n  const recentBookmarks = [...filteredBookmarks]\n    .sort((a, b) => b.createdAt - a.createdAt)\n    .slice(0, 6);\n\n  return `\n    <div class=\"homepage\">\n      <!-- 现代化头部 -->\n      <header class=\"hero-section\">\n        <div class=\"hero-content\">\n          <h1 class=\"hero-title\">\n            <span class=\"hero-icon\">🌟</span>\n            ${config.siteName}\n          </h1>\n          <p class=\"hero-subtitle\">${config.siteDescription}</p>\n          \n          <!-- 增强搜索栏 -->\n          <div class=\"search-container\">\n            <div class=\"search-wrapper\">\n              <span class=\"search-icon\">🔍</span>\n              <input \n                type=\"text\" \n                id=\"searchInput\" \n                placeholder=\"搜索书签、分类或标签...\" \n                value=\"${searchQuery || ''}\"\n                class=\"search-input\"\n                autocomplete=\"off\"\n              />\n              <button onclick=\"clearSearch()\" class=\"search-clear ${searchQuery ? 'visible' : ''}\" title=\"清除搜索\">\n                <span>✕</span>\n              </button>\n            </div>\n            <button onclick=\"performSearch()\" class=\"search-button\">\n              搜索\n            </button>\n          </div>\n          \n          <!-- 快速统计 -->\n          <div class=\"quick-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-number\">${bookmarks.length}</span>\n              <span class=\"stat-label\">个书签</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-number\">${categories.length}</span>\n              <span class=\"stat-label\">个分类</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-number\">${filteredBookmarks.length}</span>\n              <span class=\"stat-label\">个结果</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <!-- 主要内容区域 -->\n      <main class=\"main-content\">\n        <div class=\"container\">\n          <!-- 分类导航 -->\n          <nav class=\"category-nav\">\n            <div class=\"category-nav-header\">\n              <h2>📁 分类导航</h2>\n              <button onclick=\"toggleCategoryNav()\" class=\"category-toggle\">\n                <span class=\"toggle-icon\">▼</span>\n              </button>\n            </div>\n            <div class=\"category-list\" id=\"categoryList\">\n              <a href=\"/\" class=\"category-item ${!currentCategory ? 'active' : ''}\">\n                <span class=\"category-icon\">🏠</span>\n                <span class=\"category-name\">全部书签</span>\n                <span class=\"category-count\">${bookmarks.length}</span>\n              </a>\n              ${categories.map(category => `\n                <a href=\"/?category=${category.id}\" class=\"category-item ${currentCategory?.id === category.id ? 'active' : ''}\">\n                  <span class=\"category-icon\">${category.icon || '📂'}</span>\n                  <span class=\"category-name\">${category.name}</span>\n                  <span class=\"category-count\">${bookmarks.filter(b => b.category === category.id).length}</span>\n                </a>\n              `).join('')}\n            </div>\n          </nav>\n\n          <!-- 书签内容区域 -->\n          <div class=\"content-area\">\n            ${searchQuery ? `\n              <!-- 搜索结果 -->\n              <section class=\"search-results\">\n                <div class=\"section-header\">\n                  <h2>🔍 搜索结果</h2>\n                  <p class=\"search-info\">找到 ${filteredBookmarks.length} 个相关书签</p>\n                </div>\n                <div class=\"bookmarks-grid\">\n                  ${filteredBookmarks.map(bookmark => renderBookmarkCard(bookmark, categories)).join('')}\n                </div>\n              </section>\n            ` : currentCategory ? `\n              <!-- 分类书签 -->\n              <section class=\"category-bookmarks\">\n                <div class=\"section-header\">\n                  <h2>\n                    <span class=\"category-icon\">${currentCategory.icon || '📂'}</span>\n                    ${currentCategory.name}\n                  </h2>\n                  <p class=\"category-description\">${currentCategory.description || ''}</p>\n                </div>\n                <div class=\"bookmarks-grid\">\n                  ${filteredBookmarks.map(bookmark => renderBookmarkCard(bookmark, categories)).join('')}\n                </div>\n              </section>\n            ` : `\n              <!-- 热门书签 -->\n              ${popularBookmarks.length > 0 ? `\n                <section class=\"popular-bookmarks\">\n                  <div class=\"section-header\">\n                    <h2>🔥 热门书签</h2>\n                    <p class=\"section-subtitle\">最受欢迎的书签</p>\n                  </div>\n                  <div class=\"bookmarks-grid\">\n                    ${popularBookmarks.map(bookmark => renderBookmarkCard(bookmark, categories, true)).join('')}\n                  </div>\n                </section>\n              ` : ''}\n\n              <!-- 最近添加 -->\n              ${recentBookmarks.length > 0 ? `\n                <section class=\"recent-bookmarks\">\n                  <div class=\"section-header\">\n                    <h2>⭐ 最近添加</h2>\n                    <p class=\"section-subtitle\">新添加的书签</p>\n                  </div>\n                  <div class=\"bookmarks-grid\">\n                    ${recentBookmarks.map(bookmark => renderBookmarkCard(bookmark, categories)).join('')}\n                  </div>\n                </section>\n              ` : ''}\n\n              <!-- 按分类显示 -->\n              ${bookmarksByCategory.map(group => `\n                <section class=\"category-section\">\n                  <div class=\"section-header\">\n                    <h2>\n                      <span class=\"category-icon\">${group.category.icon || '📂'}</span>\n                      ${group.category.name}\n                    </h2>\n                    <a href=\"/?category=${group.category.id}\" class=\"view-all-link\">\n                      查看全部 ${group.bookmarks.length} 个 →\n                    </a>\n                  </div>\n                  <div class=\"bookmarks-grid\">\n                    ${group.bookmarks.slice(0, 6).map(bookmark => renderBookmarkCard(bookmark, categories)).join('')}\n                  </div>\n                </section>\n              `).join('')}\n            `}\n\n            <!-- 空状态 -->\n            ${filteredBookmarks.length === 0 ? `\n              <div class=\"empty-state\">\n                <div class=\"empty-icon\">📭</div>\n                <h3>没有找到书签</h3>\n                <p>${searchQuery ? '尝试使用不同的关键词搜索' : '还没有添加任何书签'}</p>\n                ${!searchQuery ? `\n                  <div class=\"empty-actions\">\n                    <a href=\"/admin\" class=\"btn btn-primary\">添加书签</a>\n                    <a href=\"/api/test\" class=\"btn btn-secondary\">导入书签</a>\n                  </div>\n                ` : ''}\n              </div>\n            ` : ''}\n          </div>\n        </div>\n      </main>\n\n      <!-- 浮动操作按钮 -->\n      <div class=\"fab-container\">\n        <button onclick=\"scrollToTop()\" class=\"fab fab-scroll\" title=\"回到顶部\">\n          <span>↑</span>\n        </button>\n        <button onclick=\"toggleTheme()\" class=\"fab fab-theme\" title=\"切换主题\">\n          <span>🌙</span>\n        </button>\n        <a href=\"/admin\" class=\"fab fab-admin\" title=\"管理面板\">\n          <span>⚙️</span>\n        </a>\n      </div>\n\n      <!-- 现代化样式 -->\n      <style>\n        :root {\n          --primary-color: #7C3AED;\n          --primary-light: #A855F7;\n          --primary-dark: #5B21B6;\n          --secondary-color: #3B82F6;\n          --success-color: #10B981;\n          --warning-color: #F59E0B;\n          --error-color: #EF4444;\n          --text-primary: #1F2937;\n          --text-secondary: #6B7280;\n          --text-muted: #9CA3AF;\n          --bg-primary: #FFFFFF;\n          --bg-secondary: #F9FAFB;\n          --bg-tertiary: #F3F4F6;\n          --border-color: #E5E7EB;\n          --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n          --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n          --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\n          --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);\n          --radius-sm: 0.375rem;\n          --radius-md: 0.5rem;\n          --radius-lg: 0.75rem;\n          --radius-xl: 1rem;\n        }\n\n        * {\n          margin: 0;\n          padding: 0;\n          box-sizing: border-box;\n        }\n\n        body {\n          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n          line-height: 1.6;\n          color: var(--text-primary);\n          background: var(--bg-secondary);\n        }\n\n        .homepage {\n          min-height: 100vh;\n        }\n\n        /* 英雄区域 */\n        .hero-section {\n          background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);\n          color: white;\n          padding: 4rem 2rem 3rem;\n          text-align: center;\n        }\n\n        .hero-content {\n          max-width: 800px;\n          margin: 0 auto;\n        }\n\n        .hero-title {\n          font-size: 3rem;\n          font-weight: 700;\n          margin-bottom: 1rem;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 1rem;\n        }\n\n        .hero-icon {\n          font-size: 3.5rem;\n        }\n\n        .hero-subtitle {\n          font-size: 1.25rem;\n          opacity: 0.9;\n          margin-bottom: 2rem;\n        }\n\n        /* 搜索容器 */\n        .search-container {\n          display: flex;\n          gap: 1rem;\n          max-width: 600px;\n          margin: 0 auto 2rem;\n        }\n\n        .search-wrapper {\n          flex: 1;\n          position: relative;\n          display: flex;\n          align-items: center;\n          background: white;\n          border-radius: var(--radius-lg);\n          padding: 0 1rem;\n          box-shadow: var(--shadow-lg);\n        }\n\n        .search-icon {\n          color: var(--text-muted);\n          margin-right: 0.75rem;\n        }\n\n        .search-input {\n          flex: 1;\n          border: none;\n          outline: none;\n          padding: 1rem 0;\n          font-size: 1rem;\n          color: var(--text-primary);\n        }\n\n        .search-clear {\n          background: none;\n          border: none;\n          color: var(--text-muted);\n          cursor: pointer;\n          padding: 0.25rem;\n          border-radius: 50%;\n          opacity: 0;\n          transition: all 0.2s;\n        }\n\n        .search-clear.visible {\n          opacity: 1;\n        }\n\n        .search-clear:hover {\n          background: var(--bg-tertiary);\n          color: var(--text-primary);\n        }\n\n        .search-button {\n          background: white;\n          color: var(--primary-color);\n          border: none;\n          padding: 1rem 2rem;\n          border-radius: var(--radius-lg);\n          font-weight: 600;\n          cursor: pointer;\n          transition: all 0.2s;\n          box-shadow: var(--shadow-lg);\n        }\n\n        .search-button:hover {\n          background: var(--bg-secondary);\n          transform: translateY(-1px);\n        }\n\n        /* 快速统计 */\n        .quick-stats {\n          display: flex;\n          justify-content: center;\n          gap: 2rem;\n          margin-top: 1rem;\n        }\n\n        .stat-item {\n          text-align: center;\n        }\n\n        .stat-number {\n          display: block;\n          font-size: 2rem;\n          font-weight: 700;\n        }\n\n        .stat-label {\n          font-size: 0.875rem;\n          opacity: 0.8;\n        }\n\n        /* 主要内容 */\n        .main-content {\n          padding: 2rem 0;\n        }\n\n        .container {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 2rem;\n          display: grid;\n          grid-template-columns: 280px 1fr;\n          gap: 2rem;\n        }\n\n        /* 分类导航 */\n        .category-nav {\n          background: var(--bg-primary);\n          border-radius: var(--radius-xl);\n          padding: 1.5rem;\n          box-shadow: var(--shadow-md);\n          height: fit-content;\n          position: sticky;\n          top: 2rem;\n        }\n\n        .category-nav-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 1rem;\n        }\n\n        .category-nav-header h2 {\n          font-size: 1.125rem;\n          color: var(--text-primary);\n        }\n\n        .category-toggle {\n          background: none;\n          border: none;\n          cursor: pointer;\n          padding: 0.25rem;\n          color: var(--text-muted);\n        }\n\n        .category-list {\n          display: flex;\n          flex-direction: column;\n          gap: 0.5rem;\n        }\n\n        .category-item {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          padding: 0.75rem;\n          border-radius: var(--radius-md);\n          text-decoration: none;\n          color: var(--text-secondary);\n          transition: all 0.2s;\n        }\n\n        .category-item:hover {\n          background: var(--bg-secondary);\n          color: var(--text-primary);\n        }\n\n        .category-item.active {\n          background: var(--primary-color);\n          color: white;\n        }\n\n        .category-name {\n          flex: 1;\n        }\n\n        .category-count {\n          background: var(--bg-tertiary);\n          color: var(--text-muted);\n          padding: 0.25rem 0.5rem;\n          border-radius: var(--radius-sm);\n          font-size: 0.75rem;\n        }\n\n        .category-item.active .category-count {\n          background: rgba(255, 255, 255, 0.2);\n          color: white;\n        }\n\n        /* 内容区域 */\n        .content-area {\n          display: flex;\n          flex-direction: column;\n          gap: 3rem;\n        }\n\n        .section-header {\n          margin-bottom: 1.5rem;\n        }\n\n        .section-header h2 {\n          font-size: 1.5rem;\n          color: var(--text-primary);\n          margin-bottom: 0.5rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .section-subtitle {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n        }\n\n        .view-all-link {\n          color: var(--primary-color);\n          text-decoration: none;\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n\n        .view-all-link:hover {\n          text-decoration: underline;\n        }\n\n        /* 书签网格 */\n        .bookmarks-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n          gap: 1.5rem;\n        }\n\n        /* 空状态 */\n        .empty-state {\n          text-align: center;\n          padding: 4rem 2rem;\n          color: var(--text-secondary);\n        }\n\n        .empty-icon {\n          font-size: 4rem;\n          margin-bottom: 1rem;\n        }\n\n        .empty-state h3 {\n          font-size: 1.5rem;\n          margin-bottom: 0.5rem;\n          color: var(--text-primary);\n        }\n\n        .empty-actions {\n          margin-top: 2rem;\n          display: flex;\n          gap: 1rem;\n          justify-content: center;\n        }\n\n        .btn {\n          padding: 0.75rem 1.5rem;\n          border-radius: var(--radius-md);\n          text-decoration: none;\n          font-weight: 500;\n          transition: all 0.2s;\n        }\n\n        .btn-primary {\n          background: var(--primary-color);\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: var(--primary-dark);\n        }\n\n        .btn-secondary {\n          background: var(--bg-primary);\n          color: var(--text-primary);\n          border: 1px solid var(--border-color);\n        }\n\n        .btn-secondary:hover {\n          background: var(--bg-secondary);\n        }\n\n        /* 浮动操作按钮 */\n        .fab-container {\n          position: fixed;\n          bottom: 2rem;\n          right: 2rem;\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n          z-index: 1000;\n        }\n\n        .fab {\n          width: 56px;\n          height: 56px;\n          border-radius: 50%;\n          border: none;\n          background: var(--primary-color);\n          color: white;\n          font-size: 1.25rem;\n          cursor: pointer;\n          box-shadow: var(--shadow-lg);\n          transition: all 0.2s;\n          text-decoration: none;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .fab:hover {\n          background: var(--primary-dark);\n          transform: translateY(-2px);\n          box-shadow: var(--shadow-xl);\n        }\n\n        /* 书签卡片样式 */\n        .bookmark-card {\n          background: var(--bg-primary);\n          border-radius: var(--radius-lg);\n          box-shadow: var(--shadow-md);\n          transition: all 0.2s;\n          overflow: hidden;\n          position: relative;\n        }\n\n        .bookmark-card:hover {\n          transform: translateY(-2px);\n          box-shadow: var(--shadow-lg);\n        }\n\n        .bookmark-card.popular {\n          border: 2px solid var(--warning-color);\n        }\n\n        .bookmark-link {\n          display: block;\n          text-decoration: none;\n          color: inherit;\n          padding: 1.5rem;\n        }\n\n        .bookmark-header {\n          display: flex;\n          align-items: flex-start;\n          gap: 1rem;\n          margin-bottom: 1rem;\n          position: relative;\n        }\n\n        .bookmark-icon {\n          width: 48px;\n          height: 48px;\n          border-radius: var(--radius-md);\n          object-fit: cover;\n          flex-shrink: 0;\n        }\n\n        .bookmark-icon-placeholder {\n          width: 48px;\n          height: 48px;\n          background: var(--bg-tertiary);\n          border-radius: var(--radius-md);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 1.5rem;\n          flex-shrink: 0;\n        }\n\n        .bookmark-meta {\n          flex: 1;\n          min-width: 0;\n        }\n\n        .bookmark-title {\n          font-size: 1.125rem;\n          font-weight: 600;\n          color: var(--text-primary);\n          margin-bottom: 0.25rem;\n          line-height: 1.4;\n          display: -webkit-box;\n          -webkit-line-clamp: 2;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n        }\n\n        .bookmark-url {\n          color: var(--text-muted);\n          font-size: 0.875rem;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .popular-badge {\n          position: absolute;\n          top: -0.5rem;\n          right: -0.5rem;\n          background: var(--warning-color);\n          color: white;\n          width: 32px;\n          height: 32px;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 1rem;\n          box-shadow: var(--shadow-md);\n        }\n\n        .bookmark-description {\n          color: var(--text-secondary);\n          font-size: 0.875rem;\n          line-height: 1.5;\n          margin-bottom: 1rem;\n          display: -webkit-box;\n          -webkit-line-clamp: 2;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n        }\n\n        .bookmark-footer {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 1rem;\n        }\n\n        .bookmark-category {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          background: var(--bg-secondary);\n          padding: 0.25rem 0.75rem;\n          border-radius: var(--radius-sm);\n          font-size: 0.75rem;\n          color: var(--text-secondary);\n        }\n\n        .bookmark-stats {\n          font-size: 0.75rem;\n          color: var(--text-muted);\n        }\n\n        .bookmark-tags {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 0.5rem;\n        }\n\n        .tag {\n          background: var(--primary-color);\n          color: white;\n          padding: 0.25rem 0.5rem;\n          border-radius: var(--radius-sm);\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .tag-more {\n          background: var(--bg-tertiary);\n          color: var(--text-muted);\n          padding: 0.25rem 0.5rem;\n          border-radius: var(--radius-sm);\n          font-size: 0.75rem;\n        }\n\n        /* 暗色主题 */\n        .dark-theme {\n          --text-primary: #F9FAFB;\n          --text-secondary: #D1D5DB;\n          --text-muted: #9CA3AF;\n          --bg-primary: #1F2937;\n          --bg-secondary: #111827;\n          --bg-tertiary: #374151;\n          --border-color: #374151;\n        }\n\n        .dark-theme .hero-section {\n          background: linear-gradient(135deg, #1F2937 0%, #374151 100%);\n        }\n\n        .dark-theme .search-wrapper,\n        .dark-theme .search-button {\n          background: var(--bg-primary);\n          color: var(--text-primary);\n        }\n\n        .dark-theme .search-input {\n          color: var(--text-primary);\n        }\n\n        .dark-theme .search-input::placeholder {\n          color: var(--text-muted);\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .hero-title {\n            font-size: 2rem;\n          }\n\n          .hero-icon {\n            font-size: 2.5rem;\n          }\n\n          .container {\n            grid-template-columns: 1fr;\n            gap: 1rem;\n          }\n\n          .category-nav {\n            position: static;\n          }\n\n          .search-container {\n            flex-direction: column;\n          }\n\n          .quick-stats {\n            gap: 1rem;\n          }\n\n          .bookmarks-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .fab-container {\n            bottom: 1rem;\n            right: 1rem;\n          }\n        }\n      </style>\n\n      <!-- JavaScript -->\n      <script>\n        // 搜索功能\n        function performSearch() {\n          const query = document.getElementById('searchInput').value.trim();\n          if (query) {\n            window.location.href = \\`/?search=\\${encodeURIComponent(query)}\\`;\n          }\n        }\n\n        function clearSearch() {\n          document.getElementById('searchInput').value = '';\n          window.location.href = '/';\n        }\n\n        // 回车搜索\n        document.getElementById('searchInput').addEventListener('keypress', function(e) {\n          if (e.key === 'Enter') {\n            performSearch();\n          }\n        });\n\n        // 分类导航切换\n        function toggleCategoryNav() {\n          const categoryList = document.getElementById('categoryList');\n          const toggleIcon = document.querySelector('.toggle-icon');\n          \n          if (categoryList.style.display === 'none') {\n            categoryList.style.display = 'flex';\n            toggleIcon.textContent = '▼';\n          } else {\n            categoryList.style.display = 'none';\n            toggleIcon.textContent = '▶';\n          }\n        }\n\n        // 回到顶部\n        function scrollToTop() {\n          window.scrollTo({ top: 0, behavior: 'smooth' });\n        }\n\n        // 主题切换\n        function toggleTheme() {\n          document.body.classList.toggle('dark-theme');\n          const isDark = document.body.classList.contains('dark-theme');\n          localStorage.setItem('theme', isDark ? 'dark' : 'light');\n          \n          // 更新图标\n          const themeIcon = document.querySelector('.fab-theme span');\n          themeIcon.textContent = isDark ? '☀️' : '🌙';\n        }\n\n        // 加载保存的主题\n        document.addEventListener('DOMContentLoaded', function() {\n          const savedTheme = localStorage.getItem('theme');\n          if (savedTheme === 'dark') {\n            document.body.classList.add('dark-theme');\n            document.querySelector('.fab-theme span').textContent = '☀️';\n          }\n        });\n\n        // 书签点击统计\n        function trackBookmarkClick(bookmarkId) {\n          fetch('/api/bookmarks/' + bookmarkId + '/click', {\n            method: 'POST'\n          }).catch(console.error);\n        }\n      </script>\n    </div>\n  `;\n}\n\n// 渲染书签卡片的辅助函数\nfunction renderBookmarkCard(bookmark: Bookmark, categories: Category[], showPopular = false): string {\n  const category = categories.find(c => c.id === bookmark.category);\n  \n  return `\n    <div class=\"bookmark-card ${showPopular ? 'popular' : ''}\" data-bookmark-id=\"${bookmark.id}\">\n      <a href=\"${bookmark.url}\" target=\"_blank\" onclick=\"trackBookmarkClick('${bookmark.id}')\" class=\"bookmark-link\">\n        <div class=\"bookmark-header\">\n          ${bookmark.icon ? `\n            <img src=\"${bookmark.icon}\" alt=\"\" class=\"bookmark-icon\" />\n          ` : `\n            <div class=\"bookmark-icon-placeholder\">🔗</div>\n          `}\n          <div class=\"bookmark-meta\">\n            <h3 class=\"bookmark-title\">${bookmark.title}</h3>\n            <p class=\"bookmark-url\">${new URL(bookmark.url).hostname}</p>\n          </div>\n          ${showPopular ? `\n            <div class=\"popular-badge\">🔥</div>\n          ` : ''}\n        </div>\n        \n        ${bookmark.shortDesc ? `\n          <p class=\"bookmark-description\">${bookmark.shortDesc}</p>\n        ` : ''}\n        \n        <div class=\"bookmark-footer\">\n          <div class=\"bookmark-category\">\n            <span class=\"category-icon\">${category?.icon || '📂'}</span>\n            <span class=\"category-name\">${category?.name || '未分类'}</span>\n          </div>\n          <div class=\"bookmark-stats\">\n            <span class=\"click-count\">${bookmark.clickCount} 次点击</span>\n          </div>\n        </div>\n        \n        ${bookmark.tags && bookmark.tags.length > 0 ? `\n          <div class=\"bookmark-tags\">\n            ${bookmark.tags.slice(0, 3).map(tag => `\n              <span class=\"tag\">${tag}</span>\n            `).join('')}\n            ${bookmark.tags.length > 3 ? `\n              <span class=\"tag-more\">+${bookmark.tags.length - 3}</span>\n            ` : ''}\n          </div>\n        ` : ''}\n      </a>\n    </div>\n  `;\n}\n", "// CloudNav 2.0 - 管理面板组件\nimport type { Bookmark, Category, Stats, Config } from '@/types';\n\ninterface AdminPanelProps {\n  bookmarks: Bookmark[];\n  categories: Category[];\n  stats: Stats;\n  config: Config;\n}\n\nexport const AdminPanel = ({ bookmarks, categories, stats, config }: AdminPanelProps): string => {\n  return `\n    <div class=\"admin-panel\">\n      {/* 管理面板头部 */}\n      <div class=\"admin-header\">\n        <div class=\"container\">\n          <div class=\"admin-nav\">\n            <h1>管理面板</h1>\n            <div class=\"admin-nav-links\">\n              <a href=\"/\" class=\"btn btn-outline\">返回首页</a>\n              <a href=\"/admin/stats\" class=\"btn btn-outline\">统计数据</a>\n              <a href=\"/admin/settings\" class=\"btn btn-outline\">系统设置</a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"container\">\n        {/* 概览卡片 */}\n        <div class=\"overview-grid\">\n          <div class=\"overview-card\">\n            <div class=\"overview-icon\">📚</div>\n            <div class=\"overview-content\">\n              <h3>书签总数</h3>\n              <div class=\"overview-number\">{bookmarks.length}</div>\n            </div>\n          </div>\n          \n          <div class=\"overview-card\">\n            <div class=\"overview-icon\">📁</div>\n            <div class=\"overview-content\">\n              <h3>分类总数</h3>\n              <div class=\"overview-number\">{categories.length}</div>\n            </div>\n          </div>\n          \n          <div class=\"overview-card\">\n            <div class=\"overview-icon\">👆</div>\n            <div class=\"overview-content\">\n              <h3>总点击量</h3>\n              <div class=\"overview-number\">{stats.totalClicks}</div>\n            </div>\n          </div>\n          \n          <div class=\"overview-card\">\n            <div class=\"overview-icon\">👀</div>\n            <div class=\"overview-content\">\n              <h3>总访问量</h3>\n              <div class=\"overview-number\">{stats.totalViews}</div>\n            </div>\n          </div>\n        </div>\n\n        {/* 快速操作 */}\n        <div class=\"quick-actions\">\n          <h2>快速操作</h2>\n          <div class=\"action-grid\">\n            <button onclick=\"showAddBookmarkModal()\" class=\"action-card\">\n              <div class=\"action-icon\">➕</div>\n              <div class=\"action-title\">添加书签</div>\n              <div class=\"action-desc\">添加新的书签到收藏</div>\n            </button>\n            \n            <button onclick=\"showAddCategoryModal()\" class=\"action-card\">\n              <div class=\"action-icon\">📁</div>\n              <div class=\"action-title\">添加分类</div>\n              <div class=\"action-desc\">创建新的书签分类</div>\n            </button>\n            \n            <button onclick=\"showImportModal()\" class=\"action-card\">\n              <div class=\"action-icon\">📥</div>\n              <div class=\"action-title\">导入书签</div>\n              <div class=\"action-desc\">从 Chrome 导入书签</div>\n            </button>\n            \n            <button onclick=\"exportBookmarks()\" class=\"action-card\">\n              <div class=\"action-icon\">📤</div>\n              <div class=\"action-title\">导出书签</div>\n              <div class=\"action-desc\">导出为 Chrome 格式</div>\n            </button>\n            \n            ${config.aiConfig.enabled ? `\n              <button onclick=\"organizeWithAI()\" class=\"action-card\">\n                <div class=\"action-icon\">🤖</div>\n                <div class=\"action-title\">AI 整理</div>\n                <div class=\"action-desc\">使用 AI 智能整理书签</div>\n              </button>\n            ` : ''}\n            \n            <button onclick=\"backupData()\" class=\"action-card\">\n              <div class=\"action-icon\">💾</div>\n              <div class=\"action-title\">备份数据</div>\n              <div class=\"action-desc\">备份所有数据</div>\n            </button>\n          </div>\n        </div>\n\n        {/* 书签管理 */}\n        <div class=\"bookmarks-section\">\n          <div class=\"section-header\">\n            <h2>书签管理</h2>\n            <div class=\"section-actions\">\n              <input type=\"text\" id=\"bookmarkSearch\" placeholder=\"搜索书签...\" class=\"search-input\" />\n              <select id=\"categoryFilter\" class=\"filter-select\">\n                <option value=\"\">所有分类</option>\n                ${categories.map(category => `\n                  <option value=\"${category.id}\">${category.name}</option>\n                `).join('')}\n              </select>\n            </div>\n          </div>\n          \n          <div class=\"bookmarks-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>标题</th>\n                  <th>URL</th>\n                  <th>分类</th>\n                  <th>点击量</th>\n                  <th>操作</th>\n                </tr>\n              </thead>\n              <tbody id=\"bookmarksTableBody\">\n                ${bookmarks.map(bookmark => {\n                  const category = categories.find(cat => cat.id === bookmark.category);\n                  return `\n                    <tr data-bookmark-id=\"${bookmark.id}\" data-category=\"${bookmark.category}\">\n                      <td>\n                        <div class=\"bookmark-cell\">\n                          ${bookmark.icon ? `<img src=\"${bookmark.icon}\" alt=\"\" class=\"bookmark-icon-small\" />` : ''}\n                          <div>\n                            <div class=\"bookmark-title\">${bookmark.title}</div>\n                            <div class=\"bookmark-desc\">${bookmark.shortDesc || ''}</div>\n                          </div>\n                        </div>\n                      </td>\n                      <td>\n                        <a href=\"${bookmark.url}\" target=\"_blank\" class=\"bookmark-url\">\n                          ${bookmark.url}\n                        </a>\n                      </td>\n                      <td>\n                        <span class=\"category-badge\">${category?.name || '未分类'}</span>\n                      </td>\n                      <td>${bookmark.clickCount}</td>\n                      <td>\n                        <div class=\"table-actions\">\n                          <button onclick=\"editBookmark('${bookmark.id}')\" class=\"btn-small btn-edit\">编辑</button>\n                          <button onclick=\"deleteBookmark('${bookmark.id}')\" class=\"btn-small btn-delete\">删除</button>\n                        </div>\n                      </td>\n                    </tr>\n                  `;\n                }).join('')}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* 分类管理 */}\n        <div class=\"categories-section\">\n          <div class=\"section-header\">\n            <h2>分类管理</h2>\n          </div>\n          \n          <div class=\"categories-grid\">\n            ${categories.map(category => `\n              <div class=\"category-card\" data-category-id=\"${category.id}\">\n                <div class=\"category-header\">\n                  <div class=\"category-info\">\n                    ${category.icon ? `<span class=\"category-icon\">${category.icon}</span>` : ''}\n                    <h3>${category.name}</h3>\n                  </div>\n                  <div class=\"category-actions\">\n                    <button onclick=\"editCategory('${category.id}')\" class=\"btn-small btn-edit\">编辑</button>\n                    <button onclick=\"deleteCategory('${category.id}')\" class=\"btn-small btn-delete\">删除</button>\n                  </div>\n                </div>\n\n                ${category.description ? `<p class=\"category-description\">${category.description}</p>` : ''}\n\n                <div class=\"category-stats\">\n                  <span class=\"category-count\">\n                    ${bookmarks.filter(b => b.category === category.id).length} 个书签\n                  </span>\n                  <span class=\"category-clicks\">\n                    ${stats.categoryStats[category.id] || 0} 次访问\n                  </span>\n                </div>\n              </div>\n            `).join('')}\n          </div>\n        </div>\n      </div>\n\n      {/* 模态框占位符 */}\n      <div id=\"modalContainer\"></div>\n\n      {/* 样式 */}\n      <style>\n        .admin-panel {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n        \n        .admin-header {\n          background: white;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n          margin-bottom: 30px;\n        }\n        \n        .admin-nav {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 20px 0;\n        }\n        \n        .admin-nav h1 {\n          color: #1e293b;\n          margin: 0;\n        }\n        \n        .admin-nav-links {\n          display: flex;\n          gap: 10px;\n        }\n        \n        .btn-outline {\n          background: transparent;\n          border: 2px solid #7C3AED;\n          color: #7C3AED;\n        }\n        \n        .btn-outline:hover {\n          background: #7C3AED;\n          color: white;\n        }\n        \n        .overview-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 20px;\n          margin-bottom: 40px;\n        }\n        \n        .overview-card {\n          background: white;\n          padding: 25px;\n          border-radius: 12px;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n          display: flex;\n          align-items: center;\n        }\n        \n        .overview-icon {\n          font-size: 2.5em;\n          margin-right: 20px;\n        }\n        \n        .overview-number {\n          font-size: 2em;\n          font-weight: bold;\n          color: #7C3AED;\n        }\n        \n        .quick-actions {\n          margin-bottom: 40px;\n        }\n        \n        .action-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 20px;\n          margin-top: 20px;\n        }\n        \n        .action-card {\n          background: white;\n          border: none;\n          padding: 25px;\n          border-radius: 12px;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n          cursor: pointer;\n          transition: all 0.3s ease;\n          text-align: center;\n        }\n        \n        .action-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n        }\n        \n        .action-icon {\n          font-size: 2.5em;\n          margin-bottom: 15px;\n        }\n        \n        .action-title {\n          font-size: 1.2em;\n          font-weight: 600;\n          margin-bottom: 8px;\n          color: #1e293b;\n        }\n        \n        .action-desc {\n          color: #64748b;\n          font-size: 0.9em;\n        }\n        \n        .section-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 20px;\n        }\n        \n        .section-actions {\n          display: flex;\n          gap: 10px;\n        }\n        \n        .search-input, .filter-select {\n          padding: 8px 12px;\n          border: 2px solid #e2e8f0;\n          border-radius: 6px;\n          font-size: 14px;\n        }\n        \n        .bookmarks-table {\n          background: white;\n          border-radius: 12px;\n          overflow: hidden;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        \n        .bookmarks-table table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n        \n        .bookmarks-table th,\n        .bookmarks-table td {\n          padding: 15px;\n          text-align: left;\n          border-bottom: 1px solid #e2e8f0;\n        }\n        \n        .bookmarks-table th {\n          background: #f8f9fa;\n          font-weight: 600;\n          color: #374151;\n        }\n        \n        .bookmark-cell {\n          display: flex;\n          align-items: center;\n          gap: 10px;\n        }\n        \n        .bookmark-icon-small {\n          width: 24px;\n          height: 24px;\n          border-radius: 4px;\n        }\n        \n        .bookmark-title {\n          font-weight: 500;\n          color: #1e293b;\n        }\n        \n        .bookmark-desc {\n          font-size: 0.85em;\n          color: #64748b;\n        }\n        \n        .bookmark-url {\n          color: #7C3AED;\n          text-decoration: none;\n          font-size: 0.9em;\n        }\n        \n        .category-badge {\n          background: #e0e7ff;\n          color: #3730a3;\n          padding: 4px 8px;\n          border-radius: 12px;\n          font-size: 0.8em;\n        }\n        \n        .table-actions {\n          display: flex;\n          gap: 5px;\n        }\n        \n        .btn-small {\n          padding: 4px 8px;\n          font-size: 0.8em;\n          border: none;\n          border-radius: 4px;\n          cursor: pointer;\n        }\n        \n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n        \n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n        \n        .categories-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n          gap: 20px;\n        }\n        \n        .category-card {\n          background: white;\n          padding: 20px;\n          border-radius: 12px;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        \n        .category-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 10px;\n        }\n        \n        .category-info {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n        }\n        \n        .category-info h3 {\n          margin: 0;\n          color: #1e293b;\n        }\n        \n        .category-description {\n          color: #64748b;\n          margin: 10px 0;\n        }\n        \n        .category-stats {\n          display: flex;\n          justify-content: space-between;\n          font-size: 0.9em;\n          color: #64748b;\n        }\n      </style>\n\n      {/* JavaScript */}\n      <script>\n        // 搜索和过滤功能\n        document.getElementById('bookmarkSearch').addEventListener('input', filterBookmarks);\n        document.getElementById('categoryFilter').addEventListener('change', filterBookmarks);\n        \n        function filterBookmarks() {\n          const searchTerm = document.getElementById('bookmarkSearch').value.toLowerCase();\n          const categoryFilter = document.getElementById('categoryFilter').value;\n          const rows = document.querySelectorAll('#bookmarksTableBody tr');\n          \n          rows.forEach(row => {\n            const title = row.querySelector('.bookmark-title').textContent.toLowerCase();\n            const url = row.querySelector('.bookmark-url').textContent.toLowerCase();\n            const category = row.dataset.category;\n            \n            const matchesSearch = title.includes(searchTerm) || url.includes(searchTerm);\n            const matchesCategory = !categoryFilter || category === categoryFilter;\n            \n            row.style.display = matchesSearch && matchesCategory ? '' : 'none';\n          });\n        }\n        \n        // 占位符函数，将在后续实现\n        function showAddBookmarkModal() { alert('添加书签功能即将实现'); }\n        function showAddCategoryModal() { alert('添加分类功能即将实现'); }\n        function showImportModal() { alert('导入功能即将实现'); }\n        function exportBookmarks() { alert('导出功能即将实现'); }\n        function organizeWithAI() { alert('AI整理功能即将实现'); }\n        function backupData() { alert('备份功能即将实现'); }\n        function editBookmark(id) { alert('编辑书签: ' + id); }\n        function deleteBookmark(id) { alert('删除书签: ' + id); }\n        function editCategory(id) { alert('编辑分类: ' + id); }\n        function deleteCategory(id) { alert('删除分类: ' + id); }\n      </script>\n    </div>\n  `;\n};\n", "// CloudNav 2.0 - 布局组件\n\ninterface LayoutProps {\n  title: string;\n  description: string;\n  children: string;\n}\n\nexport const Layout = ({ title, description, children }: LayoutProps): string => {\n  return `\n    <html lang=\"zh-CN\">\n      <head>\n        <meta charset=\"UTF-8\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n        <title>${title}</title>\n        <meta name=\"description\" content=\"${description}\" />\n\n        <!-- SEO Meta Tags -->\n        <meta property=\"og:title\" content=\"${title}\" />\n        <meta property=\"og:description\" content=\"${description}\" />\n        <meta property=\"og:type\" content=\"website\" />\n        <meta name=\"twitter:card\" content=\"summary\" />\n        <meta name=\"twitter:title\" content=\"${title}\" />\n        <meta name=\"twitter:description\" content=\"${description}\" />\n\n        <!-- Favicon -->\n        <link rel=\"icon\" type=\"image/svg+xml\" href=\"/favicon.svg\" />\n\n        <!-- 基础CSS框架 -->\n        <link href=\"https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css\" rel=\"stylesheet\">\n        \n        {/* 基础样式 */}\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n          \n          body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            background: #f8f9fa;\n          }\n          \n          .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            padding: 0 20px;\n          }\n          \n          .card {\n            background: white;\n            border-radius: 12px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            padding: 20px;\n            margin: 10px 0;\n            transition: all 0.3s ease;\n          }\n          \n          .card:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n          }\n          \n          .btn {\n            display: inline-block;\n            padding: 10px 20px;\n            background: #7C3AED;\n            color: white;\n            text-decoration: none;\n            border-radius: 8px;\n            border: none;\n            cursor: pointer;\n            transition: all 0.3s ease;\n          }\n          \n          .btn:hover {\n            background: #6D28D9;\n            transform: translateY(-1px);\n          }\n          \n          .grid {\n            display: grid;\n            gap: 20px;\n          }\n          \n          .grid-cols-1 { grid-template-columns: repeat(1, 1fr); }\n          .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }\n          .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }\n          .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }\n          \n          @media (max-width: 768px) {\n            .grid-cols-2, .grid-cols-3, .grid-cols-4 {\n              grid-template-columns: repeat(2, 1fr);\n            }\n          }\n          \n          @media (max-width: 480px) {\n            .grid-cols-2, .grid-cols-3, .grid-cols-4 {\n              grid-template-columns: 1fr;\n            }\n          }\n          \n          .header {\n            background: white;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            padding: 20px 0;\n            margin-bottom: 30px;\n          }\n          \n          .nav {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          }\n          \n          .logo {\n            font-size: 24px;\n            font-weight: bold;\n            color: #7C3AED;\n            text-decoration: none;\n          }\n          \n          .search-box {\n            flex: 1;\n            max-width: 400px;\n            margin: 0 20px;\n          }\n          \n          .search-input {\n            width: 100%;\n            padding: 12px 16px;\n            border: 2px solid #e2e8f0;\n            border-radius: 8px;\n            font-size: 16px;\n            transition: border-color 0.3s ease;\n          }\n          \n          .search-input:focus {\n            outline: none;\n            border-color: #7C3AED;\n          }\n          \n          .category-nav {\n            background: white;\n            border-radius: 12px;\n            padding: 20px;\n            margin-bottom: 30px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n          }\n          \n          .category-list {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 10px;\n          }\n          \n          .category-item {\n            padding: 8px 16px;\n            background: #f1f5f9;\n            border-radius: 20px;\n            text-decoration: none;\n            color: #64748b;\n            transition: all 0.3s ease;\n          }\n          \n          .category-item:hover,\n          .category-item.active {\n            background: #7C3AED;\n            color: white;\n          }\n          \n          .bookmark-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n            gap: 20px;\n          }\n          \n          .bookmark-card {\n            background: white;\n            border-radius: 12px;\n            padding: 20px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            transition: all 0.3s ease;\n            text-decoration: none;\n            color: inherit;\n          }\n          \n          .bookmark-card:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n          }\n          \n          .bookmark-icon {\n            width: 48px;\n            height: 48px;\n            border-radius: 8px;\n            margin-bottom: 12px;\n            object-fit: cover;\n          }\n          \n          .bookmark-title {\n            font-size: 18px;\n            font-weight: 600;\n            margin-bottom: 8px;\n            color: #1e293b;\n          }\n          \n          .bookmark-desc {\n            color: #64748b;\n            font-size: 14px;\n            line-height: 1.5;\n          }\n          \n          .error {\n            background: #fee2e2;\n            color: #dc2626;\n            padding: 20px;\n            border-radius: 8px;\n            text-align: center;\n          }\n          \n          .loading {\n            text-align: center;\n            padding: 40px;\n            color: #64748b;\n          }\n          \n          /* 暗色模式 */\n          @media (prefers-color-scheme: dark) {\n            body {\n              background: #0f172a;\n              color: #e2e8f0;\n            }\n            \n            .card, .header, .category-nav, .bookmark-card {\n              background: #1e293b;\n              color: #e2e8f0;\n            }\n            \n            .search-input {\n              background: #334155;\n              border-color: #475569;\n              color: #e2e8f0;\n            }\n            \n            .category-item {\n              background: #334155;\n              color: #94a3b8;\n            }\n            \n            .bookmark-title {\n              color: #f1f5f9;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        ${children}\n\n        <!-- 基础 JavaScript -->\n        <script>\n          // 搜索功能\n          function initSearch() {\n            const searchInput = document.querySelector('.search-input');\n            if (searchInput) {\n              searchInput.addEventListener('keypress', function(e) {\n                if (e.key === 'Enter') {\n                  const query = this.value.trim();\n                  if (query) {\n                    window.location.href = '/search?q=' + encodeURIComponent(query);\n                  }\n                }\n              });\n            }\n          }\n          \n          // 点击统计\n          function trackClick(bookmarkId) {\n            fetch('/api/stats/click', {\n              method: 'POST',\n              headers: { 'Content-Type': 'application/json' },\n              body: JSON.stringify({ bookmarkId })\n            }).catch(console.error);\n          }\n          \n          // 初始化\n          document.addEventListener('DOMContentLoaded', function() {\n            initSearch();\n          });\n        </script>\n      </body>\n    </html>\n  `;\n};\n", "// CloudNav 2.0 - 页面路由\nimport { Hono } from 'hono';\nimport type { HonoApp } from '@/types/hono';\nimport { KVService } from '@/services/kv';\nimport { HomePage } from '@/components/HomePage';\nimport { AdminPanel } from '@/components/AdminPanel';\nimport { Layout } from '@/components/Layout';\n\nconst app = new Hono<HonoApp>();\n\n// 主页（支持搜索和分类查询参数）\napp.get('/', async (c) => {\n  try {\n    const kvService = c.get('kvService') as KVService;\n    const searchQuery = c.req.query('search') || '';\n    const categoryId = c.req.query('category') || '';\n\n    // 获取数据\n    const [bookmarks, categories, config] = await Promise.all([\n      kvService.getBookmarks(),\n      kvService.getCategories(),\n      kvService.getConfig()\n    ]);\n\n    // 增加页面访问统计\n    const stats = await kvService.getStats();\n    await kvService.updateStats({\n      ...stats,\n      totalViews: stats.totalViews + 1\n    });\n\n    // 记录搜索统计\n    if (searchQuery) {\n      stats.searchStats[searchQuery] = (stats.searchStats[searchQuery] || 0) + 1;\n      await kvService.updateStats(stats);\n    }\n\n    // 找到当前分类\n    const currentCategory = categoryId ? categories.find(cat => cat.id === categoryId) : undefined;\n\n    // 渲染页面\n    const content = HomePage({\n      bookmarks,\n      categories,\n      config,\n      searchQuery: searchQuery || undefined,\n      currentCategory\n    });\n\n    let title = config.siteName;\n    let description = config.siteDescription;\n\n    if (searchQuery) {\n      title = `搜索: ${searchQuery} - ${config.siteName}`;\n      description = `搜索结果: ${searchQuery}`;\n    } else if (currentCategory) {\n      title = `${currentCategory.name} - ${config.siteName}`;\n      description = currentCategory.description || `${currentCategory.name} 分类下的书签`;\n    }\n\n    const html = Layout({\n      title,\n      description,\n      children: content\n    });\n\n    return c.html(html);\n  } catch (error) {\n    console.error('Homepage error:', error);\n    return c.html(Layout({\n      title: 'CloudNav - Error',\n      description: 'An error occurred',\n      children: '<div class=\"error\">服务暂时不可用，请稍后再试。</div>'\n    }));\n  }\n});\n\n// 搜索页面\napp.get('/search', async (c) => {\n  const query = c.req.query('q') || '';\n  const kvService = c.get('kvService') as KVService;\n  \n  try {\n    const [bookmarks, categories, config] = await Promise.all([\n      kvService.getBookmarks(),\n      kvService.getCategories(),\n      kvService.getConfig()\n    ]);\n\n    // 搜索逻辑\n    const filteredBookmarks = bookmarks.filter(bookmark => \n      bookmark.title.toLowerCase().includes(query.toLowerCase()) ||\n      bookmark.description?.toLowerCase().includes(query.toLowerCase()) ||\n      bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) ||\n      bookmark.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))\n    );\n\n    // 记录搜索统计\n    if (query) {\n      const stats = await kvService.getStats();\n      stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;\n      await kvService.updateStats(stats);\n    }\n\n    const content = HomePage({ \n      bookmarks: filteredBookmarks, \n      categories, \n      config,\n      searchQuery: query \n    });\n    \n    const html = Layout({ \n      title: `搜索: ${query} - ${config.siteName}`,\n      description: `搜索结果: ${query}`,\n      children: content \n    });\n\n    return c.html(html);\n  } catch (error) {\n    console.error('Search error:', error);\n    return c.html(Layout({ \n      title: 'CloudNav - Search Error',\n      description: 'Search error',\n      children: '<div class=\"error\">搜索服务暂时不可用。</div>'\n    }));\n  }\n});\n\n// 分类页面\napp.get('/category/:id', async (c) => {\n  const categoryId = c.req.param('id');\n  const kvService = c.get('kvService') as KVService;\n  \n  try {\n    const [bookmarks, categories, config] = await Promise.all([\n      kvService.getBookmarks(),\n      kvService.getCategories(),\n      kvService.getConfig()\n    ]);\n\n    const category = categories.find(cat => cat.id === categoryId);\n    if (!category) {\n      return c.html(Layout({ \n        title: 'Category Not Found',\n        description: 'Category not found',\n        children: '<div class=\"error\">分类不存在。</div>'\n      }), 404);\n    }\n\n    const categoryBookmarks = bookmarks.filter(bookmark => bookmark.category === categoryId);\n\n    // 更新分类统计\n    const stats = await kvService.getStats();\n    stats.categoryStats[categoryId] = (stats.categoryStats[categoryId] || 0) + 1;\n    await kvService.updateStats(stats);\n\n    const content = HomePage({ \n      bookmarks: categoryBookmarks, \n      categories, \n      config,\n      currentCategory: category \n    });\n    \n    const html = Layout({ \n      title: `${category.name} - ${config.siteName}`,\n      description: category.description || `${category.name} 分类下的书签`,\n      children: content \n    });\n\n    return c.html(html);\n  } catch (error) {\n    console.error('Category page error:', error);\n    return c.html(Layout({ \n      title: 'CloudNav - Category Error',\n      description: 'Category error',\n      children: '<div class=\"error\">分类页面加载失败。</div>'\n    }));\n  }\n});\n\n// 管理面板\napp.get('/admin', async (c) => {\n  try {\n    const kvService = c.get('kvService') as KVService;\n\n    // 获取数据\n    const [bookmarks, categories, stats, config] = await Promise.all([\n      kvService.getBookmarks(),\n      kvService.getCategories(),\n      kvService.getStats(),\n      kvService.getConfig()\n    ]);\n\n    // 渲染管理面板\n    const content = AdminPanel({ bookmarks, categories, stats, config });\n    const html = Layout({\n      title: `管理面板 - ${config.siteName}`,\n      description: '书签管理面板',\n      children: content\n    });\n\n    return c.html(html);\n  } catch (error) {\n    console.error('Admin panel error:', error);\n    return c.html(Layout({\n      title: 'CloudNav - Admin Error',\n      description: 'Admin error',\n      children: '<div class=\"error\">管理面板加载失败。</div>'\n    }));\n  }\n});\n\n// 关于页面\napp.get('/about', async (c) => {\n  const kvService = c.get('kvService') as KVService;\n  const config = await kvService.getConfig();\n\n  const aboutContent = `\n    <div class=\"about-page\">\n      <div class=\"about-container\">\n        <div class=\"about-header\">\n          <h1>关于 ${config.siteName}</h1>\n          <p class=\"about-description\">${config.siteDescription}</p>\n        </div>\n\n        <div class=\"about-content\">\n          <div class=\"features-section\">\n            <h2>✨ 功能特性</h2>\n            <div class=\"features-grid\">\n              <div class=\"feature-card\">\n                <div class=\"feature-icon\">🚀</div>\n                <h3>边缘计算</h3>\n                <p>基于 Cloudflare Workers 的全球边缘计算网络</p>\n              </div>\n              <div class=\"feature-card\">\n                <div class=\"feature-icon\">📊</div>\n                <h3>智能统计</h3>\n                <p>详细的访问统计和数据分析</p>\n              </div>\n              <div class=\"feature-card\">\n                <div class=\"feature-icon\">🤖</div>\n                <h3>AI 整理</h3>\n                <p>智能分类推荐和重复检测</p>\n              </div>\n              <div class=\"feature-card\">\n                <div class=\"feature-icon\">📱</div>\n                <h3>响应式设计</h3>\n                <p>完美适配各种设备和屏幕尺寸</p>\n              </div>\n              <div class=\"feature-card\">\n                <div class=\"feature-icon\">🔍</div>\n                <h3>强大搜索</h3>\n                <p>支持标题、描述、标签的全文搜索</p>\n              </div>\n              <div class=\"feature-card\">\n                <div class=\"feature-icon\">📥</div>\n                <h3>导入导出</h3>\n                <p>支持 Chrome 和 HTML 格式的书签导入导出</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"tech-section\">\n            <h2>🛠️ 技术栈</h2>\n            <div class=\"tech-stack\">\n              <div class=\"tech-item\">\n                <strong>前端：</strong>TypeScript + 现代 CSS + 响应式设计\n              </div>\n              <div class=\"tech-item\">\n                <strong>后端：</strong>Hono.js + Cloudflare Workers\n              </div>\n              <div class=\"tech-item\">\n                <strong>存储：</strong>Cloudflare KV Storage\n              </div>\n              <div class=\"tech-item\">\n                <strong>部署：</strong>Cloudflare Workers 全球边缘网络\n              </div>\n            </div>\n          </div>\n\n          <div class=\"version-section\">\n            <h2>📋 版本信息</h2>\n            <p><strong>版本：</strong>CloudNav 2.0</p>\n            <p><strong>更新时间：</strong>2025年6月</p>\n            <p><strong>开发者：</strong>Claude 4.0 Sonnet</p>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .about-page {\n          min-height: 100vh;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          padding: 2rem;\n        }\n\n        .about-container {\n          max-width: 1000px;\n          margin: 0 auto;\n          background: white;\n          border-radius: 1rem;\n          box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n          overflow: hidden;\n        }\n\n        .about-header {\n          background: linear-gradient(135deg, #7C3AED 0%, #A855F7 100%);\n          color: white;\n          padding: 3rem 2rem;\n          text-align: center;\n        }\n\n        .about-header h1 {\n          font-size: 2.5rem;\n          margin-bottom: 1rem;\n        }\n\n        .about-description {\n          font-size: 1.2rem;\n          opacity: 0.9;\n        }\n\n        .about-content {\n          padding: 3rem 2rem;\n        }\n\n        .features-section h2,\n        .tech-section h2,\n        .version-section h2 {\n          font-size: 1.5rem;\n          margin-bottom: 1.5rem;\n          color: #1F2937;\n        }\n\n        .features-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n          gap: 1.5rem;\n          margin-bottom: 3rem;\n        }\n\n        .feature-card {\n          background: #F9FAFB;\n          padding: 1.5rem;\n          border-radius: 0.75rem;\n          text-align: center;\n          transition: transform 0.2s;\n        }\n\n        .feature-card:hover {\n          transform: translateY(-2px);\n        }\n\n        .feature-icon {\n          font-size: 2.5rem;\n          margin-bottom: 1rem;\n        }\n\n        .feature-card h3 {\n          font-size: 1.2rem;\n          margin-bottom: 0.5rem;\n          color: #374151;\n        }\n\n        .feature-card p {\n          color: #6B7280;\n          line-height: 1.6;\n        }\n\n        .tech-stack {\n          background: #F3F4F6;\n          padding: 1.5rem;\n          border-radius: 0.75rem;\n          margin-bottom: 3rem;\n        }\n\n        .tech-item {\n          margin-bottom: 0.75rem;\n          color: #374151;\n          line-height: 1.6;\n        }\n\n        .tech-item:last-child {\n          margin-bottom: 0;\n        }\n\n        .version-section {\n          background: #EEF2FF;\n          padding: 1.5rem;\n          border-radius: 0.75rem;\n          color: #374151;\n        }\n\n        .version-section p {\n          margin-bottom: 0.5rem;\n          line-height: 1.6;\n        }\n\n        .version-section p:last-child {\n          margin-bottom: 0;\n        }\n\n        @media (max-width: 768px) {\n          .about-page {\n            padding: 1rem;\n          }\n\n          .about-header {\n            padding: 2rem 1rem;\n          }\n\n          .about-header h1 {\n            font-size: 2rem;\n          }\n\n          .about-content {\n            padding: 2rem 1rem;\n          }\n\n          .features-grid {\n            grid-template-columns: 1fr;\n          }\n        }\n      </style>\n    </div>\n  `;\n\n  const html = Layout({\n    title: `关于 - ${config.siteName}`,\n    description: `关于 ${config.siteName}`,\n    children: aboutContent\n  });\n\n  return c.html(html);\n});\n\nexport { app as pageRoutes };\n", "// CloudNav 2.0 - 管理路由\nimport { Hono } from 'hono';\nimport type { HonoApp } from '@/types/hono';\nimport { KVService } from '@/services/kv';\nimport { AdminPanel } from '@/components/AdminPanel';\nimport { Layout } from '@/components/Layout';\n\nconst app = new Hono<HonoApp>();\n\n// 管理面板主页\napp.get('/', async (c) => {\n  try {\n    const kvService = c.get('kvService') as KVService;\n    \n    // 获取所有数据\n    const [bookmarks, categories, stats, config] = await Promise.all([\n      kvService.getBookmarks(),\n      kvService.getCategories(),\n      kvService.getStats(),\n      kvService.getConfig()\n    ]);\n\n    const content = AdminPanel({ bookmarks, categories, stats, config });\n    const html = Layout({ \n      title: `管理面板 - ${config.siteName}`,\n      description: '管理您的书签和分类',\n      children: content \n    });\n\n    return c.html(html);\n  } catch (error) {\n    console.error('Admin panel error:', error);\n    return c.html(Layout({ \n      title: 'CloudNav - Admin Error',\n      description: 'Admin error',\n      children: '<div class=\"error\">管理面板加载失败。</div>'\n    }));\n  }\n});\n\n// 统计页面\napp.get('/stats', async (c) => {\n  try {\n    const kvService = c.get('kvService') as KVService;\n    const [stats, config] = await Promise.all([\n      kvService.getStats(),\n      kvService.getConfig()\n    ]);\n\n    const statsContent = `\n      <div class=\"admin-stats\">\n        <h1>统计数据</h1>\n        \n        <div class=\"stats-grid\">\n          <div class=\"stat-card\">\n            <h3>总点击量</h3>\n            <div class=\"stat-number\">${stats.totalClicks}</div>\n          </div>\n          \n          <div class=\"stat-card\">\n            <h3>总访问量</h3>\n            <div class=\"stat-number\">${stats.totalViews}</div>\n          </div>\n          \n          <div class=\"stat-card\">\n            <h3>移动端访问</h3>\n            <div class=\"stat-number\">${stats.deviceStats.mobile}</div>\n          </div>\n          \n          <div class=\"stat-card\">\n            <h3>桌面端访问</h3>\n            <div class=\"stat-number\">${stats.deviceStats.desktop}</div>\n          </div>\n        </div>\n        \n        <div class=\"stats-section\">\n          <h2>热门书签</h2>\n          <div class=\"stats-list\">\n            ${Object.entries(stats.bookmarkStats)\n              .sort(([,a], [,b]) => b - a)\n              .slice(0, 10)\n              .map(([id, count]) => `\n                <div class=\"stats-item\">\n                  <span class=\"stats-label\">${id}</span>\n                  <span class=\"stats-value\">${count} 次点击</span>\n                </div>\n              `).join('')}\n          </div>\n        </div>\n        \n        <div class=\"stats-section\">\n          <h2>热门搜索</h2>\n          <div class=\"stats-list\">\n            ${Object.entries(stats.searchStats)\n              .sort(([,a], [,b]) => b - a)\n              .slice(0, 10)\n              .map(([query, count]) => `\n                <div class=\"stats-item\">\n                  <span class=\"stats-label\">${query}</span>\n                  <span class=\"stats-value\">${count} 次搜索</span>\n                </div>\n              `).join('')}\n          </div>\n        </div>\n        \n        <div class=\"admin-actions\">\n          <a href=\"/admin\" class=\"btn\">返回管理面板</a>\n          <button onclick=\"exportStats()\" class=\"btn\">导出统计数据</button>\n        </div>\n      </div>\n      \n      <style>\n        .admin-stats {\n          max-width: 1000px;\n          margin: 0 auto;\n          padding: 20px;\n        }\n        \n        .stats-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 20px;\n          margin: 30px 0;\n        }\n        \n        .stat-card {\n          background: white;\n          padding: 20px;\n          border-radius: 12px;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n          text-align: center;\n        }\n        \n        .stat-number {\n          font-size: 2.5em;\n          font-weight: bold;\n          color: #7C3AED;\n          margin-top: 10px;\n        }\n        \n        .stats-section {\n          background: white;\n          padding: 20px;\n          border-radius: 12px;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n          margin: 20px 0;\n        }\n        \n        .stats-list {\n          margin-top: 15px;\n        }\n        \n        .stats-item {\n          display: flex;\n          justify-content: space-between;\n          padding: 10px 0;\n          border-bottom: 1px solid #e2e8f0;\n        }\n        \n        .stats-item:last-child {\n          border-bottom: none;\n        }\n        \n        .stats-label {\n          font-weight: 500;\n        }\n        \n        .stats-value {\n          color: #7C3AED;\n          font-weight: 600;\n        }\n        \n        .admin-actions {\n          text-align: center;\n          margin-top: 30px;\n        }\n        \n        .admin-actions .btn {\n          margin: 0 10px;\n        }\n      </style>\n      \n      <script>\n        function exportStats() {\n          fetch('/api/stats')\n            .then(response => response.json())\n            .then(data => {\n              const blob = new Blob([JSON.stringify(data.data, null, 2)], {\n                type: 'application/json'\n              });\n              const url = URL.createObjectURL(blob);\n              const a = document.createElement('a');\n              a.href = url;\n              a.download = 'cloudnav-stats-' + new Date().toISOString().split('T')[0] + '.json';\n              document.body.appendChild(a);\n              a.click();\n              document.body.removeChild(a);\n              URL.revokeObjectURL(url);\n            })\n            .catch(error => {\n              console.error('Export error:', error);\n              alert('导出失败，请稍后再试。');\n            });\n        }\n      </script>\n    `;\n\n    const html = Layout({ \n      title: `统计数据 - ${config.siteName}`,\n      description: '查看网站统计数据',\n      children: statsContent \n    });\n\n    return c.html(html);\n  } catch (error) {\n    console.error('Admin stats error:', error);\n    return c.html(Layout({ \n      title: 'CloudNav - Stats Error',\n      description: 'Stats error',\n      children: '<div class=\"error\">统计数据加载失败。</div>'\n    }));\n  }\n});\n\n// 设置页面\napp.get('/settings', async (c) => {\n  try {\n    const kvService = c.get('kvService') as KVService;\n    const config = await kvService.getConfig();\n\n    const settingsContent = `\n      <div class=\"admin-settings\">\n        <h1>系统设置</h1>\n        \n        <form id=\"settingsForm\" class=\"settings-form\">\n          <div class=\"form-section\">\n            <h2>基本设置</h2>\n            \n            <div class=\"form-group\">\n              <label for=\"siteName\">网站名称</label>\n              <input type=\"text\" id=\"siteName\" name=\"siteName\" value=\"${config.siteName}\" required>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"siteDescription\">网站描述</label>\n              <textarea id=\"siteDescription\" name=\"siteDescription\" rows=\"3\" required>${config.siteDescription}</textarea>\n            </div>\n          </div>\n          \n          <div class=\"form-section\">\n            <h2>AI 设置</h2>\n            \n            <div class=\"form-group\">\n              <label>\n                <input type=\"checkbox\" id=\"aiEnabled\" name=\"aiEnabled\" ${config.aiConfig.enabled ? 'checked' : ''}>\n                启用 AI 功能\n              </label>\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"aiApiUrl\">AI API 地址</label>\n              <input type=\"url\" id=\"aiApiUrl\" name=\"aiApiUrl\" value=\"${config.aiConfig.apiUrl || ''}\" placeholder=\"https://api.openai.com/v1\">\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"aiApiKey\">AI API 密钥</label>\n              <input type=\"password\" id=\"aiApiKey\" name=\"aiApiKey\" value=\"${config.aiConfig.apiKey || ''}\" placeholder=\"sk-...\">\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"aiModel\">AI 模型</label>\n              <input type=\"text\" id=\"aiModel\" name=\"aiModel\" value=\"${config.aiConfig.model || ''}\" placeholder=\"gpt-3.5-turbo\">\n            </div>\n          </div>\n          \n          <div class=\"form-section\">\n            <h2>功能设置</h2>\n            \n            <div class=\"form-group\">\n              <label>\n                <input type=\"checkbox\" id=\"statsEnabled\" name=\"statsEnabled\" ${config.features.stats ? 'checked' : ''}>\n                启用统计功能\n              </label>\n            </div>\n            \n            <div class=\"form-group\">\n              <label>\n                <input type=\"checkbox\" id=\"importEnabled\" name=\"importEnabled\" ${config.features.import ? 'checked' : ''}>\n                启用导入功能\n              </label>\n            </div>\n            \n            <div class=\"form-group\">\n              <label>\n                <input type=\"checkbox\" id=\"exportEnabled\" name=\"exportEnabled\" ${config.features.export ? 'checked' : ''}>\n                启用导出功能\n              </label>\n            </div>\n          </div>\n          \n          <div class=\"form-actions\">\n            <button type=\"submit\" class=\"btn btn-primary\">保存设置</button>\n            <a href=\"/admin\" class=\"btn\">取消</a>\n          </div>\n        </form>\n      </div>\n      \n      <style>\n        .admin-settings {\n          max-width: 800px;\n          margin: 0 auto;\n          padding: 20px;\n        }\n        \n        .settings-form {\n          background: white;\n          padding: 30px;\n          border-radius: 12px;\n          box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        \n        .form-section {\n          margin-bottom: 30px;\n          padding-bottom: 20px;\n          border-bottom: 1px solid #e2e8f0;\n        }\n        \n        .form-section:last-of-type {\n          border-bottom: none;\n        }\n        \n        .form-group {\n          margin-bottom: 20px;\n        }\n        \n        .form-group label {\n          display: block;\n          margin-bottom: 5px;\n          font-weight: 500;\n          color: #374151;\n        }\n        \n        .form-group input,\n        .form-group textarea {\n          width: 100%;\n          padding: 10px 12px;\n          border: 2px solid #e2e8f0;\n          border-radius: 8px;\n          font-size: 16px;\n          transition: border-color 0.3s ease;\n        }\n        \n        .form-group input:focus,\n        .form-group textarea:focus {\n          outline: none;\n          border-color: #7C3AED;\n        }\n        \n        .form-group input[type=\"checkbox\"] {\n          width: auto;\n          margin-right: 8px;\n        }\n        \n        .form-actions {\n          text-align: center;\n          margin-top: 30px;\n        }\n        \n        .form-actions .btn {\n          margin: 0 10px;\n        }\n        \n        .btn-primary {\n          background: #7C3AED;\n          color: white;\n        }\n        \n        .btn-primary:hover {\n          background: #6D28D9;\n        }\n      </style>\n      \n      <script>\n        document.getElementById('settingsForm').addEventListener('submit', async function(e) {\n          e.preventDefault();\n          \n          const formData = new FormData(this);\n          const settings = {\n            siteName: formData.get('siteName'),\n            siteDescription: formData.get('siteDescription'),\n            aiConfig: {\n              enabled: formData.has('aiEnabled'),\n              apiUrl: formData.get('aiApiUrl'),\n              apiKey: formData.get('aiApiKey'),\n              model: formData.get('aiModel')\n            },\n            features: {\n              stats: formData.has('statsEnabled'),\n              ai: formData.has('aiEnabled'),\n              import: formData.has('importEnabled'),\n              export: formData.has('exportEnabled')\n            }\n          };\n          \n          try {\n            const response = await fetch('/admin/api/config', {\n              method: 'PUT',\n              headers: { 'Content-Type': 'application/json' },\n              body: JSON.stringify(settings)\n            });\n            \n            if (response.ok) {\n              alert('设置保存成功！');\n              window.location.href = '/admin';\n            } else {\n              alert('保存失败，请稍后再试。');\n            }\n          } catch (error) {\n            console.error('Save error:', error);\n            alert('保存失败，请稍后再试。');\n          }\n        });\n      </script>\n    `;\n\n    const html = Layout({ \n      title: `系统设置 - ${config.siteName}`,\n      description: '配置系统设置',\n      children: settingsContent \n    });\n\n    return c.html(html);\n  } catch (error) {\n    console.error('Admin settings error:', error);\n    return c.html(Layout({ \n      title: 'CloudNav - Settings Error',\n      description: 'Settings error',\n      children: '<div class=\"error\">设置页面加载失败。</div>'\n    }));\n  }\n});\n\nexport { app as adminRoutes };\n", "// CloudNav 2.0 - Workers 入口文件\nimport { Hono } from 'hono';\nimport { cors } from 'hono/cors';\nimport { logger } from 'hono/logger';\nimport { prettyJSON } from 'hono/pretty-json';\nimport { secureHeaders } from 'hono/secure-headers';\n\nimport type { HonoApp } from '@/types/hono';\nimport { KVService } from '@/services/kv';\n\n// 导入路由\nimport { apiRoutes } from '@/routes/api';\nimport { pageRoutes } from '@/routes/pages';\nimport { adminRoutes } from '@/routes/admin';\n\n// 创建 Hono 应用\nconst app = new Hono<HonoApp>();\n\n// 中间件\napp.use('*', logger());\napp.use('*', secureHeaders());\napp.use('*', prettyJSON());\napp.use('*', cors({\n  origin: '*',\n  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],\n  allowHeaders: ['Content-Type', 'Authorization'],\n}));\n\n// 添加 KV 服务到上下文\napp.use('*', async (c, next) => {\n  const kvService = new KVService(c.env.CLOUDNAV_KV);\n  c.set('kvService', kvService);\n  await next();\n});\n\n// 健康检查\napp.get('/health', (c) => {\n  return c.json({\n    status: 'ok',\n    timestamp: new Date().toISOString(),\n    version: '2.0.0'\n  });\n});\n\n\n\n// 注册路由（顺序很重要！）\napp.route('/api', apiRoutes);\napp.route('/admin', adminRoutes);\n// 注意：页面路由必须放在最后，因为它会匹配所有路径\napp.route('/', pageRoutes);\n\n// 404 处理\napp.notFound((c) => {\n  return c.json({ error: 'Not Found' }, 404);\n});\n\n// 错误处理\napp.onError((err, c) => {\n  console.error('Application error:', err);\n  return c.json({ \n    error: 'Internal Server Error',\n    message: c.env.ENVIRONMENT === 'development' ? err.message : undefined\n  }, 500);\n});\n\nexport default app;\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"E:\\\\Work\\\\cloudnav\\\\src\\\\index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"E:\\\\Work\\\\cloudnav\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"E:\\\\Work\\\\cloudnav\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"E:\\\\Work\\\\cloudnav\\\\src\\\\index.ts\";\n\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"E:\\\\Work\\\\cloudnav\\\\.wrangler\\\\tmp\\\\bundle-oi0dnv\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"E:\\\\Work\\\\cloudnav\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"E:\\\\Work\\\\cloudnav\\\\.wrangler\\\\tmp\\\\bundle-oi0dnv\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"E:\\\\Work\\\\cloudnav\\\\.wrangler\\\\tmp\\\\bundle-oi0dnv\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS;AAAA;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC7BD,SAAS,0BAA0B,OAAO,MAAM;AAC/C,QAAM,UAAU,IAAI,QAAQ,OAAO,IAAI;AACvC,UAAQ,QAAQ,OAAO,kBAAkB;AACzC,SAAO;AACR;AAJS;AAMT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,WAAO,QAAQ,MAAM,QAAQ,SAAS;AAAA,MACrC,0BAA0B,MAAM,MAAM,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACF;AACD,CAAC;;;ACXD,IAAI,UAAU,wBAAC,YAAY,SAAS,eAAe;AACjD,SAAO,CAAC,SAAS,SAAS;AACxB,QAAI,QAAQ;AACZ,WAAO,SAAS,CAAC;AACjB,mBAAe,SAAS,GAAG;AACzB,UAAI,KAAK,OAAO;AACd,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AACA,cAAQ;AACR,UAAI;AACJ,UAAI,UAAU;AACd,UAAI;AACJ,UAAI,WAAW,CAAC,GAAG;AACjB,kBAAU,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,gBAAQ,IAAI,aAAa;AAAA,MAC3B,OAAO;AACL,kBAAU,MAAM,WAAW,UAAU,QAAQ;AAAA,MAC/C;AACA,UAAI,SAAS;AACX,YAAI;AACF,gBAAM,MAAM,QAAQ,SAAS,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,QACpD,SAAS,KAAP;AACA,cAAI,eAAe,SAAS,SAAS;AACnC,oBAAQ,QAAQ;AAChB,kBAAM,MAAM,QAAQ,KAAK,OAAO;AAChC,sBAAU;AAAA,UACZ,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,cAAc,SAAS,YAAY;AAC7C,gBAAM,MAAM,WAAW,OAAO;AAAA,QAChC;AAAA,MACF;AACA,UAAI,QAAQ,QAAQ,cAAc,SAAS,UAAU;AACnD,gBAAQ,MAAM;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAnCe;AAAA,EAoCjB;AACF,GAzCc;;;ACAd,IAAI,mBAAmB,OAAO;;;ACC9B,IAAI,YAAY,8BAAO,SAAS,UAA0B,uBAAO,OAAO,IAAI,MAAM;AAChF,QAAM,EAAE,MAAM,OAAO,MAAM,MAAM,IAAI;AACrC,QAAM,UAAU,mBAAmB,cAAc,QAAQ,IAAI,UAAU,QAAQ;AAC/E,QAAM,cAAc,QAAQ,IAAI,cAAc;AAC9C,MAAI,aAAa,WAAW,qBAAqB,KAAK,aAAa,WAAW,mCAAmC,GAAG;AAClH,WAAO,cAAc,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,EAC5C;AACA,SAAO,CAAC;AACV,GARgB;AAShB,eAAe,cAAc,SAAS,SAAS;AAC7C,QAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,MAAI,UAAU;AACZ,WAAO,0BAA0B,UAAU,OAAO;AAAA,EACpD;AACA,SAAO,CAAC;AACV;AANe;AAOf,SAAS,0BAA0B,UAAU,SAAS;AACpD,QAAM,OAAuB,uBAAO,OAAO,IAAI;AAC/C,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,UAAM,uBAAuB,QAAQ,OAAO,IAAI,SAAS,IAAI;AAC7D,QAAI,CAAC,sBAAsB;AACzB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,6BAAuB,MAAM,KAAK,KAAK;AAAA,IACzC;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,KAAK;AACf,WAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAM,uBAAuB,IAAI,SAAS,GAAG;AAC7C,UAAI,sBAAsB;AACxB,kCAA0B,MAAM,KAAK,KAAK;AAC1C,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AApBS;AAqBT,IAAI,yBAAyB,wBAAC,MAAM,KAAK,UAAU;AACjD,MAAI,KAAK,GAAG,MAAM,QAAQ;AACxB,QAAI,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG;AAC5B;AACA,WAAK,GAAG,EAAE,KAAK,KAAK;AAAA,IACtB,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,KAAK;AAAA,IAC/B;AAAA,EACF,OAAO;AACL,QAAI,CAAC,IAAI,SAAS,IAAI,GAAG;AACvB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK;AAAA,IACpB;AAAA,EACF;AACF,GAf6B;AAgB7B,IAAI,4BAA4B,wBAAC,MAAM,KAAK,UAAU;AACpD,MAAI,aAAa;AACjB,QAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,OAAK,QAAQ,CAAC,MAAM,UAAU;AAC5B,QAAI,UAAU,KAAK,SAAS,GAAG;AAC7B,iBAAW,IAAI,IAAI;AAAA,IACrB,OAAO;AACL,UAAI,CAAC,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI,MAAM,YAAY,MAAM,QAAQ,WAAW,IAAI,CAAC,KAAK,WAAW,IAAI,aAAa,MAAM;AACpI,mBAAW,IAAI,IAAoB,uBAAO,OAAO,IAAI;AAAA,MACvD;AACA,mBAAa,WAAW,IAAI;AAAA,IAC9B;AAAA,EACF,CAAC;AACH,GAbgC;;;ACtDhC,IAAI,YAAY,wBAAC,SAAS;AACxB,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM,CAAC,MAAM,IAAI;AACnB,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT,GANgB;AAOhB,IAAI,mBAAmB,wBAAC,cAAc;AACpC,QAAM,EAAE,QAAQ,KAAK,IAAI,sBAAsB,SAAS;AACxD,QAAM,QAAQ,UAAU,IAAI;AAC5B,SAAO,kBAAkB,OAAO,MAAM;AACxC,GAJuB;AAKvB,IAAI,wBAAwB,wBAAC,SAAS;AACpC,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,QAAQ,cAAc,CAAC,OAAO,UAAU;AAClD,UAAM,OAAO,IAAI;AACjB,WAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AACzB,WAAO;AAAA,EACT,CAAC;AACD,SAAO,EAAE,QAAQ,KAAK;AACxB,GAR4B;AAS5B,IAAI,oBAAoB,wBAAC,OAAO,WAAW;AACzC,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,UAAI,MAAM,CAAC,EAAE,SAAS,IAAI,GAAG;AAC3B,cAAM,CAAC,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAXwB;AAYxB,IAAI,eAAe,CAAC;AACpB,IAAI,aAAa,wBAAC,OAAO,SAAS;AAChC,MAAI,UAAU,KAAK;AACjB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM,MAAM,6BAA6B;AACvD,MAAI,OAAO;AACT,UAAM,WAAW,GAAG,SAAS;AAC7B,QAAI,CAAC,aAAa,QAAQ,GAAG;AAC3B,UAAI,MAAM,CAAC,GAAG;AACZ,qBAAa,QAAQ,IAAI,QAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,CAAC,UAAU,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,QAAQ,OAAO,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC;AAAA,MACpL,OAAO;AACL,qBAAa,QAAQ,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,MACjD;AAAA,IACF;AACA,WAAO,aAAa,QAAQ;AAAA,EAC9B;AACA,SAAO;AACT,GAjBiB;AAkBjB,IAAI,YAAY,wBAAC,KAAK,YAAY;AAChC,MAAI;AACF,WAAO,QAAQ,GAAG;AAAA,EACpB,QAAE;AACA,WAAO,IAAI,QAAQ,yBAAyB,CAAC,UAAU;AACrD,UAAI;AACF,eAAO,QAAQ,KAAK;AAAA,MACtB,QAAE;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF,GAZgB;AAahB,IAAI,eAAe,wBAAC,QAAQ,UAAU,KAAK,SAAS,GAAjC;AACnB,IAAI,UAAU,wBAAC,YAAY;AACzB,QAAM,MAAM,QAAQ;AACpB,QAAM,QAAQ,IAAI;AAAA,IAChB;AAAA,IACA,IAAI,WAAW,CAAC,MAAM,KAAK,KAAK;AAAA,EAClC;AACA,MAAI,IAAI;AACR,SAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,UAAM,WAAW,IAAI,WAAW,CAAC;AACjC,QAAI,aAAa,IAAI;AACnB,YAAM,aAAa,IAAI,QAAQ,KAAK,CAAC;AACrC,YAAM,OAAO,IAAI,MAAM,OAAO,eAAe,KAAK,SAAS,UAAU;AACrE,aAAO,aAAa,KAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,QAAQ,OAAO,IAAI,IAAI;AAAA,IACjF,WAAW,aAAa,IAAI;AAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,OAAO,CAAC;AAC3B,GAlBc;AAuBd,IAAI,kBAAkB,wBAAC,YAAY;AACjC,QAAM,SAAS,QAAQ,OAAO;AAC9B,SAAO,OAAO,SAAS,KAAK,OAAO,GAAG,EAAE,MAAM,MAAM,OAAO,MAAM,GAAG,EAAE,IAAI;AAC5E,GAHsB;AAItB,IAAI,YAAY,wBAAC,MAAM,QAAQ,SAAS;AACtC,MAAI,KAAK,QAAQ;AACf,UAAM,UAAU,KAAK,GAAG,IAAI;AAAA,EAC9B;AACA,SAAO,GAAG,OAAO,CAAC,MAAM,MAAM,KAAK,MAAM,OAAO,QAAQ,MAAM,KAAK,GAAG,MAAM,GAAG,EAAE,MAAM,MAAM,KAAK,MAAM,MAAM,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI;AAC5I,GALgB;AAMhB,IAAI,yBAAyB,wBAAC,SAAS;AACrC,MAAI,KAAK,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM,CAAC,KAAK,SAAS,GAAG,GAAG;AAClE,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,GAAG;AAC/B,QAAM,UAAU,CAAC;AACjB,MAAI,WAAW;AACf,WAAS,QAAQ,CAAC,YAAY;AAC5B,QAAI,YAAY,MAAM,CAAC,KAAK,KAAK,OAAO,GAAG;AACzC,kBAAY,MAAM;AAAA,IACpB,WAAW,KAAK,KAAK,OAAO,GAAG;AAC7B,UAAI,KAAK,KAAK,OAAO,GAAG;AACtB,YAAI,QAAQ,WAAW,KAAK,aAAa,IAAI;AAC3C,kBAAQ,KAAK,GAAG;AAAA,QAClB,OAAO;AACL,kBAAQ,KAAK,QAAQ;AAAA,QACvB;AACA,cAAM,kBAAkB,QAAQ,QAAQ,KAAK,EAAE;AAC/C,oBAAY,MAAM;AAClB,gBAAQ,KAAK,QAAQ;AAAA,MACvB,OAAO;AACL,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AACvD,GA1B6B;AA2B7B,IAAI,aAAa,wBAAC,UAAU;AAC1B,MAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,YAAQ,MAAM,QAAQ,OAAO,GAAG;AAAA,EAClC;AACA,SAAO,MAAM,QAAQ,GAAG,MAAM,KAAK,UAAU,OAAO,mBAAmB,IAAI;AAC7E,GARiB;AASjB,IAAI,iBAAiB,wBAAC,KAAK,KAAK,aAAa;AAC3C,MAAI;AACJ,MAAI,CAAC,YAAY,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACzC,QAAI,YAAY,IAAI,QAAQ,IAAI,OAAO,CAAC;AACxC,QAAI,cAAc,IAAI;AACpB,kBAAY,IAAI,QAAQ,IAAI,OAAO,CAAC;AAAA,IACtC;AACA,WAAO,cAAc,IAAI;AACvB,YAAM,kBAAkB,IAAI,WAAW,YAAY,IAAI,SAAS,CAAC;AACjE,UAAI,oBAAoB,IAAI;AAC1B,cAAM,aAAa,YAAY,IAAI,SAAS;AAC5C,cAAM,WAAW,IAAI,QAAQ,KAAK,UAAU;AAC5C,eAAO,WAAW,IAAI,MAAM,YAAY,aAAa,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC9E,WAAW,mBAAmB,MAAM,MAAM,eAAe,GAAG;AAC1D,eAAO;AAAA,MACT;AACA,kBAAY,IAAI,QAAQ,IAAI,OAAO,YAAY,CAAC;AAAA,IAClD;AACA,cAAU,OAAO,KAAK,GAAG;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU,CAAC;AACjB,cAAY,OAAO,KAAK,GAAG;AAC3B,MAAI,WAAW,IAAI,QAAQ,KAAK,CAAC;AACjC,SAAO,aAAa,IAAI;AACtB,UAAM,eAAe,IAAI,QAAQ,KAAK,WAAW,CAAC;AAClD,QAAI,aAAa,IAAI,QAAQ,KAAK,QAAQ;AAC1C,QAAI,aAAa,gBAAgB,iBAAiB,IAAI;AACpD,mBAAa;AAAA,IACf;AACA,QAAI,OAAO,IAAI;AAAA,MACb,WAAW;AAAA,MACX,eAAe,KAAK,iBAAiB,KAAK,SAAS,eAAe;AAAA,IACpE;AACA,QAAI,SAAS;AACX,aAAO,WAAW,IAAI;AAAA,IACxB;AACA,eAAW;AACX,QAAI,SAAS,IAAI;AACf;AAAA,IACF;AACA,QAAI;AACJ,QAAI,eAAe,IAAI;AACrB,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ,IAAI,MAAM,aAAa,GAAG,iBAAiB,KAAK,SAAS,YAAY;AAC7E,UAAI,SAAS;AACX,gBAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,EAAE,QAAQ,IAAI,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACpD,gBAAQ,IAAI,IAAI,CAAC;AAAA,MACnB;AACA;AACA,cAAQ,IAAI,EAAE,KAAK,KAAK;AAAA,IAC1B,OAAO;AACL,cAAQ,IAAI,MAAM;AAAA,IACpB;AAAA,EACF;AACA,SAAO,MAAM,QAAQ,GAAG,IAAI;AAC9B,GA/DqB;AAgErB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB,wBAAC,KAAK,QAAQ;AACjC,SAAO,eAAe,KAAK,KAAK,IAAI;AACtC,GAFqB;AAGrB,IAAI,sBAAsB;;;ACxM1B,IAAI,wBAAwB,wBAAC,QAAQ,UAAU,KAAK,mBAAmB,GAA3C;AAC5B,IAAI,cAAc,6BAAM;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,YAAY,CAAC;AAAA,EACb,YAAY,SAAS,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,GAAG;AACnD,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,MAAM,KAAK;AACT,WAAO,MAAM,KAAK,iBAAiB,GAAG,IAAI,KAAK,qBAAqB;AAAA,EACtE;AAAA,EACA,iBAAiB,KAAK;AACpB,UAAM,WAAW,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG;AAC7D,UAAM,QAAQ,KAAK,eAAe,QAAQ;AAC1C,WAAO,QAAQ,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI,QAAQ;AAAA,EAC3E;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO,OAAO,KAAK,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,CAAC;AACjE,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,KAAK,eAAe,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC;AAC/E,UAAI,SAAS,OAAO,UAAU,UAAU;AACtC,gBAAQ,GAAG,IAAI,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI;AAAA,MACnE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,UAAU;AACvB,WAAO,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,EAAE,QAAQ,IAAI;AAAA,EACjE;AAAA,EACA,MAAM,KAAK;AACT,WAAO,cAAc,KAAK,KAAK,GAAG;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,eAAe,KAAK,KAAK,GAAG;AAAA,EACrC;AAAA,EACA,OAAO,MAAM;AACX,QAAI,MAAM;AACR,aAAO,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAAA,IACvC;AACA,UAAM,aAAa,CAAC;AACpB,SAAK,IAAI,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,iBAAW,GAAG,IAAI;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,MAAM,UAAU,SAAS;AACvB,WAAO,KAAK,UAAU,eAAe,MAAM,UAAU,MAAM,OAAO;AAAA,EACpE;AAAA,EACA,cAAc,CAAC,QAAQ;AACrB,UAAM,EAAE,WAAW,KAAAA,KAAI,IAAI;AAC3B,UAAM,aAAa,UAAU,GAAG;AAChC,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,UAAM,eAAe,OAAO,KAAK,SAAS,EAAE,CAAC;AAC7C,QAAI,cAAc;AAChB,aAAO,UAAU,YAAY,EAAE,KAAK,CAAC,SAAS;AAC5C,YAAI,iBAAiB,QAAQ;AAC3B,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AACA,eAAO,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AAAA,MACjC,CAAC;AAAA,IACH;AACA,WAAO,UAAU,GAAG,IAAIA,KAAI,GAAG,EAAE;AAAA,EACnC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,YAAY,aAAa;AAAA,EACvC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,WAAW;AACT,WAAO,KAAK,YAAY,UAAU;AAAA,EACpC;AAAA,EACA,iBAAiB,QAAQ,MAAM;AAC7B,SAAK,eAAe,MAAM,IAAI;AAAA,EAChC;AAAA,EACA,MAAM,QAAQ;AACZ,WAAO,KAAK,eAAe,MAAM;AAAA,EACnC;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,KAAK,gBAAgB,IAAI;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK;AAAA,EACxD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK,EAAE,KAAK,UAAU,EAAE;AAAA,EAC3E;AACF,GA3GkB;;;ACJlB,IAAI,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,cAAc;AAAA,EACd,QAAQ;AACV;AACA,IAAI,MAAM,wBAAC,OAAO,cAAc;AAC9B,QAAM,gBAAgB,IAAI,OAAO,KAAK;AACtC,gBAAc,YAAY;AAC1B,gBAAc,YAAY;AAC1B,SAAO;AACT,GALU;AAgFV,IAAI,kBAAkB,8BAAO,KAAK,OAAO,mBAAmB,SAAS,WAAW;AAC9E,MAAI,OAAO,QAAQ,YAAY,EAAE,eAAe,SAAS;AACvD,QAAI,EAAE,eAAe,UAAU;AAC7B,YAAM,IAAI,SAAS;AAAA,IACrB;AACA,QAAI,eAAe,SAAS;AAC1B,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AACA,QAAM,YAAY,IAAI;AACtB,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO,QAAQ,QAAQ,GAAG;AAAA,EAC5B;AACA,MAAI,QAAQ;AACV,WAAO,CAAC,KAAK;AAAA,EACf,OAAO;AACL,aAAS,CAAC,GAAG;AAAA,EACf;AACA,QAAM,SAAS,QAAQ,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,QAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE;AAAA,IAC9E,CAAC,QAAQ,QAAQ;AAAA,MACf,IAAI,OAAO,OAAO,EAAE,IAAI,CAAC,SAAS,gBAAgB,MAAM,OAAO,OAAO,SAAS,MAAM,CAAC;AAAA,IACxF,EAAE,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,EACxB;AACA,MAAI,mBAAmB;AACrB,WAAO,IAAI,MAAM,QAAQ,SAAS;AAAA,EACpC,OAAO;AACL,WAAO;AAAA,EACT;AACF,GA5BsB;;;ACnFtB,IAAI,aAAa;AACjB,IAAI,wBAAwB,wBAAC,aAAa,YAAY;AACpD,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AACF,GAL4B;AAM5B,IAAI,UAAU,6BAAM;AAAA,EAClB;AAAA,EACA;AAAA,EACA,MAAM,CAAC;AAAA,EACP;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,SAAS;AACxB,SAAK,cAAc;AACnB,QAAI,SAAS;AACX,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,QAAQ;AAChC,WAAK,QAAQ,QAAQ;AACrB,WAAK,eAAe,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,SAAK,SAAS,IAAI,YAAY,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;AAC7E,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,iBAAiB,iBAAiB,KAAK,eAAe;AAC7D,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,gCAAgC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,sCAAsC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,SAAS,IAAI,SAAS,MAAM;AAAA,MACtC,SAAS,KAAK,qBAAqB,IAAI,QAAQ;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI,MAAM;AACZ,QAAI,KAAK,QAAQ,MAAM;AACrB,aAAO,IAAI,SAAS,KAAK,MAAM,IAAI;AACnC,iBAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAChD,YAAI,MAAM,gBAAgB;AACxB;AAAA,QACF;AACA,YAAI,MAAM,cAAc;AACtB,gBAAM,UAAU,KAAK,KAAK,QAAQ,aAAa;AAC/C,eAAK,QAAQ,OAAO,YAAY;AAChC,qBAAW,UAAU,SAAS;AAC5B,iBAAK,QAAQ,OAAO,cAAc,MAAM;AAAA,UAC1C;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,IAAI,GAAG,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,SAAS,IAAI,SAAS;AACpB,SAAK,cAAc,CAAC,YAAY,KAAK,KAAK,OAAO;AACjD,WAAO,KAAK,UAAU,GAAG,IAAI;AAAA,EAC/B;AAAA,EACA,YAAY,CAAC,WAAW,KAAK,UAAU;AAAA,EACvC,YAAY,MAAM,KAAK;AAAA,EACvB,cAAc,CAAC,aAAa;AAC1B,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,SAAS,CAAC,MAAM,OAAO,YAAY;AACjC,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO,IAAI,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI;AAAA,IACpD;AACA,UAAM,UAAU,KAAK,OAAO,KAAK,KAAK,UAAU,KAAK,qBAAqB,IAAI,QAAQ;AACtF,QAAI,UAAU,QAAQ;AACpB,cAAQ,OAAO,IAAI;AAAA,IACrB,WAAW,SAAS,QAAQ;AAC1B,cAAQ,OAAO,MAAM,KAAK;AAAA,IAC5B,OAAO;AACL,cAAQ,IAAI,MAAM,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,SAAS,CAAC,WAAW;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,CAAC,KAAK,UAAU;AACpB,SAAK,SAAyB,oBAAI,IAAI;AACtC,SAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EAC1B;AAAA,EACA,MAAM,CAAC,QAAQ;AACb,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,GAAG,IAAI;AAAA,EAC1C;AAAA,EACA,IAAI,MAAM;AACR,QAAI,CAAC,KAAK,MAAM;AACd,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,YAAY,KAAK,IAAI;AAAA,EACrC;AAAA,EACA,aAAa,MAAM,KAAK,SAAS;AAC/B,UAAM,kBAAkB,KAAK,OAAO,IAAI,QAAQ,KAAK,KAAK,OAAO,IAAI,KAAK,oBAAoB,IAAI,QAAQ;AAC1G,QAAI,OAAO,QAAQ,YAAY,aAAa,KAAK;AAC/C,YAAM,aAAa,IAAI,mBAAmB,UAAU,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO;AACzF,iBAAW,CAAC,KAAK,KAAK,KAAK,YAAY;AACrC,YAAI,IAAI,YAAY,MAAM,cAAc;AACtC,0BAAgB,OAAO,KAAK,KAAK;AAAA,QACnC,OAAO;AACL,0BAAgB,IAAI,KAAK,KAAK;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS;AACX,iBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC5C,YAAI,OAAO,MAAM,UAAU;AACzB,0BAAgB,IAAI,GAAG,CAAC;AAAA,QAC1B,OAAO;AACL,0BAAgB,OAAO,CAAC;AACxB,qBAAW,MAAM,GAAG;AAClB,4BAAgB,OAAO,GAAG,EAAE;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,KAAK,UAAU,KAAK;AACnE,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,SAAS,gBAAgB,CAAC;AAAA,EAChE;AAAA,EACA,cAAc,IAAI,SAAS,KAAK,aAAa,GAAG,IAAI;AAAA,EACpD,OAAO,CAAC,MAAM,KAAK,YAAY,KAAK,aAAa,MAAM,KAAK,OAAO;AAAA,EACnE,OAAO,CAAC,MAAM,KAAK,YAAY;AAC7B,WAAO,CAAC,KAAK,oBAAoB,CAAC,KAAK,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,YAAY,IAAI,SAAS,IAAI,IAAI,KAAK;AAAA,MAChH;AAAA,MACA;AAAA,MACA,sBAAsB,YAAY,OAAO;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO,CAAC,QAAQ,KAAK,YAAY;AAC/B,WAAO,KAAK;AAAA,MACV,KAAK,UAAU,MAAM;AAAA,MACrB;AAAA,MACA,sBAAsB,oBAAoB,OAAO;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO,CAAC,MAAM,KAAK,YAAY;AAC7B,UAAM,MAAM,wBAAC,UAAU,KAAK,aAAa,OAAO,KAAK,sBAAsB,4BAA4B,OAAO,CAAC,GAAnG;AACZ,WAAO,OAAO,SAAS,WAAW,gBAAgB,MAAM,yBAAyB,WAAW,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,IAAI,IAAI;AAAA,EAC7H;AAAA,EACA,WAAW,CAAC,UAAU,WAAW;AAC/B,SAAK,OAAO,YAAY,OAAO,QAAQ,CAAC;AACxC,WAAO,KAAK,YAAY,MAAM,UAAU,GAAG;AAAA,EAC7C;AAAA,EACA,WAAW,MAAM;AACf,SAAK,qBAAqB,MAAM,IAAI,SAAS;AAC7C,WAAO,KAAK,iBAAiB,IAAI;AAAA,EACnC;AACF,GAnKc;;;ACTd,IAAI,kBAAkB;AACtB,IAAI,4BAA4B;AAChC,IAAI,UAAU,CAAC,OAAO,QAAQ,OAAO,UAAU,WAAW,OAAO;AACjE,IAAI,mCAAmC;AACvC,IAAI,uBAAuB,qCAAc,MAAM;AAC/C,GAD2B;;;ACJ3B,IAAI,mBAAmB;;;ACKvB,IAAI,kBAAkB,wBAAC,MAAM;AAC3B,SAAO,EAAE,KAAK,iBAAiB,GAAG;AACpC,GAFsB;AAGtB,IAAI,eAAe,wBAAC,KAAK,MAAM;AAC7B,MAAI,iBAAiB,KAAK;AACxB,UAAM,MAAM,IAAI,YAAY;AAC5B,WAAO,EAAE,YAAY,IAAI,MAAM,GAAG;AAAA,EACpC;AACA,UAAQ,MAAM,GAAG;AACjB,SAAO,EAAE,KAAK,yBAAyB,GAAG;AAC5C,GAPmB;AAQnB,IAAI,OAAO,6BAAM;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,aAAa,CAAC,GAAG,SAAS,yBAAyB;AACzD,eAAW,QAAQ,CAAC,WAAW;AAC7B,WAAK,MAAM,IAAI,CAAC,UAAU,SAAS;AACjC,YAAI,OAAO,UAAU,UAAU;AAC7B,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,UAAU,QAAQ,KAAK,OAAO,KAAK;AAAA,QAC1C;AACA,aAAK,QAAQ,CAAC,YAAY;AACxB,eAAK,UAAU,QAAQ,KAAK,OAAO,OAAO;AAAA,QAC5C,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,SAAK,KAAK,CAAC,QAAQ,SAAS,aAAa;AACvC,iBAAW,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG;AAC7B,aAAK,QAAQ;AACb,mBAAW,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG;AAC/B,mBAAS,IAAI,CAAC,YAAY;AACxB,iBAAK,UAAU,EAAE,YAAY,GAAG,KAAK,OAAO,OAAO;AAAA,UACrD,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,MAAM,CAAC,SAAS,aAAa;AAChC,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ;AACb,iBAAS,QAAQ,IAAI;AAAA,MACvB;AACA,eAAS,QAAQ,CAAC,YAAY;AAC5B,aAAK,UAAU,iBAAiB,KAAK,OAAO,OAAO;AAAA,MACrD,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,EAAE,QAAQ,GAAG,qBAAqB,IAAI;AAC5C,WAAO,OAAO,MAAM,oBAAoB;AACxC,SAAK,UAAU,UAAU,OAAO,QAAQ,WAAW,UAAU;AAAA,EAC/D;AAAA,EACA,SAAS;AACP,UAAM,QAAQ,IAAI,KAAK;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,UAAM,eAAe,KAAK;AAC1B,UAAM,mBAAmB,KAAK;AAC9B,UAAM,SAAS,KAAK;AACpB,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,MAAM,MAAMC,MAAK;AACf,UAAM,SAAS,KAAK,SAAS,IAAI;AACjC,IAAAA,KAAI,OAAO,IAAI,CAAC,MAAM;AACpB,UAAI;AACJ,UAAIA,KAAI,iBAAiB,cAAc;AACrC,kBAAU,EAAE;AAAA,MACd,OAAO;AACL,kBAAU,8BAAO,GAAG,UAAU,MAAM,QAAQ,CAAC,GAAGA,KAAI,YAAY,EAAE,GAAG,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAtF;AACV,gBAAQ,gBAAgB,IAAI,EAAE;AAAA,MAChC;AACA,aAAO,UAAU,EAAE,QAAQ,EAAE,MAAM,OAAO;AAAA,IAC5C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM;AACb,UAAM,SAAS,KAAK,OAAO;AAC3B,WAAO,YAAY,UAAU,KAAK,WAAW,IAAI;AACjD,WAAO;AAAA,EACT;AAAA,EACA,UAAU,CAAC,YAAY;AACrB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EACA,WAAW,CAAC,YAAY;AACtB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,MAAM,oBAAoB,SAAS;AACvC,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS;AACX,UAAI,OAAO,YAAY,YAAY;AACjC,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB,QAAQ;AACxB,YAAI,QAAQ,mBAAmB,OAAO;AACpC,2BAAiB,wBAAC,YAAY,SAAb;AAAA,QACnB,OAAO;AACL,2BAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,gBAAgB,CAAC,MAAM;AACxC,YAAM,WAAW,cAAc,CAAC;AAChC,aAAO,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAAA,IACvD,IAAI,CAAC,MAAM;AACT,UAAI,mBAAmB;AACvB,UAAI;AACF,2BAAmB,EAAE;AAAA,MACvB,QAAE;AAAA,MACF;AACA,aAAO,CAAC,EAAE,KAAK,gBAAgB;AAAA,IACjC;AACA,wBAAoB,MAAM;AACxB,YAAM,aAAa,UAAU,KAAK,WAAW,IAAI;AACjD,YAAM,mBAAmB,eAAe,MAAM,IAAI,WAAW;AAC7D,aAAO,CAAC,YAAY;AAClB,cAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAW,IAAI,SAAS,MAAM,gBAAgB,KAAK;AACvD,eAAO,IAAI,QAAQ,KAAK,OAAO;AAAA,MACjC;AAAA,IACF,GAAG;AACH,UAAM,UAAU,8BAAO,GAAG,SAAS;AACjC,YAAM,MAAM,MAAM,mBAAmB,eAAe,EAAE,IAAI,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;AAChF,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AACA,YAAM,KAAK;AAAA,IACb,GANgB;AAOhB,SAAK,UAAU,iBAAiB,UAAU,MAAM,GAAG,GAAG,OAAO;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ,MAAM,SAAS;AAC/B,aAAS,OAAO,YAAY;AAC5B,WAAO,UAAU,KAAK,WAAW,IAAI;AACrC,UAAM,IAAI,EAAE,UAAU,KAAK,WAAW,MAAM,QAAQ,QAAQ;AAC5D,SAAK,OAAO,IAAI,QAAQ,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1C,SAAK,OAAO,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,aAAa,KAAK,GAAG;AACnB,QAAI,eAAe,OAAO;AACxB,aAAO,KAAK,aAAa,KAAK,CAAC;AAAA,IACjC;AACA,UAAM;AAAA,EACR;AAAA,EACA,UAAU,SAAS,cAAc,KAAK,QAAQ;AAC5C,QAAI,WAAW,QAAQ;AACrB,cAAQ,YAAY,IAAI,SAAS,MAAM,MAAM,KAAK,UAAU,SAAS,cAAc,KAAK,KAAK,CAAC,GAAG;AAAA,IACnG;AACA,UAAM,OAAO,KAAK,QAAQ,SAAS,EAAE,IAAI,CAAC;AAC1C,UAAM,cAAc,KAAK,OAAO,MAAM,QAAQ,IAAI;AAClD,UAAM,IAAI,IAAI,QAAQ,SAAS;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK;AAAA,IACxB,CAAC;AACD,QAAI,YAAY,CAAC,EAAE,WAAW,GAAG;AAC/B,UAAI;AACJ,UAAI;AACF,cAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY;AAC3C,YAAE,MAAM,MAAM,KAAK,iBAAiB,CAAC;AAAA,QACvC,CAAC;AAAA,MACH,SAAS,KAAP;AACA,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AACA,aAAO,eAAe,UAAU,IAAI;AAAA,QAClC,CAAC,aAAa,aAAa,EAAE,YAAY,EAAE,MAAM,KAAK,iBAAiB,CAAC;AAAA,MAC1E,EAAE,MAAM,CAAC,QAAQ,KAAK,aAAa,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,iBAAiB,CAAC;AAAA,IAC9E;AACA,UAAM,WAAW,QAAQ,YAAY,CAAC,GAAG,KAAK,cAAc,KAAK,gBAAgB;AACjF,YAAQ,YAAY;AAClB,UAAI;AACF,cAAM,UAAU,MAAM,SAAS,CAAC;AAChC,YAAI,CAAC,QAAQ,WAAW;AACtB,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAO,QAAQ;AAAA,MACjB,SAAS,KAAP;AACA,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,QAAQ,CAAC,YAAY,SAAS;AAC5B,WAAO,KAAK,UAAU,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,MAAM;AAAA,EACjE;AAAA,EACA,UAAU,CAAC,OAAO,aAAa,KAAK,iBAAiB;AACnD,QAAI,iBAAiB,SAAS;AAC5B,aAAO,KAAK,MAAM,cAAc,IAAI,QAAQ,OAAO,WAAW,IAAI,OAAO,KAAK,YAAY;AAAA,IAC5F;AACA,YAAQ,MAAM,SAAS;AACvB,WAAO,KAAK;AAAA,MACV,IAAI;AAAA,QACF,eAAe,KAAK,KAAK,IAAI,QAAQ,mBAAmB,UAAU,KAAK,KAAK;AAAA,QAC5E;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,MAAM;AACX,qBAAiB,SAAS,CAAC,UAAU;AACnC,YAAM,YAAY,KAAK,UAAU,MAAM,SAAS,OAAO,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,IACtF,CAAC;AAAA,EACH;AACF,GAzNW;;;AChBX,IAAI,oBAAoB;AACxB,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,aAAa,OAAO;AACxB,IAAI,kBAAkB,IAAI,IAAI,aAAa;AAC3C,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO,EAAE,WAAW,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,EAC3C;AACA,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,6BAA6B,MAAM,2BAA2B;AACtE,WAAO;AAAA,EACT,WAAW,MAAM,6BAA6B,MAAM,2BAA2B;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,MAAM,mBAAmB;AAC3B,WAAO;AAAA,EACT,WAAW,MAAM,mBAAmB;AAClC,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,EAAE,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE,SAAS,EAAE;AAC/D;AAlBS;AAmBT,IAAI,OAAO,6BAAM;AAAA,EACf;AAAA,EACA;AAAA,EACA,YAA4B,uBAAO,OAAO,IAAI;AAAA,EAC9C,OAAO,QAAQ,OAAO,UAAU,SAAS,oBAAoB;AAC3D,QAAI,OAAO,WAAW,GAAG;AACvB,UAAI,KAAK,WAAW,QAAQ;AAC1B,cAAM;AAAA,MACR;AACA,UAAI,oBAAoB;AACtB;AAAA,MACF;AACA,WAAK,SAAS;AACd;AAAA,IACF;AACA,UAAM,CAAC,OAAO,GAAG,UAAU,IAAI;AAC/B,UAAM,UAAU,UAAU,MAAM,WAAW,WAAW,IAAI,CAAC,IAAI,IAAI,yBAAyB,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,UAAU,OAAO,CAAC,IAAI,IAAI,yBAAyB,IAAI,MAAM,MAAM,6BAA6B;AAC9N,QAAI;AACJ,QAAI,SAAS;AACX,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,YAAY,QAAQ,CAAC,KAAK;AAC9B,UAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,oBAAY,UAAU,QAAQ,0BAA0B,KAAK;AAC7D,YAAI,YAAY,KAAK,SAAS,GAAG;AAC/B,gBAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO,KAAK,UAAU,SAAS;AAC/B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,MAAM,6BAA6B,MAAM;AAAA,QAClD,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,SAAS,IAAI,IAAI,KAAK;AAC5C,YAAI,SAAS,IAAI;AACf,eAAK,YAAY,QAAQ;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,CAAC,sBAAsB,SAAS,IAAI;AACtC,iBAAS,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;AAAA,MACtC;AAAA,IACF,OAAO;AACL,aAAO,KAAK,UAAU,KAAK;AAC3B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,EAAE,SAAS,KAAK,MAAM,6BAA6B,MAAM;AAAA,QAClE,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,KAAK,IAAI,IAAI,KAAK;AAAA,MAC1C;AAAA,IACF;AACA,SAAK,OAAO,YAAY,OAAO,UAAU,SAAS,kBAAkB;AAAA,EACtE;AAAA,EACA,iBAAiB;AACf,UAAM,YAAY,OAAO,KAAK,KAAK,SAAS,EAAE,KAAK,UAAU;AAC7D,UAAM,UAAU,UAAU,IAAI,CAAC,MAAM;AACnC,YAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,cAAQ,OAAO,EAAE,cAAc,WAAW,IAAI,MAAM,EAAE,cAAc,gBAAgB,IAAI,CAAC,IAAI,KAAK,MAAM,KAAK,EAAE,eAAe;AAAA,IAChI,CAAC;AACD,QAAI,OAAO,KAAK,WAAW,UAAU;AACnC,cAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,IACnC;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACA,WAAO,QAAQ,QAAQ,KAAK,GAAG,IAAI;AAAA,EACrC;AACF,GA9EW;;;ACvBX,IAAI,OAAO,6BAAM;AAAA,EACf,WAAW,EAAE,UAAU,EAAE;AAAA,EACzB,QAAQ,IAAI,KAAK;AAAA,EACjB,OAAO,MAAM,OAAO,oBAAoB;AACtC,UAAM,aAAa,CAAC;AACpB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,OAAO;AAClB,UAAI,WAAW;AACf,aAAO,KAAK,QAAQ,cAAc,CAAC,MAAM;AACvC,cAAM,OAAO,MAAM;AACnB,eAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACpB;AACA,mBAAW;AACX,eAAO;AAAA,MACT,CAAC;AACD,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,KAAK,MAAM,0BAA0B,KAAK,CAAC;AAC1D,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI;AAClC,iBAAO,CAAC,IAAI,OAAO,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,OAAO,YAAY,KAAK,UAAU,kBAAkB;AAC9E,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,QAAI,SAAS,KAAK,MAAM,eAAe;AACvC,QAAI,WAAW,IAAI;AACjB,aAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,IACtB;AACA,QAAI,eAAe;AACnB,UAAM,sBAAsB,CAAC;AAC7B,UAAM,sBAAsB,CAAC;AAC7B,aAAS,OAAO,QAAQ,yBAAyB,CAAC,GAAG,cAAc,eAAe;AAChF,UAAI,iBAAiB,QAAQ;AAC3B,4BAAoB,EAAE,YAAY,IAAI,OAAO,YAAY;AACzD,eAAO;AAAA,MACT;AACA,UAAI,eAAe,QAAQ;AACzB,4BAAoB,OAAO,UAAU,CAAC,IAAI,EAAE;AAC5C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO,CAAC,IAAI,OAAO,IAAI,QAAQ,GAAG,qBAAqB,mBAAmB;AAAA,EAC5E;AACF,GArDW;;;ACOX,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC;AAChE,IAAI,sBAAsC,uBAAO,OAAO,IAAI;AAC5D,SAAS,oBAAoB,MAAM;AACjC,SAAO,oBAAoB,IAAI,MAAM,IAAI;AAAA,IACvC,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,MAC3B;AAAA,MACA,CAAC,GAAG,aAAa,WAAW,KAAK,aAAa;AAAA,IAChD;AAAA,EACF;AACF;AAPS;AAQT,SAAS,2BAA2B;AAClC,wBAAsC,uBAAO,OAAO,IAAI;AAC1D;AAFS;AAGT,SAAS,mCAAmC,QAAQ;AAClD,QAAM,OAAO,IAAI,KAAK;AACtB,QAAM,cAAc,CAAC;AACrB,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT;AACA,QAAM,2BAA2B,OAAO;AAAA,IACtC,CAAC,UAAU,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,GAAG,GAAG,KAAK;AAAA,EAChD,EAAE;AAAA,IACA,CAAC,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW,KAAK,MAAM,YAAY,IAAI,YAAY,KAAK,MAAM,SAAS,MAAM;AAAA,EACpG;AACA,QAAM,YAA4B,uBAAO,OAAO,IAAI;AACpD,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,yBAAyB,QAAQ,IAAI,KAAK,KAAK;AAC3E,UAAM,CAAC,oBAAoB,MAAM,QAAQ,IAAI,yBAAyB,CAAC;AACvE,QAAI,oBAAoB;AACtB,gBAAU,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC,CAAC,GAAG,UAAU;AAAA,IAChG,OAAO;AACL;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,mBAAa,KAAK,OAAO,MAAM,GAAG,kBAAkB;AAAA,IACtD,SAAS,GAAP;AACA,YAAM,MAAM,aAAa,IAAI,qBAAqB,IAAI,IAAI;AAAA,IAC5D;AACA,QAAI,oBAAoB;AACtB;AAAA,IACF;AACA,gBAAY,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,GAAG,UAAU,MAAM;AACjD,YAAM,gBAAgC,uBAAO,OAAO,IAAI;AACxD,oBAAc;AACd,aAAO,cAAc,GAAG,cAAc;AACpC,cAAM,CAAC,KAAK,KAAK,IAAI,WAAW,UAAU;AAC1C,sBAAc,GAAG,IAAI;AAAA,MACvB;AACA,aAAO,CAAC,GAAG,aAAa;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,QAAM,CAAC,QAAQ,qBAAqB,mBAAmB,IAAI,KAAK,YAAY;AAC5E,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,aAAS,IAAI,GAAG,OAAO,YAAY,CAAC,EAAE,QAAQ,IAAI,MAAM,KAAK;AAC3D,YAAM,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;AACjC,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,eAAS,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,MAAM,KAAK;AACjD,YAAI,KAAK,CAAC,CAAC,IAAI,oBAAoB,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,CAAC;AACpB,aAAW,KAAK,qBAAqB;AACnC,eAAW,CAAC,IAAI,YAAY,oBAAoB,CAAC,CAAC;AAAA,EACpD;AACA,SAAO,CAAC,QAAQ,YAAY,SAAS;AACvC;AAxDS;AAyDT,SAAS,eAAe,YAAY,MAAM;AACxC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,aAAW,KAAK,OAAO,KAAK,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG;AAC3E,QAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,GAAG;AACrC,aAAO,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAVS;AAWT,IAAI,eAAe,6BAAM;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAC5E,SAAK,UAAU,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAAA,EAC1E;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,cAAc,CAAC,QAAQ;AAC1B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,QAAI,CAAC,WAAW,MAAM,GAAG;AACvB;AACA,OAAC,YAAY,MAAM,EAAE,QAAQ,CAAC,eAAe;AAC3C,mBAAW,MAAM,IAAoB,uBAAO,OAAO,IAAI;AACvD,eAAO,KAAK,WAAW,eAAe,CAAC,EAAE,QAAQ,CAAC,MAAM;AACtD,qBAAW,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,WAAW,eAAe,EAAE,CAAC,CAAC;AAAA,QAC5D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,MAAM,MAAM,KAAK,CAAC,GAAG;AAC9C,QAAI,MAAM,KAAK,IAAI,GAAG;AACpB,YAAM,KAAK,oBAAoB,IAAI;AACnC,UAAI,WAAW,iBAAiB;AAC9B,eAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,qBAAW,CAAC,EAAE,IAAI,MAAM,eAAe,WAAW,CAAC,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,QACvH,CAAC;AAAA,MACH,OAAO;AACL,mBAAW,MAAM,EAAE,IAAI,MAAM,eAAe,WAAW,MAAM,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,MACjI;AACA,aAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM;AACxC,eAAG,KAAK,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC3D,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,OAAO,CAAC,CAAC,EAAE;AAAA,YACrB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC9D;AAAA,QACF;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,UAAM,QAAQ,uBAAuB,IAAI,KAAK,CAAC,IAAI;AACnD,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,QAAQ,MAAM,CAAC;AACrB,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,CAAC,EAAE,KAAK,MAAM;AAAA,YACnB,GAAG,eAAe,WAAW,CAAC,GAAG,KAAK,KAAK,eAAe,WAAW,eAAe,GAAG,KAAK,KAAK,CAAC;AAAA,UACpG;AACA,iBAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,aAAa,MAAM,IAAI,CAAC,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,6BAAyB;AACzB,UAAM,WAAW,KAAK,kBAAkB;AACxC,SAAK,QAAQ,CAAC,SAAS,UAAU;AAC/B,YAAM,UAAU,SAAS,OAAO,KAAK,SAAS,eAAe;AAC7D,YAAM,cAAc,QAAQ,CAAC,EAAE,KAAK;AACpC,UAAI,aAAa;AACf,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,MAAM,MAAM,QAAQ,CAAC,CAAC;AACpC,UAAI,CAAC,OAAO;AACV,eAAO,CAAC,CAAC,GAAG,UAAU;AAAA,MACxB;AACA,YAAM,QAAQ,MAAM,QAAQ,IAAI,CAAC;AACjC,aAAO,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,KAAK;AAAA,IAClC;AACA,WAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,EAChC;AAAA,EACA,oBAAoB;AAClB,UAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAO,KAAK,KAAK,OAAO,EAAE,OAAO,OAAO,KAAK,KAAK,WAAW,CAAC,EAAE,QAAQ,CAAC,WAAW;AAClF,eAAS,MAAM,MAAM,KAAK,cAAc,MAAM;AAAA,IAChD,CAAC;AACD,SAAK,cAAc,KAAK,UAAU;AAClC,WAAO;AAAA,EACT;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,SAAS,CAAC;AAChB,QAAI,cAAc,WAAW;AAC7B,KAAC,KAAK,aAAa,KAAK,OAAO,EAAE,QAAQ,CAAC,MAAM;AAC9C,YAAM,WAAW,EAAE,MAAM,IAAI,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;AAC9F,UAAI,SAAS,WAAW,GAAG;AACzB,wBAAgB;AAChB,eAAO,KAAK,GAAG,QAAQ;AAAA,MACzB,WAAW,WAAW,iBAAiB;AACrC,eAAO;AAAA,UACL,GAAG,OAAO,KAAK,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AAAA,QACnF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,mCAAmC,MAAM;AAAA,IAClD;AAAA,EACF;AACF,GA/GmB;;;ACzFnB,IAAI,cAAc,6BAAM;AAAA,EACtB,OAAO;AAAA,EACP,WAAW,CAAC;AAAA,EACZ,UAAU,CAAC;AAAA,EACX,YAAY,MAAM;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,SAAK,QAAQ,KAAK,CAAC,QAAQ,MAAM,OAAO,CAAC;AAAA,EAC3C;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,IAAI,KAAK,KAAK;AACnB,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI;AACF,iBAAS,KAAK,GAAG,OAAO,OAAO,QAAQ,KAAK,MAAM,MAAM;AACtD,iBAAO,IAAI,GAAG,OAAO,EAAE,CAAC;AAAA,QAC1B;AACA,cAAM,OAAO,MAAM,QAAQ,IAAI;AAAA,MACjC,SAAS,GAAP;AACA,YAAI,aAAa,sBAAsB;AACrC;AAAA,QACF;AACA,cAAM;AAAA,MACR;AACA,WAAK,QAAQ,OAAO,MAAM,KAAK,MAAM;AACrC,WAAK,WAAW,CAAC,MAAM;AACvB,WAAK,UAAU;AACf;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,SAAK,OAAO,iBAAiB,KAAK,aAAa;AAC/C,WAAO;AAAA,EACT;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,WAAW,KAAK,SAAS,WAAW,GAAG;AAC9C,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,WAAO,KAAK,SAAS,CAAC;AAAA,EACxB;AACF,GApDkB;;;ACClB,IAAI,cAA8B,uBAAO,OAAO,IAAI;AACpD,IAAIC,QAAO,6BAAM;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY,QAAQ,SAAS,UAAU;AACrC,SAAK,YAAY,YAA4B,uBAAO,OAAO,IAAI;AAC/D,SAAK,WAAW,CAAC;AACjB,QAAI,UAAU,SAAS;AACrB,YAAM,IAAoB,uBAAO,OAAO,IAAI;AAC5C,QAAE,MAAM,IAAI,EAAE,SAAS,cAAc,CAAC,GAAG,OAAO,EAAE;AAClD,WAAK,WAAW,CAAC,CAAC;AAAA,IACpB;AACA,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,OAAO,QAAQ,MAAM,SAAS;AAC5B,SAAK,SAAS,EAAE,KAAK;AACrB,QAAI,UAAU;AACd,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,QAAQ,MAAM,IAAI,CAAC;AACzB,YAAM,UAAU,WAAW,GAAG,KAAK;AACnC,YAAM,MAAM,MAAM,QAAQ,OAAO,IAAI,QAAQ,CAAC,IAAI;AAClD,UAAI,OAAO,QAAQ,WAAW;AAC5B,kBAAU,QAAQ,UAAU,GAAG;AAC/B,YAAI,SAAS;AACX,uBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC9B;AACA;AAAA,MACF;AACA,cAAQ,UAAU,GAAG,IAAI,IAAIA,MAAK;AAClC,UAAI,SAAS;AACX,gBAAQ,UAAU,KAAK,OAAO;AAC9B,qBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC9B;AACA,gBAAU,QAAQ,UAAU,GAAG;AAAA,IACjC;AACA,YAAQ,SAAS,KAAK;AAAA,MACpB,CAAC,MAAM,GAAG;AAAA,QACR;AAAA,QACA,cAAc,aAAa,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AAAA,QACjE,OAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,QAAQ,YAAY,QAAQ;AAChD,UAAM,cAAc,CAAC;AACrB,aAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,aAAa,EAAE,MAAM,KAAK,EAAE,eAAe;AACjD,YAAM,eAAe,CAAC;AACtB,UAAI,eAAe,QAAQ;AACzB,mBAAW,SAAyB,uBAAO,OAAO,IAAI;AACtD,oBAAY,KAAK,UAAU;AAC3B,YAAI,eAAe,eAAe,UAAU,WAAW,aAAa;AAClE,mBAAS,KAAK,GAAG,OAAO,WAAW,aAAa,QAAQ,KAAK,MAAM,MAAM;AACvE,kBAAM,MAAM,WAAW,aAAa,EAAE;AACtC,kBAAM,YAAY,aAAa,WAAW,KAAK;AAC/C,uBAAW,OAAO,GAAG,IAAI,SAAS,GAAG,KAAK,CAAC,YAAY,OAAO,GAAG,IAAI,WAAW,GAAG,KAAK,SAAS,GAAG;AACpG,yBAAa,WAAW,KAAK,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,MAAM;AACnB,UAAM,cAAc,CAAC;AACrB,SAAK,UAAU;AACf,UAAM,UAAU;AAChB,QAAI,WAAW,CAAC,OAAO;AACvB,UAAM,QAAQ,UAAU,IAAI;AAC5B,UAAM,gBAAgB,CAAC;AACvB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,SAAS,MAAM,MAAM;AAC3B,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,KAAK;AACrD,cAAM,OAAO,SAAS,CAAC;AACvB,cAAM,WAAW,KAAK,UAAU,IAAI;AACpC,YAAI,UAAU;AACZ,mBAAS,UAAU,KAAK;AACxB,cAAI,QAAQ;AACV,gBAAI,SAAS,UAAU,GAAG,GAAG;AAC3B,0BAAY;AAAA,gBACV,GAAG,KAAK,gBAAgB,SAAS,UAAU,GAAG,GAAG,QAAQ,KAAK,OAAO;AAAA,cACvE;AAAA,YACF;AACA,wBAAY,KAAK,GAAG,KAAK,gBAAgB,UAAU,QAAQ,KAAK,OAAO,CAAC;AAAA,UAC1E,OAAO;AACL,sBAAU,KAAK,QAAQ;AAAA,UACzB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,OAAO,KAAK,UAAU,QAAQ,IAAI,MAAM,KAAK;AAC3D,gBAAM,UAAU,KAAK,UAAU,CAAC;AAChC,gBAAM,SAAS,KAAK,YAAY,cAAc,CAAC,IAAI,EAAE,GAAG,KAAK,QAAQ;AACrE,cAAI,YAAY,KAAK;AACnB,kBAAM,UAAU,KAAK,UAAU,GAAG;AAClC,gBAAI,SAAS;AACX,0BAAY,KAAK,GAAG,KAAK,gBAAgB,SAAS,QAAQ,KAAK,OAAO,CAAC;AACvE,sBAAQ,UAAU;AAClB,wBAAU,KAAK,OAAO;AAAA,YACxB;AACA;AAAA,UACF;AACA,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA,gBAAM,CAAC,KAAK,MAAM,OAAO,IAAI;AAC7B,gBAAM,QAAQ,KAAK,UAAU,GAAG;AAChC,gBAAM,iBAAiB,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAC9C,cAAI,mBAAmB,QAAQ;AAC7B,kBAAM,IAAI,QAAQ,KAAK,cAAc;AACrC,gBAAI,GAAG;AACL,qBAAO,IAAI,IAAI,EAAE,CAAC;AAClB,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,KAAK,SAAS,MAAM,CAAC;AAC7E,kBAAI,OAAO,KAAK,MAAM,SAAS,EAAE,QAAQ;AACvC,sBAAM,UAAU;AAChB,sBAAM,iBAAiB,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,UAAU;AACnD,sBAAM,iBAAiB,cAAc,cAAc,MAAM,CAAC;AAC1D,+BAAe,KAAK,KAAK;AAAA,cAC3B;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,YAAY,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAC1C,mBAAO,IAAI,IAAI;AACf,gBAAI,QAAQ;AACV,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,QAAQ,KAAK,OAAO,CAAC;AAC7E,kBAAI,MAAM,UAAU,GAAG,GAAG;AACxB,4BAAY;AAAA,kBACV,GAAG,KAAK,gBAAgB,MAAM,UAAU,GAAG,GAAG,QAAQ,QAAQ,KAAK,OAAO;AAAA,gBAC5E;AAAA,cACF;AAAA,YACF,OAAO;AACL,oBAAM,UAAU;AAChB,wBAAU,KAAK,KAAK;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,iBAAW,UAAU,OAAO,cAAc,MAAM,KAAK,CAAC,CAAC;AAAA,IACzD;AACA,QAAI,YAAY,SAAS,GAAG;AAC1B,kBAAY,KAAK,CAAC,GAAG,MAAM;AACzB,eAAO,EAAE,QAAQ,EAAE;AAAA,MACrB,CAAC;AAAA,IACH;AACA,WAAO,CAAC,YAAY,IAAI,CAAC,EAAE,SAAS,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC;AAAA,EACrE;AACF,GA1JW;;;ACDX,IAAI,aAAa,6BAAM;AAAA,EACrB,OAAO;AAAA,EACP;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,IAAIC,MAAK;AAAA,EACxB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,UAAU,uBAAuB,IAAI;AAC3C,QAAI,SAAS;AACX,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,aAAK,MAAM,OAAO,QAAQ,QAAQ,CAAC,GAAG,OAAO;AAAA,MAC/C;AACA;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,MAAM,OAAO;AAAA,EACzC;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,WAAO,KAAK,MAAM,OAAO,QAAQ,IAAI;AAAA,EACvC;AACF,GAnBiB;;;ACEjB,IAAIC,QAAO,qCAAc,KAAS;AAAA,EAChC,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,OAAO;AACb,SAAK,SAAS,QAAQ,UAAU,IAAI,YAAY;AAAA,MAC9C,SAAS,CAAC,IAAI,aAAa,GAAG,IAAI,WAAW,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AACF,GAPW;;;ACJX,IAAI,OAAO,wBAAC,YAAY;AACtB,QAAM,WAAW;AAAA,IACf,QAAQ;AAAA,IACR,cAAc,CAAC,OAAO,QAAQ,OAAO,QAAQ,UAAU,OAAO;AAAA,IAC9D,cAAc,CAAC;AAAA,IACf,eAAe,CAAC;AAAA,EAClB;AACA,QAAM,OAAO;AAAA,IACX,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,mBAAmB,CAAC,eAAe;AACvC,QAAI,OAAO,eAAe,UAAU;AAClC,UAAI,eAAe,KAAK;AACtB,eAAO,MAAM;AAAA,MACf,OAAO;AACL,eAAO,CAAC,WAAW,eAAe,SAAS,SAAS;AAAA,MACtD;AAAA,IACF,WAAW,OAAO,eAAe,YAAY;AAC3C,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC,WAAW,WAAW,SAAS,MAAM,IAAI,SAAS;AAAA,IAC5D;AAAA,EACF,GAAG,KAAK,MAAM;AACd,QAAM,oBAAoB,CAAC,qBAAqB;AAC9C,QAAI,OAAO,qBAAqB,YAAY;AAC1C,aAAO;AAAA,IACT,WAAW,MAAM,QAAQ,gBAAgB,GAAG;AAC1C,aAAO,MAAM;AAAA,IACf,OAAO;AACL,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF,GAAG,KAAK,YAAY;AACpB,SAAO,sCAAe,MAAM,GAAG,MAAM;AACnC,aAAS,IAAI,KAAK,OAAO;AACvB,QAAE,IAAI,QAAQ,IAAI,KAAK,KAAK;AAAA,IAC9B;AAFS;AAGT,UAAM,cAAc,gBAAgB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AACnE,QAAI,aAAa;AACf,UAAI,+BAA+B,WAAW;AAAA,IAChD;AACA,QAAI,KAAK,WAAW,KAAK;AACvB,YAAM,eAAe,EAAE,IAAI,OAAO,MAAM;AACxC,UAAI,cAAc;AAChB,YAAI,QAAQ,YAAY;AAAA,MAC1B,OAAO;AACL,YAAI,QAAQ,QAAQ;AAAA,MACtB;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,UAAI,oCAAoC,MAAM;AAAA,IAChD;AACA,QAAI,KAAK,eAAe,QAAQ;AAC9B,UAAI,iCAAiC,KAAK,cAAc,KAAK,GAAG,CAAC;AAAA,IACnE;AACA,QAAI,EAAE,IAAI,WAAW,WAAW;AAC9B,UAAI,KAAK,UAAU,MAAM;AACvB,YAAI,0BAA0B,KAAK,OAAO,SAAS,CAAC;AAAA,MACtD;AACA,YAAM,eAAe,iBAAiB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AACrE,UAAI,aAAa,QAAQ;AACvB,YAAI,gCAAgC,aAAa,KAAK,GAAG,CAAC;AAAA,MAC5D;AACA,UAAI,UAAU,KAAK;AACnB,UAAI,CAAC,SAAS,QAAQ;AACpB,cAAM,iBAAiB,EAAE,IAAI,OAAO,gCAAgC;AACpE,YAAI,gBAAgB;AAClB,oBAAU,eAAe,MAAM,SAAS;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,SAAS,QAAQ;AACnB,YAAI,gCAAgC,QAAQ,KAAK,GAAG,CAAC;AACrD,UAAE,IAAI,QAAQ,OAAO,QAAQ,gCAAgC;AAAA,MAC/D;AACA,QAAE,IAAI,QAAQ,OAAO,gBAAgB;AACrC,QAAE,IAAI,QAAQ,OAAO,cAAc;AACnC,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,EAAE,IAAI;AAAA,QACf,QAAQ;AAAA,QACR,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA,UAAM,KAAK;AAAA,EACb,GAlDO;AAmDT,GApFW;;;ACAX,SAAS,kBAAkB;AACzB,QAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,QAAM,YAAY,OAAO,MAAM,YAAY,YAAY,KAAK,UAAU,YAAY,SAAS,cAAc,SAAS,MAAM;AACxH,SAAO,CAAC;AACV;AAJS;AAKT,eAAe,uBAAuB;AACpC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,YAAY,cAAc,UAAU,UAAU,cAAc,uBAAuB,gBAAgB,MAAM,OAAO,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC,gBAAgB;AAC3K,SAAO,CAAC;AACV;AAJe;;;ACJf,IAAI,WAAW,wBAAC,UAAU;AACxB,QAAM,CAAC,WAAW,SAAS,IAAI,CAAC,KAAK,GAAG;AACxC,QAAM,aAAa,MAAM,IAAI,CAAC,MAAM,EAAE,QAAQ,4BAA4B,OAAO,SAAS,CAAC;AAC3F,SAAO,WAAW,KAAK,SAAS;AAClC,GAJe;AAKf,IAAI,OAAO,wBAAC,UAAU;AACpB,QAAM,QAAQ,KAAK,IAAI,IAAI;AAC3B,SAAO,SAAS,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC;AAC9E,GAHW;AAIX,IAAI,cAAc,8BAAO,WAAW;AAClC,QAAM,eAAe,MAAM,qBAAqB;AAChD,MAAI,cAAc;AAChB,YAAQ,SAAS,MAAM,GAAG;AAAA,MACxB,KAAK;AACH,eAAO,WAAW;AAAA,MACpB,KAAK;AACH,eAAO,WAAW;AAAA,MACpB,KAAK;AACH,eAAO,WAAW;AAAA,MACpB,KAAK;AACH,eAAO,WAAW;AAAA,IACtB;AAAA,EACF;AACA,SAAO,GAAG;AACZ,GAfkB;AAgBlB,eAAe,IAAI,IAAI,QAAQ,QAAQ,MAAM,SAAS,GAAG,SAAS;AAChE,QAAM,MAAM,WAAW,QAAuB,GAAG,UAAU,UAAU,SAAS,GAAG,UAAU,UAAU,QAAQ,MAAM,YAAY,MAAM,KAAK;AAC1I,KAAG,GAAG;AACR;AAHe;AAIf,IAAI,SAAS,wBAAC,KAAK,QAAQ,QAAQ;AACjC,SAAO,sCAAe,QAAQ,GAAG,MAAM;AACrC,UAAM,EAAE,QAAQ,IAAI,IAAI,EAAE;AAC1B,UAAM,OAAO,IAAI,MAAM,IAAI,QAAQ,KAAK,CAAC,CAAC;AAC1C,UAAM,IAAI,IAAI,OAAsB,QAAQ,IAAI;AAChD,UAAM,QAAQ,KAAK,IAAI;AACvB,UAAM,KAAK;AACX,UAAM,IAAI,IAAI,OAAsB,QAAQ,MAAM,EAAE,IAAI,QAAQ,KAAK,KAAK,CAAC;AAAA,EAC7E,GAPO;AAQT,GATa;;;AC9Bb,IAAI,aAAa,wBAAC,YAAY;AAC5B,QAAM,cAAc,SAAS,SAAS;AACtC,SAAO,sCAAe,YAAY,GAAG,MAAM;AACzC,UAAM,SAAS,EAAE,IAAI,MAAM,WAAW,KAAK,EAAE,IAAI,MAAM,WAAW,MAAM;AACxE,UAAM,KAAK;AACX,QAAI,UAAU,EAAE,IAAI,QAAQ,IAAI,cAAc,GAAG,WAAW,kBAAkB,GAAG;AAC/E,YAAM,MAAM,MAAM,EAAE,IAAI,KAAK;AAC7B,QAAE,MAAM,IAAI,SAAS,KAAK,UAAU,KAAK,MAAM,SAAS,SAAS,CAAC,GAAG,EAAE,GAAG;AAAA,IAC5E;AAAA,EACF,GAPO;AAQT,GAViB;;;ACCjB,IAAI,cAAc;AAAA,EAChB,2BAA2B,CAAC,gCAAgC,cAAc;AAAA,EAC1E,2BAA2B,CAAC,gCAAgC,aAAa;AAAA,EACzE,yBAAyB,CAAC,8BAA8B,aAAa;AAAA,EACrE,oBAAoB,CAAC,wBAAwB,IAAI;AAAA,EACjD,gBAAgB,CAAC,mBAAmB,aAAa;AAAA,EACjD,yBAAyB,CAAC,6BAA6B,qCAAqC;AAAA,EAC5F,qBAAqB,CAAC,0BAA0B,SAAS;AAAA,EACzD,qBAAqB,CAAC,0BAA0B,KAAK;AAAA,EACrD,kBAAkB,CAAC,sBAAsB,QAAQ;AAAA,EACjD,eAAe,CAAC,mBAAmB,YAAY;AAAA,EAC/C,+BAA+B,CAAC,qCAAqC,MAAM;AAAA,EAC3E,gBAAgB,CAAC,oBAAoB,GAAG;AAC1C;AACA,IAAI,kBAAkB;AAAA,EACpB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB,CAAC;AACtB;AAeA,IAAI,gBAAgB,wBAAC,kBAAkB;AACrC,QAAM,UAAU,EAAE,GAAG,iBAAiB,GAAG,cAAc;AACvD,QAAM,eAAe,mBAAmB,OAAO;AAC/C,QAAM,YAAY,CAAC;AACnB,MAAI,QAAQ,uBAAuB;AACjC,UAAM,CAAC,UAAU,KAAK,IAAI,iBAAiB,QAAQ,qBAAqB;AACxE,QAAI,UAAU;AACZ,gBAAU,KAAK,QAAQ;AAAA,IACzB;AACA,iBAAa,KAAK,CAAC,2BAA2B,KAAK,CAAC;AAAA,EACtD;AACA,MAAI,QAAQ,iCAAiC;AAC3C,UAAM,CAAC,UAAU,KAAK,IAAI,iBAAiB,QAAQ,+BAA+B;AAClF,QAAI,UAAU;AACZ,gBAAU,KAAK,QAAQ;AAAA,IACzB;AACA,iBAAa,KAAK,CAAC,uCAAuC,KAAK,CAAC;AAAA,EAClE;AACA,MAAI,QAAQ,qBAAqB,OAAO,KAAK,QAAQ,iBAAiB,EAAE,SAAS,GAAG;AAClF,iBAAa,KAAK;AAAA,MAChB;AAAA,MACA,+BAA+B,QAAQ,iBAAiB;AAAA,IAC1D,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,oBAAoB;AAC9B,iBAAa,KAAK,CAAC,uBAAuB,sBAAsB,QAAQ,kBAAkB,CAAC,CAAC;AAAA,EAC9F;AACA,MAAI,QAAQ,UAAU;AACpB,iBAAa,KAAK,CAAC,aAAa,mBAAmB,QAAQ,QAAQ,CAAC,CAAC;AAAA,EACvE;AACA,SAAO,sCAAe,eAAe,KAAK,MAAM;AAC9C,UAAM,qBAAqB,UAAU,WAAW,IAAI,eAAe,UAAU,OAAO,CAAC,KAAK,OAAO,GAAG,KAAK,GAAG,GAAG,YAAY;AAC3H,UAAM,KAAK;AACX,eAAW,KAAK,kBAAkB;AAClC,QAAI,SAAS,iBAAiB;AAC5B,UAAI,IAAI,QAAQ,OAAO,cAAc;AAAA,IACvC;AAAA,EACF,GAPO;AAQT,GAtCoB;AAuCpB,SAAS,mBAAmB,SAAS;AACnC,SAAO,OAAO,QAAQ,WAAW,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,YAAY,MAAM;AAC9F,UAAM,gBAAgB,QAAQ,GAAG;AACjC,WAAO,OAAO,kBAAkB,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa,IAAI;AAAA,EAChF,CAAC;AACH;AALS;AAMT,SAAS,iBAAiB,uBAAuB;AAC/C,QAAM,YAAY,CAAC;AACnB,QAAM,eAAe,CAAC;AACtB,aAAW,CAAC,WAAW,KAAK,KAAK,OAAO,QAAQ,qBAAqB,GAAG;AACtE,UAAM,aAAa,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AACxD,eAAW,QAAQ,CAAC,QAAQ,MAAM;AAChC,UAAI,OAAO,WAAW,YAAY;AAChC,cAAM,QAAQ,IAAI,IAAI,IAAI,aAAa;AACvC,kBAAU,KAAK,CAAC,KAAK,WAAW;AAC9B,iBAAO,KAAK,IAAI,OAAO,KAAK,SAAS;AAAA,QACvC,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,iBAAa;AAAA,MACX,UAAU;AAAA,QACR;AAAA,QACA,CAAC,OAAO,WAAW,SAAS,MAAM,MAAM,YAAY,IAAI,MAAM,YAAY;AAAA,MAC5E;AAAA,MACA,GAAG,WAAW,QAAQ,CAAC,WAAW,CAAC,KAAK,MAAM,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACA,eAAa,IAAI;AACjB,SAAO,UAAU,WAAW,IAAI,CAAC,QAAQ,aAAa,KAAK,EAAE,CAAC,IAAI;AAAA,IAChE,CAAC,KAAK,iBAAiB,aAAa,IAAI,CAAC,WAAW;AAClD,UAAI,OAAO,CAAC,MAAM,6BAA6B,OAAO,CAAC,MAAM,uCAAuC;AAClG,cAAM,QAAQ,OAAO,CAAC,EAAE,MAAM;AAC9B,kBAAU,QAAQ,CAAC,OAAO;AACxB,aAAG,KAAK,KAAK;AAAA,QACf,CAAC;AACD,eAAO,CAAC,OAAO,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC;AAAA,MACnC,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,IACD;AAAA,EACF;AACF;AArCS;AAsCT,SAAS,+BAA+B,QAAQ;AAC9C,SAAO,OAAO,QAAQ,MAAM,EAAE,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM;AACxD,UAAM,iBAAiB,aAAa,SAAS;AAC7C,QAAI,OAAO,UAAU,WAAW;AAC9B,aAAO,GAAG,kBAAkB,QAAQ,MAAM;AAAA,IAC5C;AACA,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO,GAAG;AAAA,MACZ;AACA,UAAI,MAAM,WAAW,MAAM,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,SAAS;AACnE,eAAO,GAAG,kBAAkB,MAAM,CAAC;AAAA,MACrC;AACA,YAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,IAAI,OAAO;AACzF,aAAO,GAAG,mBAAmB,UAAU,KAAK,GAAG;AAAA,IACjD;AACA,WAAO;AAAA,EACT,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,IAAI;AAC9B;AAlBS;AAmBT,SAAS,aAAa,KAAK;AACzB,SAAO,IAAI,QAAQ,qBAAqB,OAAO,EAAE,YAAY;AAC/D;AAFS;AAGT,SAAS,sBAAsB,qBAAqB,CAAC,GAAG;AACtD,SAAO,mBAAmB,IAAI,CAAC,aAAa,GAAG,SAAS,SAAS,SAAS,MAAM,EAAE,KAAK,IAAI;AAC7F;AAFS;AAGT,SAAS,mBAAmB,WAAW,CAAC,GAAG;AACzC,SAAO,SAAS,IAAI,CAAC,WAAW,KAAK,UAAU,MAAM,CAAC,EAAE,KAAK,IAAI;AACnE;AAFS;AAGT,SAAS,WAAW,KAAK,cAAc;AACrC,eAAa,QAAQ,CAAC,CAAC,QAAQ,KAAK,MAAM;AACxC,QAAI,IAAI,QAAQ,IAAI,QAAQ,KAAK;AAAA,EACnC,CAAC;AACH;AAJS;;;AC1JF,IAAM,YAAN,MAAgB;AAAA,EACb;AAAA,EACS,UAAU;AAAA,EAE3B,YAAY,IAAiB;AAC3B,SAAK,KAAK;AAAA,EACZ;AAAA;AAAA,EAGA,MAAc,IAAO,KAAgC;AACnD,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,GAAG,IAAI,KAAK,MAAM;AAC1C,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,wBAAwB,QAAQ,KAAK;AACnD,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,MAAc,IAAO,KAAa,OAAU,KAAgC;AAC1E,QAAI;AACF,YAAM,SAAoB;AAAA,QACxB,MAAM;AAAA,QACN,aAAa,KAAK,IAAI;AAAA,QACtB,SAAS,KAAK;AAAA,MAChB;AAEA,YAAM,KAAK,GAAG,IAAI,KAAK,KAAK,UAAU,MAAM,GAAG;AAAA,QAC7C,eAAe;AAAA,MACjB,CAAC;AACD,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,wBAAwB,QAAQ,KAAK;AACnD,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,MAAc,gBAAmB,KAAwC;AACvE,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,GAAG,IAAI,KAAK,MAAM;AAC5C,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,oCAAoC,QAAQ,KAAK;AAC/D,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,eAAoC;AACxC,UAAM,SAAS,MAAM,KAAK,gBAA4B,oBAAoB;AAC1E,WAAO,QAAQ,QAAQ,CAAC;AAAA,EAC1B;AAAA,EAEA,MAAM,aAAa,WAAyC;AAC1D,WAAO,MAAM,KAAK,IAAI,sBAAsB,SAAS;AAAA,EACvD;AAAA,EAEA,MAAM,YAAY,UAAsC;AACtD,UAAM,YAAY,MAAM,KAAK,aAAa;AAC1C,cAAU,KAAK,QAAQ;AACvB,WAAO,MAAM,KAAK,aAAa,SAAS;AAAA,EAC1C;AAAA,EAEA,MAAM,eAAe,IAAY,SAA8C;AAC7E,UAAM,YAAY,MAAM,KAAK,aAAa;AAC1C,UAAM,QAAQ,UAAU,UAAU,OAAK,EAAE,OAAO,EAAE;AAElD,QAAI,UAAU;AAAI,aAAO;AAEzB,cAAU,KAAK,IAAI,EAAE,GAAG,UAAU,KAAK,GAAG,GAAG,SAAS,WAAW,KAAK,IAAI,EAAE;AAC5E,WAAO,MAAM,KAAK,aAAa,SAAS;AAAA,EAC1C;AAAA,EAEA,MAAM,eAAe,IAA8B;AACjD,UAAM,YAAY,MAAM,KAAK,aAAa;AAC1C,UAAM,WAAW,UAAU,OAAO,OAAK,EAAE,OAAO,EAAE;AAElD,QAAI,SAAS,WAAW,UAAU;AAAQ,aAAO;AAEjD,WAAO,MAAM,KAAK,aAAa,QAAQ;AAAA,EACzC;AAAA;AAAA,EAGA,MAAM,gBAAqC;AACzC,UAAM,SAAS,MAAM,KAAK,gBAA4B,qBAAqB;AAC3E,WAAO,QAAQ,QAAQ,CAAC;AAAA,EAC1B;AAAA,EAEA,MAAM,cAAc,YAA0C;AAC5D,WAAO,MAAM,KAAK,IAAI,uBAAuB,UAAU;AAAA,EACzD;AAAA,EAEA,MAAM,YAAY,UAAsC;AACtD,UAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,eAAW,KAAK,QAAQ;AACxB,WAAO,MAAM,KAAK,cAAc,UAAU;AAAA,EAC5C;AAAA,EAEA,MAAM,eAAe,IAAY,SAA8C;AAC7E,UAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,UAAM,QAAQ,WAAW,UAAU,OAAK,EAAE,OAAO,EAAE;AAEnD,QAAI,UAAU;AAAI,aAAO;AAEzB,eAAW,KAAK,IAAI,EAAE,GAAG,WAAW,KAAK,GAAG,GAAG,QAAQ;AACvD,WAAO,MAAM,KAAK,cAAc,UAAU;AAAA,EAC5C;AAAA,EAEA,MAAM,eAAe,IAA8B;AACjD,UAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,UAAM,WAAW,WAAW,OAAO,OAAK,EAAE,OAAO,EAAE;AAEnD,QAAI,SAAS,WAAW,WAAW;AAAQ,aAAO;AAElD,WAAO,MAAM,KAAK,cAAc,QAAQ;AAAA,EAC1C;AAAA;AAAA,EAGA,MAAM,WAA2B;AAC/B,UAAM,SAAS,MAAM,KAAK,gBAAuB,gBAAgB;AACjE,WAAO,QAAQ,QAAQ;AAAA,MACrB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe,CAAC;AAAA,MAChB,eAAe,CAAC;AAAA,MAChB,aAAa,CAAC;AAAA,MACd,YAAY,CAAC;AAAA,MACb,aAAa,EAAE,QAAQ,GAAG,SAAS,GAAG,QAAQ,EAAE;AAAA,MAChD,aAAa,KAAK,IAAI;AAAA,IACxB;AAAA,EACF;AAAA,EAEA,MAAM,YAAY,SAA2C;AAC3D,UAAM,QAAQ,MAAM,KAAK,SAAS;AAClC,UAAM,eAAe,EAAE,GAAG,OAAO,GAAG,SAAS,aAAa,KAAK,IAAI,EAAE;AACrE,WAAO,MAAM,KAAK,IAAI,kBAAkB,YAAY;AAAA,EACtD;AAAA,EAEA,MAAM,uBAAuB,YAAsC;AACjE,UAAM,QAAQ,MAAM,KAAK,SAAS;AAClC,UAAM;AACN,UAAM,cAAc,UAAU,KAAK,MAAM,cAAc,UAAU,KAAK,KAAK;AAG3E,UAAM,SAAQ,oBAAI,KAAK,GAAE,YAAY,EAAE,MAAM,GAAG,EAAE,CAAC;AACnD,UAAM,WAAW,KAAK,KAAK,MAAM,WAAW,KAAK,KAAK,KAAK;AAE3D,WAAO,MAAM,KAAK,YAAY,KAAK;AAAA,EACrC;AAAA;AAAA,EAGA,MAAM,YAA6B;AACjC,UAAM,SAAS,MAAM,KAAK,gBAAwB,iBAAiB;AACnE,WAAO,QAAQ,QAAQ;AAAA,MACrB,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,UAAU,EAAE,SAAS,MAAM;AAAA,MAC3B,UAAU,EAAE,OAAO,MAAM,IAAI,OAAO,QAAQ,MAAM,QAAQ,KAAK;AAAA,MAC/D,OAAO,EAAE,cAAc,WAAW,UAAU,MAAM;AAAA,IACpD;AAAA,EACF;AAAA,EAEA,MAAM,aAAa,SAA4C;AAC7D,UAAM,SAAS,MAAM,KAAK,UAAU;AACpC,UAAM,gBAAgB,EAAE,GAAG,QAAQ,GAAG,QAAQ;AAC9C,WAAO,MAAM,KAAK,IAAI,mBAAmB,aAAa;AAAA,EACxD;AAAA;AAAA,EAGA,MAAM,SAAmG;AACvG,UAAM,CAAC,WAAW,YAAY,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MAC/D,KAAK,aAAa;AAAA,MAClB,KAAK,cAAc;AAAA,MACnB,KAAK,SAAS;AAAA,MACd,KAAK,UAAU;AAAA,IACjB,CAAC;AAED,WAAO,EAAE,WAAW,YAAY,OAAO,OAAO;AAAA,EAChD;AAAA,EAEA,MAAM,QAAQ,MAA6G;AACzH,QAAI;AACF,YAAM,WAAW,CAAC;AAElB,UAAI,KAAK;AAAW,iBAAS,KAAK,KAAK,aAAa,KAAK,SAAS,CAAC;AACnE,UAAI,KAAK;AAAY,iBAAS,KAAK,KAAK,cAAc,KAAK,UAAU,CAAC;AACtE,UAAI,KAAK;AAAO,iBAAS,KAAK,KAAK,YAAY,KAAK,KAAK,CAAC;AAC1D,UAAI,KAAK;AAAQ,iBAAS,KAAK,KAAK,aAAa,KAAK,MAAM,CAAC;AAE7D,YAAM,QAAQ,IAAI,QAAQ;AAC1B,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,kBAAkB,KAAK;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,UAA4B;AAChC,QAAI;AAEF,YAAM,QAAQ,MAAM,KAAK,SAAS;AAClC,YAAM,gBAAgB,oBAAI,KAAK;AAC/B,oBAAc,QAAQ,cAAc,QAAQ,IAAI,EAAE;AAElD,aAAO,KAAK,MAAM,UAAU,EAAE,QAAQ,UAAQ;AAC5C,YAAI,IAAI,KAAK,IAAI,IAAI,eAAe;AAClC,iBAAO,MAAM,WAAW,IAAI;AAAA,QAC9B;AAAA,MACF,CAAC;AAED,YAAM,KAAK,YAAY,KAAK;AAC5B,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,kBAAkB,KAAK;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AA1Na;;;ACHN,IAAM,cACX;;;ACoBK,IAAI,SAAS,wBAAC,OAAO,OAAO;AACjC,MAAI,KAAK;AACT,MAAI,QAAQ,OAAO,gBAAgB,IAAI,WAAY,QAAQ,CAAE,CAAC;AAC9D,SAAO,QAAQ;AACb,UAAM,YAAkB,MAAM,IAAI,IAAI,EAAE;AAAA,EAC1C;AACA,SAAO;AACT,GAPoB;;;AChBb,IAAM,kBAAN,MAAsB;AAAA,EACnB;AAAA,EAER,YAAY,WAAsB;AAChC,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAGA,MAAM,kBAAuC;AAC3C,WAAO,MAAM,KAAK,UAAU,aAAa;AAAA,EAC3C;AAAA;AAAA,EAGA,MAAM,gBAAgB,IAAsC;AAC1D,UAAM,YAAY,MAAM,KAAK,gBAAgB;AAC7C,WAAO,UAAU,KAAK,cAAY,SAAS,OAAO,EAAE,KAAK;AAAA,EAC3D;AAAA;AAAA,EAGA,MAAM,uBAAuB,YAAyC;AACpE,UAAM,YAAY,MAAM,KAAK,gBAAgB;AAC7C,WAAO,UAAU,OAAO,cAAY,SAAS,aAAa,UAAU;AAAA,EACtE;AAAA;AAAA,EAGA,MAAM,gBAAgB,OAAoC;AACxD,UAAM,YAAY,MAAM,KAAK,gBAAgB;AAC7C,UAAM,aAAa,MAAM,YAAY,EAAE,KAAK;AAE5C,QAAI,CAAC;AAAY,aAAO;AAExB,WAAO,UAAU;AAAA,MAAO,cACtB,SAAS,MAAM,YAAY,EAAE,SAAS,UAAU,KAChD,SAAS,aAAa,YAAY,EAAE,SAAS,UAAU,KACvD,SAAS,WAAW,YAAY,EAAE,SAAS,UAAU,KACrD,SAAS,IAAI,YAAY,EAAE,SAAS,UAAU,KAC9C,SAAS,MAAM,KAAK,SAAO,IAAI,YAAY,EAAE,SAAS,UAAU,CAAC;AAAA,IACnE;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,eAAe,cAAkG;AACrH,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,cAAwB;AAAA,MAC5B,IAAI,OAAO;AAAA,MACX,GAAG;AAAA,MACH,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,IACd;AAEA,UAAM,UAAU,MAAM,KAAK,UAAU,YAAY,WAAW;AAC5D,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,eAAe,IAAY,SAAgF;AAC/G,UAAM,WAAW,MAAM,KAAK,gBAAgB,EAAE;AAC9C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,UAAM,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,WAAW,KAAK,IAAI;AAAA,IACtB;AAEA,UAAM,UAAU,MAAM,KAAK,UAAU,eAAe,IAAI,WAAW;AACnE,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAEA,WAAO,EAAE,GAAG,UAAU,GAAG,YAAY;AAAA,EACvC;AAAA;AAAA,EAGA,MAAM,eAAe,IAA8B;AACjD,UAAM,WAAW,MAAM,KAAK,gBAAgB,EAAE;AAC9C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,KAAK,UAAU,eAAe,EAAE;AAAA,EAC/C;AAAA;AAAA,EAGA,MAAM,gBAAgB,KAAiE;AACrF,UAAM,UAAmD,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE;AAEnF,eAAW,MAAM,KAAK;AACpB,YAAM,UAAU,MAAM,KAAK,eAAe,EAAE;AAC5C,UAAI,SAAS;AACX,gBAAQ,QAAQ,KAAK,EAAE;AAAA,MACzB,OAAO;AACL,gBAAQ,OAAO,KAAK,EAAE;AAAA,MACxB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,uBAAuB,YAAoB,eAAyC;AACxF,WAAO,MAAM,KAAK,eAAe,YAAY,EAAE,UAAU,cAAc,CAAC,MAAM;AAAA,EAChF;AAAA;AAAA,EAGA,MAAM,wBAAwB,aAAuB,eAAyE;AAC5H,UAAM,UAAmD,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE;AAEnF,eAAW,MAAM,aAAa;AAC5B,YAAM,QAAQ,MAAM,KAAK,uBAAuB,IAAI,aAAa;AACjE,UAAI,OAAO;AACT,gBAAQ,QAAQ,KAAK,EAAE;AAAA,MACzB,OAAO;AACL,gBAAQ,OAAO,KAAK,EAAE;AAAA,MACxB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,oBAAoB,IAA8B;AACtD,UAAM,WAAW,MAAM,KAAK,gBAAgB,EAAE;AAC9C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,MAAM,KAAK,UAAU,eAAe,IAAI;AAAA,MACtD,YAAY,SAAS,aAAa;AAAA,MAClC,WAAW,KAAK,IAAI;AAAA,IACtB,CAAC;AAGD,QAAI,SAAS;AACX,YAAM,KAAK,UAAU,uBAAuB,EAAE;AAAA,IAChD;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,oBAAoB,QAAgB,IAAyB;AACjE,UAAM,YAAY,MAAM,KAAK,gBAAgB;AAC7C,WAAO,UACJ,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU,EAC1C,MAAM,GAAG,KAAK;AAAA,EACnB;AAAA;AAAA,EAGA,MAAM,mBAAmB,QAAgB,IAAyB;AAChE,UAAM,YAAY,MAAM,KAAK,gBAAgB;AAC7C,WAAO,UACJ,KAAK,CAAC,GAAG,MAAM,EAAE,YAAY,EAAE,SAAS,EACxC,MAAM,GAAG,KAAK;AAAA,EACnB;AAAA;AAAA,EAGA,MAAM,4BAA4B,QAAgB,IAAyB;AACzE,UAAM,YAAY,MAAM,KAAK,gBAAgB;AAC7C,WAAO,UACJ,KAAK,CAAC,GAAG,MAAM,EAAE,YAAY,EAAE,SAAS,EACxC,MAAM,GAAG,KAAK;AAAA,EACnB;AAAA;AAAA,EAGA,qBAAqB,MAAiD;AACpE,UAAM,SAAmB,CAAC;AAE1B,QAAI,CAAC,KAAK,SAAS,OAAO,KAAK,UAAU,YAAY,KAAK,MAAM,KAAK,EAAE,WAAW,GAAG;AACnF,aAAO,KAAK,sCAAQ;AAAA,IACtB;AAEA,QAAI,CAAC,KAAK,OAAO,OAAO,KAAK,QAAQ,UAAU;AAC7C,aAAO,KAAK,6BAAS;AAAA,IACvB,OAAO;AACL,UAAI;AACF,YAAI,IAAI,KAAK,GAAG;AAAA,MAClB,QAAE;AACA,eAAO,KAAK,6BAAS;AAAA,MACvB;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,YAAY,OAAO,KAAK,aAAa,UAAU;AACvD,aAAO,KAAK,sCAAQ;AAAA,IACtB;AAEA,QAAI,KAAK,QAAQ,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC1C,aAAO,KAAK,4CAAS;AAAA,IACvB;AAEA,WAAO;AAAA,MACL,OAAO,OAAO,WAAW;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,uBAAuB,KAAa,WAA8C;AACtF,UAAM,YAAY,MAAM,KAAK,gBAAgB;AAC7C,WAAO,UAAU;AAAA,MAAK,cACpB,SAAS,QAAQ,OAAO,SAAS,OAAO;AAAA,IAC1C,KAAK;AAAA,EACP;AAAA;AAAA,EAGA,MAAM,mBAKH;AACD,UAAM,YAAY,MAAM,KAAK,gBAAgB;AAC7C,UAAM,QAAQ,UAAU;AACxB,UAAM,cAAc,UAAU,OAAO,CAAC,KAAK,aAAa,MAAM,SAAS,YAAY,CAAC;AAEpF,UAAM,aAAqC,CAAC;AAC5C,cAAU,QAAQ,cAAY;AAC5B,iBAAW,SAAS,QAAQ,KAAK,WAAW,SAAS,QAAQ,KAAK,KAAK;AAAA,IACzE,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,0BAA0B,QAAQ,IAAI,cAAc,QAAQ;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,kBAAuC;AAC3C,WAAO,MAAM,KAAK,gBAAgB;AAAA,EACpC;AAAA;AAAA,EAGA,MAAM,gBAAgB,WAAuB,UAGzC,CAAC,GAIF;AACD,UAAM,SAAkE,EAAE,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE;AAE9G,eAAW,YAAY,WAAW;AAChC,UAAI;AAEF,cAAM,aAAa,KAAK,qBAAqB,QAAQ;AACrD,YAAI,CAAC,WAAW,OAAO;AACrB,iBAAO,OAAO,KAAK,iBAAO,SAAS,oCAAgB,WAAW,OAAO,KAAK,IAAI,GAAG;AACjF;AAAA,QACF;AAGA,YAAI,QAAQ,gBAAgB;AAC1B,gBAAM,YAAY,MAAM,KAAK,uBAAuB,SAAS,GAAG;AAChE,cAAI,WAAW;AACb,mBAAO;AACP;AAAA,UACF;AAAA,QACF;AAGA,cAAM,KAAK,eAAe;AAAA,UACxB,OAAO,SAAS;AAAA,UAChB,KAAK,SAAS;AAAA,UACd,aAAa,SAAS;AAAA,UACtB,WAAW,SAAS;AAAA,UACpB,UAAU,SAAS;AAAA,UACnB,MAAM,SAAS;AAAA,UACf,MAAM,SAAS;AAAA,QACjB,CAAC;AAED,eAAO;AAAA,MACT,SAAS,OAAP;AACA,eAAO,OAAO,KAAK,6BAAS,SAAS,wBAAc,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,MAC/G;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AA/Ra;;;ACAN,IAAM,kBAAN,MAAsB;AAAA,EACnB;AAAA,EAER,YAAY,WAAsB;AAChC,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAGA,MAAM,mBAAwC;AAC5C,UAAM,aAAa,MAAM,KAAK,UAAU,cAAc;AACtD,WAAO,WAAW,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAAA,EACpD;AAAA;AAAA,EAGA,MAAM,gBAAgB,IAAsC;AAC1D,UAAM,aAAa,MAAM,KAAK,iBAAiB;AAC/C,WAAO,WAAW,KAAK,cAAY,SAAS,OAAO,EAAE,KAAK;AAAA,EAC5D;AAAA;AAAA,EAGA,MAAM,eAAe,cAAqE;AACxF,UAAM,aAAa,MAAM,KAAK,iBAAiB;AAC/C,UAAM,WAAW,WAAW,SAAS,IAAI,KAAK,IAAI,GAAG,WAAW,IAAI,OAAK,EAAE,KAAK,CAAC,IAAI;AAErF,UAAM,cAAwB;AAAA,MAC5B,IAAI,OAAO;AAAA,MACX,GAAG;AAAA,MACH,OAAO,aAAa,SAAS,WAAW;AAAA,MACxC,WAAW,KAAK,IAAI;AAAA,IACtB;AAEA,UAAM,UAAU,MAAM,KAAK,UAAU,YAAY,WAAW;AAC5D,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,eAAe,IAAY,SAAgF;AAC/G,UAAM,WAAW,MAAM,KAAK,gBAAgB,EAAE;AAC9C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,MAAM,KAAK,UAAU,eAAe,IAAI,OAAO;AAC/D,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAEA,WAAO,EAAE,GAAG,UAAU,GAAG,QAAQ;AAAA,EACnC;AAAA;AAAA,EAGA,MAAM,eAAe,IAAY,UAG7B,CAAC,GAAsF;AACzF,UAAM,WAAW,MAAM,KAAK,gBAAgB,EAAE;AAC9C,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,SAAS,MAAM;AAAA,IAC1B;AAGA,UAAM,YAAY,MAAM,KAAK,UAAU,aAAa;AACpD,UAAM,oBAAoB,UAAU,OAAO,cAAY,SAAS,aAAa,EAAE;AAE/E,QAAI,iBAAiB;AACrB,QAAI,mBAAmB;AAGvB,QAAI,kBAAkB,SAAS,GAAG;AAChC,UAAI,QAAQ,iBAAiB;AAE3B,mBAAW,YAAY,mBAAmB;AACxC,gBAAMC,WAAU,MAAM,KAAK,UAAU,eAAe,SAAS,IAAI;AAAA,YAC/D,UAAU,QAAQ;AAAA,YAClB,WAAW,KAAK,IAAI;AAAA,UACtB,CAAC;AACD,cAAIA;AAAS;AAAA,QACf;AAAA,MACF,WAAW,QAAQ,iBAAiB;AAElC,mBAAW,YAAY,mBAAmB;AACxC,gBAAMA,WAAU,MAAM,KAAK,UAAU,eAAe,SAAS,EAAE;AAC/D,cAAIA;AAAS;AAAA,QACf;AAAA,MACF,OAAO;AAEL,cAAM,IAAI,MAAM,yFAAyF;AAAA,MAC3G;AAAA,IACF;AAGA,UAAM,UAAU,MAAM,KAAK,UAAU,eAAe,EAAE;AAEtD,WAAO;AAAA,MACL;AAAA,MACA,gBAAgB,iBAAiB,IAAI,iBAAiB;AAAA,MACtD,kBAAkB,mBAAmB,IAAI,mBAAmB;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,kBAAkB,gBAAmE;AACzF,QAAI;AACF,iBAAW,EAAE,IAAI,MAAM,KAAK,gBAAgB;AAC1C,cAAM,KAAK,UAAU,eAAe,IAAI,EAAE,MAAM,CAAC;AAAA,MACnD;AACA,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,iCAAiC,KAAK;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,mBAKF;AACF,UAAM,CAAC,YAAY,WAAW,KAAK,IAAI,MAAM,QAAQ,IAAI;AAAA,MACvD,KAAK,iBAAiB;AAAA,MACtB,KAAK,UAAU,aAAa;AAAA,MAC5B,KAAK,UAAU,SAAS;AAAA,IAC1B,CAAC;AAED,WAAO,WAAW,IAAI,cAAY;AAChC,YAAM,oBAAoB,UAAU,OAAO,cAAY,SAAS,aAAa,SAAS,EAAE;AACxF,YAAM,cAAc,kBAAkB,OAAO,CAAC,KAAK,aAAa,MAAM,SAAS,YAAY,CAAC;AAC5F,YAAM,cAAc,kBAAkB,SAAS,IAC3C,KAAK,IAAI,GAAG,kBAAkB,IAAI,cAAY,SAAS,SAAS,CAAC,IACjE,SAAS;AAEb,aAAO;AAAA,QACL;AAAA,QACA,eAAe,kBAAkB;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,qBAAqB,MAAiD;AACpE,UAAM,SAAmB,CAAC;AAE1B,QAAI,CAAC,KAAK,QAAQ,OAAO,KAAK,SAAS,YAAY,KAAK,KAAK,KAAK,EAAE,WAAW,GAAG;AAChF,aAAO,KAAK,kDAAU;AAAA,IACxB;AAEA,QAAI,KAAK,UAAU,WAAc,OAAO,KAAK,UAAU,YAAY,KAAK,QAAQ,IAAI;AAClF,aAAO,KAAK,kDAAU;AAAA,IACxB;AAEA,WAAO;AAAA,MACL,OAAO,OAAO,WAAW;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,2BAA2B,MAAc,WAA8C;AAC3F,UAAM,aAAa,MAAM,KAAK,iBAAiB;AAC/C,WAAO,WAAW;AAAA,MAAK,cACrB,SAAS,KAAK,YAAY,MAAM,KAAK,YAAY,KAAK,SAAS,OAAO;AAAA,IACxE,KAAK;AAAA,EACP;AAAA;AAAA,EAGA,MAAM,qBAA0C;AAC9C,UAAM,CAAC,YAAY,SAAS,IAAI,MAAM,QAAQ,IAAI;AAAA,MAChD,KAAK,iBAAiB;AAAA,MACtB,KAAK,UAAU,aAAa;AAAA,IAC9B,CAAC;AAED,WAAO,WAAW;AAAA,MAAO,cACvB,CAAC,UAAU,KAAK,cAAY,SAAS,aAAa,SAAS,EAAE;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,qBAAqB,QAAgB,IAIvC;AACF,UAAM,QAAQ,MAAM,KAAK,iBAAiB;AAC1C,WAAO,MACJ,KAAK,CAAC,GAAG,MAAM,EAAE,cAAc,EAAE,WAAW,EAC5C,MAAM,GAAG,KAAK,EACd,IAAI,CAAC,EAAE,UAAU,eAAe,YAAY,OAAO;AAAA,MAClD;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE;AAAA,EACN;AAAA;AAAA,EAGA,MAAM,0BAA+C;AACnD,UAAM,oBAAoB;AAAA,MACxB,EAAE,MAAM,4BAAQ,MAAM,mBAAO,aAAa,gEAAc,OAAO,EAAE;AAAA,MACjE,EAAE,MAAM,4BAAQ,MAAM,aAAM,aAAa,8CAAW,OAAO,EAAE;AAAA,MAC7D,EAAE,MAAM,4BAAQ,MAAM,aAAM,aAAa,0DAAa,OAAO,EAAE;AAAA,MAC/D,EAAE,MAAM,4BAAQ,MAAM,aAAM,aAAa,8CAAW,OAAO,EAAE;AAAA,MAC7D,EAAE,MAAM,4BAAQ,MAAM,aAAM,aAAa,8CAAW,OAAO,EAAE;AAAA,MAC7D,EAAE,MAAM,4BAAQ,MAAM,aAAM,aAAa,0DAAa,OAAO,EAAE;AAAA,MAC/D,EAAE,MAAM,4BAAQ,MAAM,aAAM,aAAa,8CAAW,OAAO,EAAE;AAAA,MAC7D,EAAE,MAAM,gBAAM,MAAM,aAAM,aAAa,oDAAY,OAAO,EAAE;AAAA,IAC9D;AAEA,UAAM,oBAAgC,CAAC;AAEvC,eAAW,gBAAgB,mBAAmB;AAC5C,UAAI;AAEF,cAAM,WAAW,MAAM,KAAK,2BAA2B,aAAa,IAAI;AACxE,YAAI,CAAC,UAAU;AACb,gBAAM,WAAW,MAAM,KAAK,eAAe,YAAY;AACvD,4BAAkB,KAAK,QAAQ;AAAA,QACjC;AAAA,MACF,SAAS,OAAP;AACA,gBAAQ,MAAM,qCAAqC,aAAa,SAAS,KAAK;AAAA,MAChF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,mBAAwC;AAC5C,WAAO,MAAM,KAAK,iBAAiB;AAAA,EACrC;AAAA;AAAA,EAGA,MAAM,iBAAiB,YAAwB,UAG3C,CAAC,GAIF;AACD,UAAM,SAAkE,EAAE,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE;AAE9G,eAAW,YAAY,YAAY;AACjC,UAAI;AAEF,cAAM,aAAa,KAAK,qBAAqB,QAAQ;AACrD,YAAI,CAAC,WAAW,OAAO;AACrB,iBAAO,OAAO,KAAK,iBAAO,SAAS,mCAAe,WAAW,OAAO,KAAK,IAAI,GAAG;AAChF;AAAA,QACF;AAGA,YAAI,QAAQ,gBAAgB;AAC1B,gBAAM,YAAY,MAAM,KAAK,2BAA2B,SAAS,IAAI;AACrE,cAAI,WAAW;AACb,mBAAO;AACP;AAAA,UACF;AAAA,QACF;AAGA,cAAM,KAAK,eAAe;AAAA,UACxB,MAAM,SAAS;AAAA,UACf,MAAM,SAAS;AAAA,UACf,aAAa,SAAS;AAAA,UACtB,OAAO,SAAS;AAAA,QAClB,CAAC;AAED,eAAO;AAAA,MACT,SAAS,OAAP;AACA,eAAO,OAAO,KAAK,6BAAS,SAAS,uBAAa,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,MAC9G;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,gBAAgB,UAAkB,UAAyE;AAC/G,UAAM,CAAC,gBAAgB,cAAc,IAAI,MAAM,QAAQ,IAAI;AAAA,MACzD,KAAK,gBAAgB,QAAQ;AAAA,MAC7B,KAAK,gBAAgB,QAAQ;AAAA,IAC/B,CAAC;AAED,QAAI,CAAC,kBAAkB,CAAC,gBAAgB;AACtC,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAGA,UAAM,YAAY,MAAM,KAAK,UAAU,aAAa;AACpD,UAAM,kBAAkB,UAAU,OAAO,cAAY,SAAS,aAAa,QAAQ;AAEnF,QAAI,iBAAiB;AACrB,eAAW,YAAY,iBAAiB;AACtC,YAAM,UAAU,MAAM,KAAK,UAAU,eAAe,SAAS,IAAI;AAAA,QAC/D,UAAU;AAAA,QACV,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AACD,UAAI;AAAS;AAAA,IACf;AAGA,UAAM,gBAAgB,MAAM,KAAK,UAAU,eAAe,QAAQ;AAElE,WAAO;AAAA,MACL,SAAS;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AA3Ta;;;ACuBN,IAAM,mBAAN,MAAuB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EAER,YAAY,WAAsB;AAChC,SAAK,YAAY;AACjB,SAAK,kBAAkB,IAAI,gBAAgB,SAAS;AACpD,SAAK,kBAAkB,IAAI,gBAAgB,SAAS;AAAA,EACtD;AAAA;AAAA,EAGA,MAAM,oBAAoB,YAKvB;AACD,UAAM,SAKF;AAAA,MACF,SAAS;AAAA,MACT,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,QAAQ,CAAC;AAAA,IACX;AAEA,QAAI;AACF,cAAQ,IAAI,yCAAW;AAGvB,cAAQ,IAAI,yCAAW;AACvB,eAAS,IAAI,GAAG,IAAI,WAAW,WAAW,QAAQ,KAAK;AACrD,cAAM,iBAAiB,WAAW,WAAW,CAAC;AAC9C,YAAI;AACF,gBAAM,WAA+C;AAAA,YACnD,MAAM,eAAe;AAAA,YACrB,MAAM,eAAe;AAAA,YACrB,aAAa,2DAAc,eAAe;AAAA,YAC1C,OAAO,IAAI;AAAA,UACb;AAGA,gBAAM,WAAW,MAAM,KAAK,gBAAgB,2BAA2B,SAAS,IAAI;AACpF,cAAI,CAAC,UAAU;AACb,kBAAM,KAAK,gBAAgB,eAAe,QAAQ;AAClD,mBAAO;AACP,oBAAQ,IAAI,gDAAa,SAAS,MAAM;AAAA,UAC1C,OAAO;AACL,oBAAQ,IAAI,uDAAe,SAAS,MAAM;AAAA,UAC5C;AAAA,QACF,SAAS,OAAP;AACA,gBAAM,WAAW,wCAAU,eAAe,SAAS,iBAAiB,QAAQ,MAAM,UAAU;AAC5F,iBAAO,OAAO,KAAK,QAAQ;AAC3B,kBAAQ,MAAM,QAAQ;AAAA,QACxB;AAAA,MACF;AAGA,cAAQ,IAAI,yCAAW;AACvB,iBAAW,cAAc,WAAW,OAAO;AACzC,YAAI;AACF,gBAAM,WAA4E;AAAA,YAChF,OAAO,WAAW;AAAA,YAClB,KAAK,WAAW;AAAA,YAChB,aAAa,WAAW;AAAA,YACxB,WAAW,WAAW;AAAA,YACtB,UAAU,WAAW;AAAA,YACrB,MAAM,WAAW;AAAA,YACjB,MAAM,CAAC;AAAA,UACT;AAGA,gBAAM,aAAa,KAAK,gBAAgB,qBAAqB,QAAQ;AACrE,cAAI,CAAC,WAAW,OAAO;AACrB,mBAAO,OAAO,KAAK,wCAAU,SAAS,UAAU,WAAW,OAAO,KAAK,IAAI,GAAG;AAC9E;AAAA,UACF;AAGA,gBAAM,YAAY,MAAM,KAAK,gBAAgB,uBAAuB,SAAS,GAAG;AAChF,cAAI,CAAC,WAAW;AACd,kBAAM,KAAK,gBAAgB,eAAe,QAAQ;AAClD,mBAAO;AACP,oBAAQ,IAAI,gDAAa,SAAS,OAAO;AAAA,UAC3C,OAAO;AACL,oBAAQ,IAAI,uDAAe,SAAS,OAAO;AAAA,UAC7C;AAAA,QACF,SAAS,OAAP;AACA,gBAAM,WAAW,wCAAU,WAAW,UAAU,iBAAiB,QAAQ,MAAM,UAAU;AACzF,iBAAO,OAAO,KAAK,QAAQ;AAC3B,kBAAQ,MAAM,QAAQ;AAAA,QACxB;AAAA,MACF;AAEA,aAAO,UAAU;AACjB,cAAQ,IAAI,6BAAS,OAAO,0CAA2B,OAAO,sCAAuB;AAAA,IAEvF,SAAS,OAAP;AACA,aAAO,OAAO,KAAK,yCAAW,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AACxF,cAAQ,MAAM,6BAAS,KAAK;AAAA,IAC9B;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,mBAA+C;AACnD,QAAI;AAKF,YAAM,aAAyB;AAAA,QAC7B,YAAY;AAAA,UACV,EAAE,IAAI,cAAc,MAAM,4BAAQ,MAAM,YAAK;AAAA,UAC7C,EAAE,IAAI,MAAM,MAAM,kBAAQ,MAAM,YAAK;AAAA,UACrC,EAAE,IAAI,UAAU,MAAM,4BAAQ,MAAM,YAAK;AAAA,UACzC,EAAE,IAAI,OAAO,MAAM,4BAAQ,MAAM,SAAI;AAAA,UACrC,EAAE,IAAI,SAAS,MAAM,4BAAQ,MAAM,YAAK;AAAA,UACxC,EAAE,IAAI,QAAQ,MAAM,4BAAQ,MAAM,YAAK;AAAA,QACzC;AAAA,QACA,OAAO;AAAA,UACL;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,aAAa;AAAA,YACb,WAAW;AAAA,YACX,KAAK;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,aAAa;AAAA,YACb,WAAW;AAAA,YACX,KAAK;AAAA,YACL,UAAU;AAAA,YACV,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,mDAAqB,KAAK;AACxC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,mBAQH;AACD,QAAI;AAEF,YAAM,CAAC,mBAAmB,kBAAkB,IAAI,MAAM,QAAQ,IAAI;AAAA,QAChE,KAAK,gBAAgB,gBAAgB;AAAA,QACrC,KAAK,gBAAgB,iBAAiB;AAAA,MACxC,CAAC;AAED,UAAI,kBAAkB,SAAS,KAAK,mBAAmB,SAAS,GAAG;AACjE,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS,EAAE,oBAAoB,GAAG,mBAAmB,GAAG,QAAQ,CAAC,EAAE;AAAA,QACrE;AAAA,MACF;AAGA,YAAM,aAAa,MAAM,KAAK,iBAAiB;AAC/C,UAAI,CAAC,YAAY;AACf,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS,EAAE,oBAAoB,GAAG,mBAAmB,GAAG,QAAQ,CAAC,kDAAU,EAAE;AAAA,QAC/E;AAAA,MACF;AAGA,YAAM,kBAAkB,MAAM,KAAK,oBAAoB,UAAU;AAEjE,aAAO;AAAA,QACL,SAAS,gBAAgB;AAAA,QACzB,SAAS,gBAAgB,UACrB,gEAAc,gBAAgB,+CAA2B,gBAAgB,yCACzE;AAAA,QACJ,SAAS;AAAA,UACP,oBAAoB,gBAAgB;AAAA,UACpC,mBAAmB,gBAAgB;AAAA,UACnC,QAAQ,gBAAgB;AAAA,QAC1B;AAAA,MACF;AAAA,IAEF,SAAS,OAAP;AACA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,UACP,oBAAoB;AAAA,UACpB,mBAAmB;AAAA,UACnB,QAAQ,CAAC,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACnE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,eAAiC;AACrC,QAAI;AACF,YAAM,CAAC,WAAW,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,QAChD,KAAK,gBAAgB,gBAAgB;AAAA,QACrC,KAAK,gBAAgB,iBAAiB;AAAA,MACxC,CAAC;AAGD,iBAAW,YAAY,WAAW;AAChC,cAAM,KAAK,gBAAgB,eAAe,SAAS,EAAE;AAAA,MACvD;AAGA,iBAAW,YAAY,YAAY;AACjC,cAAM,KAAK,gBAAgB,eAAe,SAAS,IAAI,EAAE,iBAAiB,KAAK,CAAC;AAAA,MAClF;AAGA,YAAM,KAAK,UAAU,YAAY;AAAA,QAC/B,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,eAAe,CAAC;AAAA,QAChB,eAAe,CAAC;AAAA,QAChB,aAAa,CAAC;AAAA,QACd,YAAY,CAAC;AAAA,QACb,aAAa,EAAE,QAAQ,GAAG,SAAS,GAAG,QAAQ,EAAE;AAAA,QAChD,aAAa,KAAK,IAAI;AAAA,MACxB,CAAC;AAED,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,yCAAW,KAAK;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,mBAKH;AACD,UAAM,SAKF;AAAA,MACF,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,QAAQ,CAAC;AAAA,IACX;AAEA,QAAI;AAEF,YAAM,oBAAoB,MAAM,KAAK,gBAAgB,wBAAwB;AAC7E,aAAO,oBAAoB,kBAAkB;AAG7C,YAAM,kBAAkB;AAAA,QACtB;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,aAAa;AAAA,UACb,WAAW;AAAA,UACX,UAAU,kBAAkB,KAAK,OAAK,EAAE,SAAS,0BAAM,GAAG,MAAM;AAAA,UAChE,MAAM;AAAA,UACN,MAAM,CAAC,gBAAM,gBAAM,KAAK;AAAA,QAC1B;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,aAAa;AAAA,UACb,WAAW;AAAA,UACX,UAAU,kBAAkB,KAAK,OAAK,EAAE,SAAS,0BAAM,GAAG,MAAM;AAAA,UAChE,MAAM;AAAA,UACN,MAAM,CAAC,gBAAM,MAAM,cAAI;AAAA,QACzB;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,aAAa;AAAA,UACb,WAAW;AAAA,UACX,UAAU,kBAAkB,KAAK,OAAK,EAAE,SAAS,0BAAM,GAAG,MAAM;AAAA,UAChE,MAAM;AAAA,UACN,MAAM,CAAC,gBAAM,OAAO,cAAI;AAAA,QAC1B;AAAA,MACF;AAEA,iBAAW,gBAAgB,iBAAiB;AAC1C,YAAI;AACF,gBAAM,KAAK,gBAAgB,eAAe,YAAY;AACtD,iBAAO;AAAA,QACT,SAAS,OAAP;AACA,iBAAO,OAAO,KAAK,qDAAa,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,QAC5F;AAAA,MACF;AAEA,aAAO,UAAU;AAAA,IACnB,SAAS,OAAP;AACA,aAAO,OAAO,KAAK,qDAAa,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,IAC5F;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,qBAKH;AACD,UAAM,CAAC,WAAW,YAAY,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MACxD,KAAK,gBAAgB,gBAAgB;AAAA,MACrC,KAAK,gBAAgB,iBAAiB;AAAA,MACtC,KAAK,UAAU,UAAU;AAAA,IAC3B,CAAC;AAED,WAAO;AAAA,MACL,SAAS,UAAU,SAAS,KAAK,WAAW,SAAS;AAAA,MACrD,eAAe,UAAU;AAAA,MACzB,eAAe,WAAW;AAAA,MAC1B,eAAe;AAAA;AAAA,IACjB;AAAA,EACF;AACF;AA1Va;;;ACvBN,IAAM,wBAAN,MAA4B;AAAA,EACzB;AAAA,EACA;AAAA,EAER,YAAY,iBAAkC,iBAAkC;AAC9E,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA,EAGA,oBAAoB,aAAgD;AAClE,QAAI;AACF,YAAM,OAAO,KAAK,MAAM,WAAW;AAGnC,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM,gBAAgB,CAAC,KAAK,MAAM,OAAO;AAChE,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AAEA,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,qCAAqC,KAAK;AACxD,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,mBAAmB,aAAqE;AACtF,QAAI;AACF,YAAM,YAAmB,CAAC;AAC1B,YAAM,UAAoB,CAAC;AAG3B,YAAM,QAAQ,YAAY,MAAM,IAAI;AACpC,UAAI,gBAAgB;AACpB,UAAI,cAAwB,CAAC;AAE7B,iBAAW,QAAQ,OAAO;AACxB,cAAM,cAAc,KAAK,KAAK;AAG9B,YAAI,YAAY,SAAS,SAAS,GAAG;AACnC,gBAAM,cAAc,YAAY,MAAM,WAAW;AACjD,cAAI,aAAa;AACf,4BAAgB,YAAY,CAAC;AAC7B,wBAAY,KAAK,aAAa;AAC9B,oBAAQ,KAAK,aAAa;AAAA,UAC5B;AAAA,QACF,WAGS,YAAY,SAAS,OAAO,GAAG;AACtC,sBAAY,IAAI;AAChB,0BAAgB,YAAY,YAAY,SAAS,CAAC,KAAK;AAAA,QACzD,WAGS,YAAY,SAAS,cAAc,GAAG;AAC7C,gBAAM,WAAW,YAAY,MAAM,gBAAgB;AACnD,gBAAM,aAAa,YAAY,MAAM,WAAW;AAChD,gBAAM,eAAe,YAAY,MAAM,oBAAoB;AAC3D,gBAAM,YAAY,YAAY,MAAM,gBAAgB;AAEpD,cAAI,YAAY,YAAY;AAC1B,kBAAM,MAAM,SAAS,CAAC;AACtB,kBAAM,QAAQ,WAAW,CAAC;AAC1B,kBAAM,UAAU,eAAe,SAAS,aAAa,CAAC,CAAC,IAAI,MAAO,KAAK,IAAI;AAC3E,kBAAM,OAAO,YAAY,UAAU,CAAC,IAAI;AAExC,sBAAU,KAAK;AAAA,cACb;AAAA,cACA;AAAA,cACA,aAAa,yCAAgB;AAAA,cAC7B,WAAW;AAAA,cACX,UAAU,iBAAiB;AAAA,cAC3B,MAAM,gBAAgB,CAAC,aAAa,IAAI,CAAC,UAAU;AAAA,cACnD;AAAA,cACA,WAAW;AAAA,cACX,cAAc;AAAA,YAChB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAEA,aAAO,EAAE,WAAW,QAAQ;AAAA,IAC9B,SAAS,OAAP;AACA,cAAQ,MAAM,mCAAmC,KAAK;AACtD,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGQ,yBACN,MACA,aAAqB,IACoB;AACzC,UAAM,YAAmB,CAAC;AAC1B,UAAM,UAAoB,CAAC;AAE3B,QAAI,KAAK,SAAS,SAAS,KAAK,KAAK;AAEnC,gBAAU,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,KAAK,KAAK;AAAA,QACV,aAAa;AAAA,QACb,WAAW,KAAK;AAAA,QAChB,UAAU,cAAc;AAAA,QACxB,MAAM,aAAa,CAAC,UAAU,IAAI,CAAC,UAAU;AAAA,QAC7C,WAAW,KAAK,aAAa,SAAS,KAAK,UAAU,IAAI,KAAK,IAAI;AAAA,QAClE,cAAc,KAAK,gBAAgB,SAAS,KAAK,aAAa,IAAI,KAAK,IAAI;AAAA,MAC7E,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,YAAY,KAAK,UAAU;AAElD,YAAM,aAAa,KAAK;AACxB,YAAM,aAAa,aAAa,GAAG,cAAc,eAAe;AAEhE,cAAQ,KAAK,UAAU;AAGvB,iBAAW,SAAS,KAAK,UAAU;AACjC,cAAM,cAAc,KAAK,yBAAyB,OAAO,UAAU;AACnE,kBAAU,KAAK,GAAG,YAAY,SAAS;AACvC,gBAAQ,KAAK,GAAG,YAAY,OAAO;AAAA,MACrC;AAAA,IACF;AAEA,WAAO,EAAE,WAAW,QAAQ;AAAA,EAC9B;AAAA;AAAA,EAGA,MAAM,gBACJ,SACA,UAKI,CAAC,GACkB;AACvB,UAAM,SAAS,QAAQ,UAAU,KAAK,iBAAiB,OAAO;AAE9D,QAAI,WAAW,QAAQ;AACrB,aAAO,KAAK,sBAAsB,SAAS,OAAO;AAAA,IACpD,WAAW,WAAW,QAAQ;AAC5B,aAAO,KAAK,oBAAoB,SAAS,OAAO;AAAA,IAClD,OAAO;AACL,aAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ,CAAC,uIAAwC;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGQ,iBAAiB,SAA8C;AACrE,UAAM,iBAAiB,QAAQ,KAAK;AAGpC,QAAI,eAAe,WAAW,GAAG,KAAK,eAAe,SAAS,SAAS,GAAG;AACxE,aAAO;AAAA,IACT;AAGA,QAAI,eAAe,SAAS,qCAAqC,KAC7D,eAAe,SAAS,cAAc,KACtC,eAAe,SAAS,MAAM,GAAG;AACnC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,sBACJ,aACA,UAII,CAAC,GACkB;AACvB,UAAM,SAAuB;AAAA,MAC3B,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,IACX;AAEA,QAAI;AAEF,YAAM,aAAa,KAAK,oBAAoB,WAAW;AACvD,UAAI,CAAC,YAAY;AACf,eAAO,OAAO,KAAK,0DAAkB;AACrC,eAAO;AAAA,MACT;AAGA,YAAM,eAAsB,CAAC;AAC7B,YAAM,aAAuB,CAAC;AAG9B,UAAI,WAAW,MAAM,cAAc;AACjC,cAAM,YAAY,KAAK,yBAAyB,WAAW,MAAM,cAAc,oBAAK;AACpF,qBAAa,KAAK,GAAG,UAAU,SAAS;AACxC,mBAAW,KAAK,GAAG,UAAU,OAAO;AAAA,MACtC;AAGA,UAAI,WAAW,MAAM,OAAO;AAC1B,cAAM,cAAc,KAAK,yBAAyB,WAAW,MAAM,OAAO,0BAAM;AAChF,qBAAa,KAAK,GAAG,YAAY,SAAS;AAC1C,mBAAW,KAAK,GAAG,YAAY,OAAO;AAAA,MACxC;AAGA,UAAI,WAAW,MAAM,QAAQ;AAC3B,cAAM,eAAe,KAAK,yBAAyB,WAAW,MAAM,QAAQ,0BAAM;AAClF,qBAAa,KAAK,GAAG,aAAa,SAAS;AAC3C,mBAAW,KAAK,GAAG,aAAa,OAAO;AAAA,MACzC;AAGA,YAAM,cAAsC,CAAC;AAC7C,UAAI,QAAQ,kBAAkB;AAC5B,cAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC;AAE7C,mBAAW,cAAc,eAAe;AACtC,cAAI;AAEF,kBAAM,mBAAmB,MAAM,KAAK,gBAAgB,2BAA2B,UAAU;AAEzF,gBAAI,CAAC,kBAAkB;AACrB,oBAAM,WAAW,MAAM,KAAK,gBAAgB,eAAe;AAAA,gBACzD,MAAM;AAAA,gBACN,aAAa,iDAAmB;AAAA,gBAChC,OAAO;AAAA;AAAA,cACT,CAAC;AACD,0BAAY,UAAU,IAAI,SAAS;AAAA,YACrC,OAAO;AACL,0BAAY,UAAU,IAAI,iBAAiB;AAAA,YAC7C;AAAA,UACF,SAAS,OAAP;AACA,mBAAO,OAAO,KAAK,yCAAW,YAAY;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AAGA,YAAM,qBAAqB,MAAM,KAAK,gBAAgB,iBAAiB;AACvE,YAAM,oBAAoB,QAAQ,mBAChC,mBAAmB,KAAK,OAAK,EAAE,SAAS,0BAAM,GAAG,MACjD,mBAAmB,CAAC,GAAG;AAGzB,iBAAW,gBAAgB,cAAc;AACvC,YAAI;AAEF,cAAI,aAAa;AACjB,cAAI,QAAQ,oBAAoB,aAAa,UAAU;AACrD,yBAAa,YAAY,aAAa,QAAQ,KAAK;AAAA,UACrD;AAGA,gBAAM,aAAa,KAAK,gBAAgB,qBAAqB;AAAA,YAC3D,GAAG;AAAA,YACH,UAAU;AAAA,UACZ,CAAC;AAED,cAAI,CAAC,WAAW,OAAO;AACrB,mBAAO,OAAO,KAAK,yCAAW,aAAa,WAAW,WAAW,OAAO,KAAK,IAAI,GAAG;AACpF;AAAA,UACF;AAGA,cAAI,QAAQ,gBAAgB;AAC1B,kBAAM,YAAY,MAAM,KAAK,gBAAgB,uBAAuB,aAAa,GAAG;AACpF,gBAAI,WAAW;AACb,qBAAO;AACP;AAAA,YACF;AAAA,UACF;AAGA,gBAAM,KAAK,gBAAgB,eAAe;AAAA,YACxC,OAAO,aAAa;AAAA,YACpB,KAAK,aAAa;AAAA,YAClB,aAAa,aAAa;AAAA,YAC1B,WAAW,aAAa;AAAA,YACxB,UAAU;AAAA,YACV,MAAM,aAAa;AAAA,UACrB,CAAC;AAED,iBAAO;AAAA,QACT,SAAS,OAAP;AACA,iBAAO,OAAO,KAAK,yCAAW,aAAa,WAAW,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,QAClH;AAAA,MACF;AAEA,aAAO,UAAU;AACjB,cAAQ,IAAI,gDAAkB,OAAO,gCAAiB,OAAO,+BAAgB,OAAO,OAAO,2BAAY;AAAA,IAEzG,SAAS,OAAP;AACA,aAAO,OAAO,KAAK,yCAAW,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AACxF,cAAQ,MAAM,mCAAmC,KAAK;AAAA,IACxD;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,oBACJ,aACA,UAII,CAAC,GACkB;AACvB,UAAM,SAAuB;AAAA,MAC3B,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ,CAAC;AAAA,IACX;AAEA,QAAI;AAEF,YAAM,WAAW,KAAK,mBAAmB,WAAW;AACpD,UAAI,CAAC,UAAU;AACb,eAAO,OAAO,KAAK,wDAAgB;AACnC,eAAO;AAAA,MACT;AAEA,YAAM,EAAE,WAAW,cAAc,SAAS,WAAW,IAAI;AAGzD,YAAM,cAAsC,CAAC;AAC7C,UAAI,QAAQ,kBAAkB;AAC5B,cAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC;AAE7C,mBAAW,cAAc,eAAe;AACtC,cAAI;AAEF,kBAAM,mBAAmB,MAAM,KAAK,gBAAgB,2BAA2B,UAAU;AAEzF,gBAAI,CAAC,kBAAkB;AACrB,oBAAM,WAAW,MAAM,KAAK,gBAAgB,eAAe;AAAA,gBACzD,MAAM;AAAA,gBACN,aAAa,2DAAmB;AAAA,gBAChC,OAAO;AAAA;AAAA,cACT,CAAC;AACD,0BAAY,UAAU,IAAI,SAAS;AAAA,YACrC,OAAO;AACL,0BAAY,UAAU,IAAI,iBAAiB;AAAA,YAC7C;AAAA,UACF,SAAS,OAAP;AACA,mBAAO,OAAO,KAAK,yCAAW,YAAY;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AAGA,YAAM,qBAAqB,MAAM,KAAK,gBAAgB,iBAAiB;AACvE,YAAM,oBAAoB,QAAQ,mBAChC,mBAAmB,KAAK,OAAK,EAAE,SAAS,0BAAM,GAAG,MACjD,mBAAmB,CAAC,GAAG;AAGzB,iBAAW,gBAAgB,cAAc;AACvC,YAAI;AAEF,cAAI,aAAa;AACjB,cAAI,QAAQ,oBAAoB,aAAa,UAAU;AACrD,yBAAa,YAAY,aAAa,QAAQ,KAAK;AAAA,UACrD;AAGA,gBAAM,aAAa,KAAK,gBAAgB,qBAAqB;AAAA,YAC3D,GAAG;AAAA,YACH,UAAU;AAAA,UACZ,CAAC;AAED,cAAI,CAAC,WAAW,OAAO;AACrB,mBAAO,OAAO,KAAK,yCAAW,aAAa,WAAW,WAAW,OAAO,KAAK,IAAI,GAAG;AACpF;AAAA,UACF;AAGA,cAAI,QAAQ,gBAAgB;AAC1B,kBAAM,YAAY,MAAM,KAAK,gBAAgB,uBAAuB,aAAa,GAAG;AACpF,gBAAI,WAAW;AACb,qBAAO;AACP;AAAA,YACF;AAAA,UACF;AAGA,gBAAM,KAAK,gBAAgB,eAAe;AAAA,YACxC,OAAO,aAAa;AAAA,YACpB,KAAK,aAAa;AAAA,YAClB,aAAa,aAAa;AAAA,YAC1B,WAAW,aAAa;AAAA,YACxB,UAAU;AAAA,YACV,MAAM,aAAa;AAAA,YACnB,MAAM,aAAa;AAAA,UACrB,CAAC;AAED,iBAAO;AAAA,QACT,SAAS,OAAP;AACA,iBAAO,OAAO,KAAK,yCAAW,aAAa,WAAW,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,QAClH;AAAA,MACF;AAEA,aAAO,UAAU;AACjB,cAAQ,IAAI,8CAAgB,OAAO,gCAAiB,OAAO,+BAAgB,OAAO,OAAO,2BAAY;AAAA,IAEvG,SAAS,OAAP;AACA,aAAO,OAAO,KAAK,yCAAW,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AACxF,cAAQ,MAAM,iCAAiC,KAAK;AAAA,IACtD;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,uBAAwC;AAC5C,QAAI;AACF,YAAM,CAAC,WAAW,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,QAChD,KAAK,gBAAgB,gBAAgB;AAAA,QACrC,KAAK,gBAAgB,iBAAiB;AAAA,MACxC,CAAC;AAGD,YAAM,sBAAkD,CAAC;AACzD,gBAAU,QAAQ,cAAY;AAC5B,YAAI,CAAC,oBAAoB,SAAS,QAAQ,GAAG;AAC3C,8BAAoB,SAAS,QAAQ,IAAI,CAAC;AAAA,QAC5C;AACA,4BAAoB,SAAS,QAAQ,EAAE,KAAK,QAAQ;AAAA,MACtD,CAAC;AAGD,YAAM,kBAAsC;AAAA,QAC1C,UAAU,KAAK,iBAAiB;AAAA,QAChC,OAAO;AAAA,UACL,cAAc;AAAA,YACZ,YAAY,KAAK,IAAI,EAAE,SAAS;AAAA,YAChC,eAAe,KAAK,IAAI,EAAE,SAAS;AAAA,YACnC,MAAM,KAAK,aAAa;AAAA,YACxB,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU,CAAC;AAAA,UACb;AAAA,UACA,OAAO;AAAA,YACL,YAAY,KAAK,IAAI,EAAE,SAAS;AAAA,YAChC,eAAe,KAAK,IAAI,EAAE,SAAS;AAAA,YACnC,MAAM,KAAK,aAAa;AAAA,YACxB,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU,CAAC;AAAA,UACb;AAAA,UACA,QAAQ;AAAA,YACN,YAAY,KAAK,IAAI,EAAE,SAAS;AAAA,YAChC,eAAe,KAAK,IAAI,EAAE,SAAS;AAAA,YACnC,MAAM,KAAK,aAAa;AAAA,YACxB,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,UAAU,CAAC;AAAA,UACb;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAEA,UAAI,YAAY;AAGhB,iBAAW,QAAQ,cAAY;AAC7B,cAAM,oBAAoB,oBAAoB,SAAS,EAAE,KAAK,CAAC;AAE/D,YAAI,kBAAkB,SAAS,GAAG;AAChC,gBAAM,aAA6B;AAAA,YACjC,YAAY,SAAS,UAAU,SAAS;AAAA,YACxC,eAAe,KAAK,IAAI,EAAE,SAAS;AAAA,YACnC,MAAM,KAAK,aAAa;AAAA,YACxB,KAAK,aAAa,SAAS;AAAA,YAC3B,MAAM,SAAS;AAAA,YACf,MAAM;AAAA,YACN,UAAU,kBAAkB,IAAI,eAAa;AAAA,cAC3C,YAAY,SAAS,UAAU,SAAS;AAAA,cACxC,eAAe,SAAS,UAAU,SAAS;AAAA,cAC3C,MAAM,KAAK,aAAa;AAAA,cACxB,KAAK,aAAa,SAAS;AAAA,cAC3B,MAAM,SAAS;AAAA,cACf,MAAM;AAAA,cACN,KAAK,SAAS;AAAA,YAChB,EAAE;AAAA,UACJ;AAEA,0BAAgB,MAAM,aAAa,SAAU,KAAK,UAAU;AAAA,QAC9D;AAAA,MACF,CAAC;AAED,aAAO,KAAK,UAAU,iBAAiB,MAAM,CAAC;AAAA,IAChD,SAAS,OAAP;AACA,cAAQ,MAAM,mCAAmC,KAAK;AACtD,YAAM,IAAI,MAAM,0DAAkB;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,yBAA0C;AAC9C,QAAI;AACF,YAAM,CAAC,WAAW,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,QAChD,KAAK,gBAAgB,gBAAgB;AAAA,QACrC,KAAK,gBAAgB,iBAAiB;AAAA,MACxC,CAAC;AAED,UAAI,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWX,YAAM,sBAAkD,CAAC;AACzD,gBAAU,QAAQ,cAAY;AAC5B,YAAI,CAAC,oBAAoB,SAAS,QAAQ,GAAG;AAC3C,8BAAoB,SAAS,QAAQ,IAAI,CAAC;AAAA,QAC5C;AACA,4BAAoB,SAAS,QAAQ,EAAE,KAAK,QAAQ;AAAA,MACtD,CAAC;AAGD,iBAAW,QAAQ,cAAY;AAC7B,cAAM,oBAAoB,oBAAoB,SAAS,EAAE,KAAK,CAAC;AAE/D,YAAI,kBAAkB,SAAS,GAAG;AAChC,kBAAQ,yBAAyB,KAAK,MAAM,SAAS,YAAY,GAAI,qBAAqB,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI,MAAM,KAAK,WAAW,SAAS,IAAI;AAAA;AACzJ,kBAAQ;AAAA;AAER,4BAAkB,QAAQ,cAAY;AACpC,kBAAM,UAAU,KAAK,MAAM,SAAS,YAAY,GAAI;AACpD,kBAAM,eAAe,KAAK,MAAM,SAAS,YAAY,GAAI;AACzD,oBAAQ,wBAAwB,KAAK,WAAW,SAAS,GAAG,gBAAgB,2BAA2B;AACvG,gBAAI,SAAS,MAAM;AACjB,sBAAQ,UAAU,KAAK,WAAW,SAAS,IAAI;AAAA,YACjD;AACA,oBAAQ,IAAI,KAAK,WAAW,SAAS,KAAK;AAAA;AAC1C,gBAAI,SAAS,aAAa;AACxB,sBAAQ,eAAe,KAAK,WAAW,SAAS,WAAW;AAAA;AAAA,YAC7D;AAAA,UACF,CAAC;AAED,kBAAQ;AAAA;AAAA,QACV;AAAA,MACF,CAAC;AAED,cAAQ;AAAA;AAER,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,qCAAqC,KAAK;AACxD,YAAM,IAAI,MAAM,4DAAoB;AAAA,IACtC;AAAA,EACF;AAAA;AAAA,EAGQ,mBAA2B;AACjC,WAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,EACnD;AAAA;AAAA,EAGQ,eAAuB;AAC7B,WAAO,uCAAuC,QAAQ,SAAS,SAAS,GAAG;AACzE,YAAM,IAAI,KAAK,OAAO,IAAI,KAAK;AAC/B,YAAM,IAAI,MAAM,MAAM,IAAK,IAAI,IAAM;AACrC,aAAO,EAAE,SAAS,EAAE;AAAA,IACtB,CAAC;AAAA,EACH;AAAA;AAAA,EAGQ,WAAW,MAAsB;AACvC,WAAO,KACJ,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,OAAO;AAAA,EAC1B;AAAA;AAAA,EAGA,MAAM,iBAIH;AACD,QAAI;AACF,YAAM,YAAY,MAAM,KAAK,gBAAgB,gBAAgB;AAC7D,YAAM,oBAAoB,UAAU;AAAA,QAAO,OACzC,EAAE,MAAM,SAAS,UAAU,KAC3B,EAAE,aAAa,SAAS,4BAAa;AAAA,MACvC;AAEA,aAAO;AAAA,QACL,eAAe,kBAAkB;AAAA,QACjC,gBAAgB,kBAAkB,SAAS,IACvC,KAAK,IAAI,GAAG,kBAAkB,IAAI,OAAK,EAAE,SAAS,CAAC,IACnD;AAAA,QACJ,eAAe,CAAC,UAAU,YAAY,QAAQ;AAAA,MAChD;AAAA,IACF,SAAS,OAAP;AACA,cAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAO;AAAA,QACL,eAAe;AAAA,QACf,eAAe,CAAC;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AAnnBa;;;ACmDN,IAAM,YAAN,MAAgB;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EAER,YACE,iBACA,iBACA,UACA;AACA,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAGA,MAAM,iBAAiB,SAAuD;AAC5E,UAAM,CAAC,WAAW,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,MAChD,KAAK,gBAAgB,gBAAgB;AAAA,MACrC,KAAK,gBAAgB,iBAAiB;AAAA,IACxC,CAAC;AAED,UAAM,SAA2B;AAAA,MAC/B,aAAa;AAAA,QACX,yBAAyB,CAAC;AAAA,QAC1B,oBAAoB,CAAC;AAAA,QACrB,wBAAwB,CAAC;AAAA,QACzB,gBAAgB,CAAC;AAAA,QACjB,wBAAwB,CAAC;AAAA,MAC3B;AAAA,MACA,YAAY;AAAA,QACV,eAAe,UAAU;AAAA,QACzB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,wBAAwB;AAAA,QACxB,gBAAgB;AAAA,MAClB;AAAA,IACF;AAGA,QAAI,QAAQ,+BAA+B;AACzC,aAAO,YAAY,0BAA0B,MAAM,KAAK;AAAA,QACtD;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV;AACA,aAAO,WAAW,sBAAsB,OAAO,YAAY,wBAAwB;AAAA,IACrF;AAGA,QAAI,QAAQ,0BAA0B;AACpC,aAAO,YAAY,qBAAqB,MAAM,KAAK;AAAA,QACjD;AAAA,QACA,QAAQ;AAAA,MACV;AACA,aAAO,WAAW,kBAAkB,OAAO,YAAY,mBAAmB;AAAA,IAC5E;AAGA,QAAI,QAAQ,6BAA6B;AACvC,aAAO,YAAY,yBAAyB,MAAM,KAAK;AAAA,QACrD;AAAA,QACA,QAAQ;AAAA,MACV;AACA,aAAO,WAAW,yBAAyB,OAAO,YAAY,uBAAuB;AAAA,IACvF;AAGA,QAAI,QAAQ,sBAAsB;AAChC,aAAO,YAAY,iBAAiB,MAAM,KAAK;AAAA,QAC7C;AAAA,QACA,QAAQ;AAAA,MACV;AACA,aAAO,WAAW,iBAAiB,OAAO,YAAY,eAAe;AAAA,IACvE;AAGA,QAAI,QAAQ,2BAA2B;AACrC,aAAO,YAAY,yBAAyB,MAAM,KAAK;AAAA,QACrD;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAc,+BACZ,WACA,YACA,WACA;AACA,UAAM,kBAAyB,CAAC;AAEhC,eAAW,YAAY,WAAW;AAChC,YAAM,aAAa,KAAK,2BAA2B,UAAU,UAAU;AAEvE,UAAI,cAAc,WAAW,cAAc,aACvC,WAAW,sBAAsB,SAAS,UAAU;AACtD,wBAAgB,KAAK;AAAA,UACnB,YAAY,SAAS;AAAA,UACrB,iBAAiB,SAAS;AAAA,UAC1B,mBAAmB,WAAW;AAAA,UAC9B,YAAY,WAAW;AAAA,UACvB,QAAQ,WAAW;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGQ,2BAA2B,UAAoB,YAAwB;AAC7E,UAAM,MAAM,SAAS,IAAI,YAAY;AACrC,UAAM,QAAQ,SAAS,MAAM,YAAY;AACzC,UAAM,eAAe,SAAS,eAAe,IAAI,YAAY;AAG7D,UAAM,cAAc;AAAA,MAClB,EAAE,SAAS,CAAC,cAAc,cAAc,eAAe,GAAG,UAAU,4BAAQ,YAAY,IAAI;AAAA,MAC5F,EAAE,SAAS,CAAC,qBAAqB,mBAAmB,GAAG,UAAU,4BAAQ,YAAY,IAAI;AAAA,MACzF,EAAE,SAAS,CAAC,eAAe,UAAU,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MACxE,EAAE,SAAS,CAAC,eAAe,gBAAgB,eAAe,GAAG,UAAU,4BAAQ,YAAY,IAAI;AAAA,MAC/F,EAAE,SAAS,CAAC,cAAc,cAAc,QAAQ,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MACnF,EAAE,SAAS,CAAC,SAAS,WAAW,SAAS,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MAC5E,EAAE,SAAS,CAAC,eAAe,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MAC9D,EAAE,SAAS,CAAC,SAAS,eAAe,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,IACzE;AAGA,UAAM,eAAe;AAAA,MACnB,EAAE,UAAU,CAAC,OAAO,iBAAiB,MAAM,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MAC9E,EAAE,UAAU,CAAC,YAAY,SAAS,QAAQ,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MAC7E,EAAE,UAAU,CAAC,QAAQ,WAAW,WAAW,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MAC9E,EAAE,UAAU,CAAC,QAAQ,SAAS,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MACjE,EAAE,UAAU,CAAC,UAAU,MAAM,IAAI,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,MACpE,EAAE,UAAU,CAAC,QAAQ,QAAQ,GAAG,UAAU,gBAAM,YAAY,IAAI;AAAA,IAClE;AAEA,QAAI,YAAY;AAChB,QAAI,gBAAgB;AAGpB,eAAW,QAAQ,aAAa;AAC9B,iBAAW,UAAU,KAAK,SAAS;AACjC,YAAI,IAAI,SAAS,MAAM,GAAG;AACxB,gBAAM,WAAW,WAAW,KAAK,OAAK,EAAE,KAAK,SAAS,KAAK,QAAQ,CAAC;AACpE,cAAI,YAAY,KAAK,aAAa,eAAe;AAC/C,wBAAY;AAAA,cACV,mBAAmB,SAAS;AAAA,cAC5B,YAAY,KAAK;AAAA,cACjB,QAAQ,4BAAQ;AAAA,YAClB;AACA,4BAAgB,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,eAAW,QAAQ,cAAc;AAC/B,iBAAW,WAAW,KAAK,UAAU;AACnC,YAAI,MAAM,SAAS,OAAO,KAAK,YAAY,SAAS,OAAO,GAAG;AAC5D,gBAAM,WAAW,WAAW,KAAK,OAAK,EAAE,KAAK,SAAS,KAAK,QAAQ,CAAC;AACpE,cAAI,YAAY,KAAK,aAAa,eAAe;AAC/C,wBAAY;AAAA,cACV,mBAAmB,SAAS;AAAA,cAC5B,YAAY,KAAK;AAAA,cACjB,QAAQ,mCAAU;AAAA,YACpB;AACA,4BAAgB,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAc,iBAAiB,WAAuB,WAAmB;AACvE,UAAM,aAAoB,CAAC;AAC3B,UAAM,YAAY,oBAAI,IAAY;AAElC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,UAAU,IAAI,UAAU,CAAC,EAAE,EAAE;AAAG;AAEpC,YAAM,mBAAmB,CAAC,UAAU,CAAC,CAAC;AAEtC,eAAS,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC7C,YAAI,UAAU,IAAI,UAAU,CAAC,EAAE,EAAE;AAAG;AAEpC,cAAM,aAAa,KAAK,oBAAoB,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AAEtE,YAAI,cAAc,WAAW;AAC3B,2BAAiB,KAAK,UAAU,CAAC,CAAC;AAClC,oBAAU,IAAI,UAAU,CAAC,EAAE,EAAE;AAAA,QAC/B;AAAA,MACF;AAEA,UAAI,iBAAiB,SAAS,GAAG;AAC/B,mBAAW,KAAK;AAAA,UACd,WAAW;AAAA,UACX,YAAY,KAAK,yBAAyB,gBAAgB;AAAA,UAC1D,QAAQ,KAAK,mBAAmB,gBAAgB;AAAA,QAClD,CAAC;AAED,yBAAiB,QAAQ,OAAK,UAAU,IAAI,EAAE,EAAE,CAAC;AAAA,MACnD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGQ,oBAAoB,WAAqB,WAA6B;AAE5E,QAAI,UAAU,QAAQ,UAAU,KAAK;AACnC,aAAO;AAAA,IACT;AAGA,UAAM,kBAAkB,KAAK,wBAAwB,UAAU,OAAO,UAAU,KAAK;AAGrF,UAAM,YAAY,KAAK,SAAS,UAAU,GAAG;AAC7C,UAAM,YAAY,KAAK,SAAS,UAAU,GAAG;AAC7C,UAAM,gBAAgB,KAAK,wBAAwB,WAAW,SAAS;AAGvE,WAAQ,kBAAkB,MAAM,gBAAgB;AAAA,EAClD;AAAA;AAAA,EAGQ,wBAAwB,OAAe,OAAuB;AACpE,UAAM,SAAS,IAAI,IAAI,MAAM,YAAY,EAAE,MAAM,KAAK,CAAC;AACvD,UAAM,SAAS,IAAI,IAAI,MAAM,YAAY,EAAE,MAAM,KAAK,CAAC;AAEvD,UAAM,eAAe,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,OAAK,OAAO,IAAI,CAAC,CAAC,CAAC;AACnE,UAAM,QAAQ,oBAAI,IAAI,CAAC,GAAG,QAAQ,GAAG,MAAM,CAAC;AAE5C,WAAO,aAAa,OAAO,MAAM;AAAA,EACnC;AAAA;AAAA,EAGQ,SAAS,KAAqB;AACpC,QAAI;AACF,YAAM,SAAS,IAAI,IAAI,GAAG;AAC1B,aAAO,GAAG,OAAO,WAAW,OAAO;AAAA,IACrC,QAAE;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGQ,yBAAyB,WAA+B;AAC9D,QAAI,UAAU,SAAS;AAAG,aAAO;AAEjC,QAAI,kBAAkB;AACtB,QAAI,cAAc;AAElB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,eAAS,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC7C,2BAAmB,KAAK,oBAAoB,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AACtE;AAAA,MACF;AAAA,IACF;AAEA,WAAO,cAAc,IAAI,kBAAkB,cAAc;AAAA,EAC3D;AAAA;AAAA,EAGQ,mBAAmB,WAA+B;AACxD,QAAI,UAAU,SAAS;AAAG,aAAO;AAGjC,UAAMC,QAAO,UAAU,IAAI,OAAK,EAAE,GAAG;AACrC,UAAM,aAAa,IAAI,IAAIA,KAAI;AAE/B,QAAI,WAAW,OAAOA,MAAK,QAAQ;AACjC,aAAO;AAAA,IACT;AAGA,UAAM,SAAS,UAAU,IAAI,OAAK,EAAE,KAAK;AACzC,UAAM,kBAAkB,KAAK,wBAAwB,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAEzE,QAAI,kBAAkB,KAAK;AACzB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAc,qBAAqB,WAAuB,WAAmB;AAC3E,UAAM,cAAqB,CAAC;AAE5B,eAAW,YAAY,WAAW;AAChC,UAAI,CAAC,SAAS,eAAe,SAAS,YAAY,SAAS,IAAI;AAC7D,cAAM,aAAa,KAAK,+BAA+B,QAAQ;AAE/D,YAAI,cAAc,WAAW,cAAc,WAAW;AACpD,sBAAY,KAAK;AAAA,YACf,YAAY,SAAS;AAAA,YACrB,oBAAoB,SAAS,eAAe;AAAA,YAC5C,sBAAsB,WAAW;AAAA,YACjC,YAAY,WAAW;AAAA,UACzB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGQ,+BAA+B,UAAoB;AACzD,UAAM,MAAM,SAAS,IAAI,YAAY;AACrC,UAAM,QAAQ,SAAS;AAGvB,UAAM,qBAA6C;AAAA,MACjD,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAEA,eAAW,CAAC,QAAQ,WAAW,KAAK,OAAO,QAAQ,kBAAkB,GAAG;AACtE,UAAI,IAAI,SAAS,MAAM,GAAG;AACxB,eAAO;AAAA,UACL,aAAa,GAAG,WAAW;AAAA,UAC3B,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAGA,QAAI,IAAI,SAAS,QAAQ,KAAK,IAAI,SAAS,iBAAiB,GAAG;AAC7D,aAAO;AAAA,QACL,aAAa,GAAG;AAAA,QAChB,YAAY;AAAA,MACd;AAAA,IACF;AAEA,QAAI,IAAI,SAAS,QAAQ,KAAK,IAAI,SAAS,WAAW,GAAG;AACvD,aAAO;AAAA,QACL,aAAa,GAAG;AAAA,QAChB,YAAY;AAAA,MACd;AAAA,IACF;AAEA,QAAI,IAAI,SAAS,YAAY,KAAK,IAAI,SAAS,SAAS,GAAG;AACzD,aAAO;AAAA,QACL,aAAa,GAAG;AAAA,QAChB,YAAY;AAAA,MACd;AAAA,IACF;AAGA,WAAO;AAAA,MACL,aAAa,GAAG;AAAA,MAChB,YAAY;AAAA,IACd;AAAA,EACF;AAAA;AAAA,EAGA,MAAc,YAAY,WAAuB,WAAmB;AAClE,UAAM,cAAqB,CAAC;AAE5B,eAAW,YAAY,WAAW;AAChC,YAAM,OAAO,KAAK,wBAAwB,QAAQ;AAElD,UAAI,QAAQ,KAAK,cAAc,WAAW;AACxC,oBAAY,KAAK;AAAA,UACf,YAAY,SAAS;AAAA,UACrB,eAAe,KAAK;AAAA,UACpB,YAAY,KAAK;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGQ,wBAAwB,UAAoB;AAClD,UAAM,MAAM,SAAS,IAAI,YAAY;AACrC,UAAM,QAAQ,SAAS,MAAM,YAAY;AACzC,UAAM,OAAiB,CAAC;AACxB,QAAI,aAAa;AAGjB,UAAM,aAAuC;AAAA,MAC3C,cAAc,CAAC,gBAAM,gBAAM,KAAK;AAAA,MAChC,qBAAqB,CAAC,gBAAM,gBAAM,cAAI;AAAA,MACtC,eAAe,CAAC,gBAAM,cAAI;AAAA,MAC1B,eAAe,CAAC,gBAAM,cAAI;AAAA,MAC1B,gBAAgB,CAAC,gBAAM,gBAAM,cAAI;AAAA,MACjC,cAAc,CAAC,gBAAM,cAAI;AAAA,MACzB,iBAAiB,CAAC,gBAAM,cAAI;AAAA,IAC9B;AAEA,eAAW,CAAC,QAAQ,aAAa,KAAK,OAAO,QAAQ,UAAU,GAAG;AAChE,UAAI,IAAI,SAAS,MAAM,GAAG;AACxB,aAAK,KAAK,GAAG,aAAa;AAC1B,qBAAa,KAAK,IAAI,YAAY,GAAG;AAAA,MACvC;AAAA,IACF;AAGA,UAAM,cAAwC;AAAA,MAC5C,OAAO,CAAC,OAAO,gBAAM,cAAI;AAAA,MACzB,YAAY,CAAC,gBAAM,cAAI;AAAA,MACvB,iBAAiB,CAAC,gBAAM,cAAI;AAAA,MAC5B,QAAQ,CAAC,gBAAM,cAAI;AAAA,MACnB,aAAa,CAAC,gBAAM,cAAI;AAAA,MACxB,WAAW,CAAC,UAAK,cAAI;AAAA,MACrB,UAAU,CAAC,gBAAM,IAAI;AAAA,MACrB,OAAO,CAAC,OAAO,gBAAM,cAAI;AAAA,MACzB,cAAc,CAAC,cAAc,MAAM,cAAI;AAAA,MACvC,UAAU,CAAC,UAAU,cAAI;AAAA,MACzB,SAAS,CAAC,SAAS,gBAAM,cAAI;AAAA,IAC/B;AAEA,eAAW,CAAC,SAAS,cAAc,KAAK,OAAO,QAAQ,WAAW,GAAG;AACnE,UAAI,MAAM,SAAS,OAAO,KAAK,IAAI,SAAS,OAAO,GAAG;AACpD,aAAK,KAAK,GAAG,cAAc;AAC3B,qBAAa,KAAK,IAAI,YAAY,GAAG;AAAA,MACvC;AAAA,IACF;AAGA,UAAM,aAAa,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC;AAEpC,WAAO,WAAW,SAAS,IAAI;AAAA,MAC7B,MAAM;AAAA,MACN;AAAA,IACF,IAAI;AAAA,EACN;AAAA;AAAA,EAGA,MAAc,qBACZ,WACA,YACA,WACA;AACA,UAAM,cAAqB,CAAC;AAG5B,UAAM,yBAAyB,UAAU,OAAO,OAAK;AACnD,YAAM,WAAW,WAAW,KAAK,OAAK,EAAE,OAAO,EAAE,QAAQ;AACzD,aAAO,CAAC,YAAY,SAAS,SAAS,wBAAS,SAAS,SAAS;AAAA,IACnE,CAAC;AAGD,UAAM,eAAe,KAAK,uBAAuB,sBAAsB;AAEvE,eAAW,CAAC,QAAQ,eAAe,KAAK,OAAO,QAAQ,YAAY,GAAG;AACpE,UAAI,gBAAgB,UAAU,GAAG;AAC/B,cAAM,eAAe,KAAK,6BAA6B,MAAM;AAC7D,YAAI,cAAc;AAChB,sBAAY,KAAK;AAAA,YACf,MAAM;AAAA,YACN,aAAa,gBAAM;AAAA,YACnB,aAAa,gBAAgB,IAAI,OAAK,EAAE,EAAE;AAAA,YAC1C,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGQ,uBAAuB,WAAmD;AAChF,UAAM,SAAqC,CAAC;AAE5C,eAAW,YAAY,WAAW;AAChC,UAAI;AACF,cAAM,SAAS,IAAI,IAAI,SAAS,GAAG,EAAE;AACrC,YAAI,CAAC,OAAO,MAAM,GAAG;AACnB,iBAAO,MAAM,IAAI,CAAC;AAAA,QACpB;AACA,eAAO,MAAM,EAAE,KAAK,QAAQ;AAAA,MAC9B,QAAE;AAAA,MAEF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGQ,6BAA6B,QAA+B;AAClE,UAAM,mBAA2C;AAAA,MAC/C,cAAc;AAAA,MACd,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,UAAU;AAAA,MACV,eAAe;AAAA,MACf,gBAAgB;AAAA,IAClB;AAEA,WAAO,iBAAiB,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA,EAGA,MAAM,mBACJ,gBACA,qBAOgD;AAChD,QAAI,UAAU;AACd,UAAM,SAAmB,CAAC;AAE1B,QAAI;AAEF,UAAI,oBAAoB,yBAAyB;AAC/C,mBAAW,gBAAgB,oBAAoB,yBAAyB;AACtE,gBAAM,aAAa,eAAe,YAAY,wBAAwB;AAAA,YACpE,OAAK,GAAG,EAAE,cAAc,EAAE,wBAAwB;AAAA,UACpD;AAEA,cAAI,YAAY;AACd,gBAAI;AACF,oBAAM,KAAK,gBAAgB,eAAe,WAAW,YAAY;AAAA,gBAC/D,UAAU,WAAW;AAAA,cACvB,CAAC;AACD;AAAA,YACF,SAAS,OAAP;AACA,qBAAO,KAAK,qDAAa,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,YACrF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,oBAAoB,oBAAoB;AAC1C,mBAAW,gBAAgB,oBAAoB,oBAAoB;AACjE,gBAAM,aAAa,eAAe,YAAY,uBAAuB;AAAA,YACnE,OAAK,EAAE,eAAe;AAAA,UACxB;AAEA,cAAI,YAAY;AACd,gBAAI;AACF,oBAAM,KAAK,gBAAgB,eAAe,WAAW,YAAY;AAAA,gBAC/D,aAAa,WAAW;AAAA,cAC1B,CAAC;AACD;AAAA,YACF,SAAS,OAAP;AACA,qBAAO,KAAK,qDAAa,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,YACrF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,oBAAoB,YAAY;AAClC,mBAAW,gBAAgB,oBAAoB,YAAY;AACzD,gBAAM,aAAa,eAAe,YAAY,eAAe;AAAA,YAC3D,OAAK,EAAE,eAAe;AAAA,UACxB;AAEA,cAAI,YAAY;AACd,gBAAI;AACF,oBAAM,KAAK,gBAAgB,eAAe,WAAW,YAAY;AAAA,gBAC/D,MAAM,WAAW;AAAA,cACnB,CAAC;AACD;AAAA,YACF,SAAS,OAAP;AACA,qBAAO,KAAK,qDAAa,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,YACrF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,oBAAoB,eAAe;AACrC,mBAAW,gBAAgB,oBAAoB,eAAe;AAC5D,gBAAM,aAAa,eAAe,YAAY,uBAAuB;AAAA,YACnE,OAAK,EAAE,SAAS;AAAA,UAClB;AAEA,cAAI,YAAY;AACd,gBAAI;AACF,oBAAM,cAAc,MAAM,KAAK,gBAAgB,eAAe;AAAA,gBAC5D,MAAM,WAAW;AAAA,gBACjB,aAAa,WAAW;AAAA,gBACxB,OAAO;AAAA,cACT,CAAC;AAGD,yBAAW,cAAc,WAAW,aAAa;AAC/C,sBAAM,KAAK,gBAAgB,eAAe,YAAY;AAAA,kBACpD,UAAU,YAAY;AAAA,gBACxB,CAAC;AAAA,cACH;AAEA;AAAA,YACF,SAAS,OAAP;AACA,qBAAO,KAAK,+CAAY,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,YACpF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,oBAAoB,mBAAmB;AACzC,mBAAW,gBAAgB,oBAAoB,mBAAmB;AAChE,cAAI;AACF,kBAAM,KAAK,gBAAgB,eAAe,YAAY;AACtD;AAAA,UACF,SAAS,OAAP;AACA,mBAAO,KAAK,qDAAa,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,UACrF;AAAA,QACF;AAAA,MACF;AAAA,IAEF,SAAS,OAAP;AACA,aAAO,KAAK,mDAAgB,iBAAiB,QAAQ,MAAM,UAAU,iBAAiB;AAAA,IACxF;AAEA,WAAO,EAAE,SAAS,OAAO;AAAA,EAC3B;AAAA;AAAA,EAGA,cAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAGA,eAAe,QAAiC;AAC9C,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,OAAO;AAAA,EAChD;AACF;AA1oBa;;;ACiBN,IAAM,oBAAN,MAAwB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EAER,YAAY,WAAsB;AAChC,SAAK,YAAY;AACjB,SAAK,kBAAkB,IAAI,gBAAgB,SAAS;AACpD,SAAK,kBAAkB,IAAI,gBAAgB,SAAS;AAAA,EACtD;AAAA;AAAA,EAGA,MAAM,mBAA2C;AAC/C,UAAM,CAAC,OAAO,WAAW,UAAU,IAAI,MAAM,QAAQ,IAAI;AAAA,MACvD,KAAK,UAAU,SAAS;AAAA,MACxB,KAAK,gBAAgB,gBAAgB;AAAA,MACrC,KAAK,gBAAgB,iBAAiB;AAAA,IACxC,CAAC;AAED,WAAO;AAAA,MACL,UAAU,KAAK,uBAAuB,OAAO,WAAW,UAAU;AAAA,MAClE,eAAe,KAAK,uBAAuB,OAAO,WAAW,UAAU;AAAA,MACvE,eAAe,KAAK,uBAAuB,OAAO,WAAW,UAAU;AAAA,MACvE,aAAa,KAAK,qBAAqB,KAAK;AAAA,MAC5C,WAAW,KAAK,mBAAmB,KAAK;AAAA,MACxC,aAAa,KAAK,qBAAqB,KAAK;AAAA,MAC5C,kBAAkB,KAAK,0BAA0B;AAAA,IACnD;AAAA,EACF;AAAA;AAAA,EAGQ,uBAAuB,OAAc,WAAuB,YAAwB;AAC1F,UAAM,cAAc,UAAU,OAAO,CAAC,KAAK,aAAa,MAAM,SAAS,YAAY,CAAC;AAEpF,WAAO;AAAA,MACL,gBAAgB,UAAU;AAAA,MAC1B,iBAAiB,WAAW;AAAA,MAC5B;AAAA,MACA,YAAY,MAAM;AAAA,MAClB,0BAA0B,UAAU,SAAS,IAAI,cAAc,UAAU,SAAS;AAAA,IACpF;AAAA,EACF;AAAA;AAAA,EAGQ,uBAAuB,OAAc,WAAuB,YAAwB;AAE1F,UAAM,eAAe,UAClB,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU,EAC1C,MAAM,GAAG,EAAE,EACX,IAAI,eAAa;AAAA,MAChB;AAAA,MACA,QAAQ,SAAS;AAAA,MACjB,WAAW,MAAM,cAAc,IAAK,SAAS,aAAa,MAAM,cAAe,MAAM;AAAA,IACvF,EAAE;AAGJ,UAAM,kBAAkB,UACrB,KAAK,CAAC,GAAG,MAAM,EAAE,YAAY,EAAE,SAAS,EACxC,MAAM,GAAG,EAAE;AAGd,UAAM,sBAA8C,CAAC;AACrD,cAAU,QAAQ,cAAY;AAC5B,0BAAoB,SAAS,QAAQ,KAAK,oBAAoB,SAAS,QAAQ,KAAK,KAAK;AAAA,IAC3F,CAAC;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGQ,uBAAuB,OAAc,WAAuB,YAAwB;AAE1F,UAAM,qBAA6C,CAAC;AACpD,cAAU,QAAQ,cAAY;AAC5B,yBAAmB,SAAS,QAAQ,KAAK,mBAAmB,SAAS,QAAQ,KAAK,KAAK,SAAS;AAAA,IAClG,CAAC;AAGD,UAAM,gBAAgB,WACnB,IAAI,cAAY;AACf,YAAM,gBAAgB,UAAU,OAAO,OAAK,EAAE,aAAa,SAAS,EAAE,EAAE;AACxE,YAAM,cAAc,mBAAmB,SAAS,EAAE,KAAK;AACvD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe,gBAAgB,IAAI,cAAc,gBAAgB;AAAA,MACnE;AAAA,IACF,CAAC,EACA,KAAK,CAAC,GAAG,MAAM,EAAE,cAAc,EAAE,WAAW,EAC5C,MAAM,GAAG,EAAE;AAGd,UAAM,iBAAiB,UAAU;AACjC,UAAM,uBAAuB,WAC1B,IAAI,cAAY;AACf,YAAM,QAAQ,UAAU,OAAO,OAAK,EAAE,aAAa,SAAS,EAAE,EAAE;AAChE,aAAO;AAAA,QACL,cAAc,SAAS;AAAA,QACvB;AAAA,QACA,YAAY,iBAAiB,IAAK,QAAQ,iBAAkB,MAAM;AAAA,MACpE;AAAA,IACF,CAAC,EACA,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAEnC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGQ,qBAAqB,OAAc;AACzC,UAAM,gBAAgB,OAAO,QAAQ,MAAM,WAAW;AACtD,UAAM,gBAAgB,cAAc,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,MAAM,MAAM,OAAO,CAAC;AAE7E,UAAM,cAAc,cACjB,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,EAC5B,MAAM,GAAG,EAAE,EACX,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO;AAAA,MACxB;AAAA,MACA;AAAA,MACA,YAAY,gBAAgB,IAAK,QAAQ,gBAAiB,MAAM;AAAA,IAClE,EAAE;AAEJ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,gBAAgB,cAAc;AAAA,IAChC;AAAA,EACF;AAAA;AAAA,EAGQ,mBAAmB,OAAc;AACvC,UAAM,aAAa,MAAM;AAGzB,UAAM,cAAc,KAAK,gBAAgB,UAAU;AAGnD,UAAM,eAAe,KAAK,iBAAiB,UAAU;AAErD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGQ,qBAAqB,OAAc;AACzC,UAAM,EAAE,QAAQ,SAAS,OAAO,IAAI,MAAM;AAC1C,UAAM,QAAQ,SAAS,UAAU;AAEjC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAkB,QAAQ,IAAK,SAAS,QAAS,MAAM;AAAA,MACvD,mBAAmB,QAAQ,IAAK,UAAU,QAAS,MAAM;AAAA,MACzD,kBAAkB,QAAQ,IAAK,SAAS,QAAS,MAAM;AAAA,IACzD;AAAA,EACF;AAAA;AAAA,EAGQ,4BAA4B;AAElC,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGQ,gBAAgB,YAAoC;AAC1D,UAAM,aAAgE,CAAC;AAEvE,WAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,CAAC,MAAM,MAAM,MAAM;AACrD,YAAM,YAAY,KAAK,aAAa,IAAI,KAAK,IAAI,CAAC;AAClD,YAAM,UAAU,UAAU,YAAY,EAAE,MAAM,GAAG,EAAE,CAAC;AAEpD,UAAI,CAAC,WAAW,OAAO,GAAG;AACxB,mBAAW,OAAO,IAAI,EAAE,QAAQ,GAAG,OAAO,EAAE;AAAA,MAC9C;AACA,iBAAW,OAAO,EAAE,UAAU;AAAA,IAChC,CAAC;AAED,WAAO,OAAO,QAAQ,UAAU,EAC7B,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,EAAE,MAAM,GAAG,KAAK,EAAE,EACzC,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,cAAc,EAAE,IAAI,CAAC;AAAA,EAChD;AAAA;AAAA,EAGQ,iBAAiB,YAAoC;AAC3D,UAAM,cAAiE,CAAC;AAExE,WAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,CAAC,MAAM,MAAM,MAAM;AACrD,YAAM,WAAW,KAAK,UAAU,GAAG,CAAC;AAEpC,UAAI,CAAC,YAAY,QAAQ,GAAG;AAC1B,oBAAY,QAAQ,IAAI,EAAE,QAAQ,GAAG,OAAO,EAAE;AAAA,MAChD;AACA,kBAAY,QAAQ,EAAE,UAAU;AAAA,IAClC,CAAC;AAED,WAAO,OAAO,QAAQ,WAAW,EAC9B,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,EAAE,OAAO,GAAG,KAAK,EAAE,EAC3C,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,CAAC;AAAA,EAClD;AAAA;AAAA,EAGQ,aAAa,MAAkB;AACrC,UAAM,IAAI,IAAI,KAAK,IAAI;AACvB,UAAM,MAAM,EAAE,OAAO;AACrB,UAAM,OAAO,EAAE,QAAQ,IAAI,OAAO,QAAQ,IAAI,KAAK;AACnD,WAAO,IAAI,KAAK,EAAE,QAAQ,IAAI,CAAC;AAAA,EACjC;AAAA;AAAA,EAGA,MAAM,eAAe,WAAmC;AACtD,QAAI;AACF,YAAM,QAAQ,MAAM,KAAK,UAAU,SAAS;AAC5C,YAAM;AAGN,UAAI,WAAW;AACb,cAAM,aAAa,KAAK,iBAAiB,SAAS;AAClD,cAAM,YAAY,UAAU;AAAA,MAC9B;AAGA,YAAM,SAAQ,oBAAI,KAAK,GAAE,YAAY,EAAE,MAAM,GAAG,EAAE,CAAC;AACnD,YAAM,WAAW,KAAK,KAAK,MAAM,WAAW,KAAK,KAAK,KAAK;AAE3D,YAAM,KAAK,UAAU,YAAY,KAAK;AAAA,IACxC,SAAS,OAAP;AACA,cAAQ,MAAM,+BAA+B,KAAK;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAGQ,iBAAiB,WAAoD;AAC3E,UAAM,KAAK,UAAU,YAAY;AAEjC,QAAI,4BAA4B,KAAK,EAAE,GAAG;AACxC,aAAO;AAAA,IACT;AAEA,QAAI,wFAAwF,KAAK,EAAE,GAAG;AACpG,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,aAAa,OAA8B;AAC/C,QAAI;AACF,UAAI,CAAC,MAAM,KAAK;AAAG;AAEnB,YAAM,QAAQ,MAAM,KAAK,UAAU,SAAS;AAC5C,YAAM,YAAY,KAAK,KAAK,MAAM,YAAY,KAAK,KAAK,KAAK;AAC7D,YAAM,KAAK,UAAU,YAAY,KAAK;AAAA,IACxC,SAAS,OAAP;AACA,cAAQ,MAAM,4BAA4B,KAAK;AAAA,IACjD;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,gBAAgB,aAAqB,IAAmB;AAC5D,QAAI;AACF,YAAM,QAAQ,MAAM,KAAK,UAAU,SAAS;AAC5C,YAAM,aAAa,oBAAI,KAAK;AAC5B,iBAAW,QAAQ,WAAW,QAAQ,IAAI,UAAU;AACpD,YAAM,eAAe,WAAW,YAAY,EAAE,MAAM,GAAG,EAAE,CAAC;AAG1D,aAAO,KAAK,MAAM,UAAU,EAAE,QAAQ,UAAQ;AAC5C,YAAI,OAAO,cAAc;AACvB,iBAAO,MAAM,WAAW,IAAI;AAAA,QAC9B;AAAA,MACF,CAAC;AAED,YAAM,KAAK,UAAU,YAAY,KAAK;AACtC,cAAQ,IAAI,+BAA+B,iBAAiB;AAAA,IAC9D,SAAS,OAAP;AACA,cAAQ,MAAM,gCAAgC,KAAK;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,cAAsC;AAC1C,WAAO,MAAM,KAAK,iBAAiB;AAAA,EACrC;AAAA;AAAA,EAGA,MAAM,aAA4B;AAChC,QAAI;AACF,YAAM,KAAK,UAAU,YAAY;AAAA,QAC/B,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,eAAe,CAAC;AAAA,QAChB,eAAe,CAAC;AAAA,QAChB,aAAa,CAAC;AAAA,QACd,YAAY,CAAC;AAAA,QACb,aAAa,EAAE,QAAQ,GAAG,SAAS,GAAG,QAAQ,EAAE;AAAA,QAChD,aAAa,KAAK,IAAI;AAAA,MACxB,CAAC;AACD,cAAQ,IAAI,+BAA+B;AAAA,IAC7C,SAAS,OAAP;AACA,cAAQ,MAAM,0BAA0B,KAAK;AAC7C,YAAM;AAAA,IACR;AAAA,EACF;AACF;AA/Ta;;;AC7Db,IAAM,MAAM,IAAIC,MAAc;AAG9B,IAAI,IAAI,KAAK,OAAO,GAAG,SAAS;AAC9B,QAAM,YAAY,EAAE,IAAI,WAAW;AACnC,QAAM,kBAAkB,IAAI,gBAAgB,SAAS;AACrD,QAAM,kBAAkB,IAAI,gBAAgB,SAAS;AAGrD,QAAM,SAAS,MAAM,UAAU,UAAU;AAEzC,IAAE,IAAI,mBAAmB,eAAe;AACxC,IAAE,IAAI,mBAAmB,eAAe;AACxC,IAAE,IAAI,oBAAoB,IAAI,iBAAiB,SAAS,CAAC;AACzD,IAAE,IAAI,yBAAyB,IAAI,sBAAsB,iBAAiB,eAAe,CAAC;AAC1F,IAAE,IAAI,aAAa,IAAI,UAAU,iBAAiB,iBAAiB,OAAO,QAAQ,CAAC;AACnF,IAAE,IAAI,qBAAqB,IAAI,kBAAkB,SAAS,CAAC;AAC3D,QAAM,KAAK;AACb,CAAC;AAKD,IAAI,IAAI,cAAc,OAAO,MAAM;AACjC,MAAI;AACF,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,YAAY,MAAM,gBAAgB,gBAAgB;AAExD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,kBAAkB,OAAO,MAAM;AACrC,MAAI;AACF,UAAM,KAAK,EAAE,IAAI,MAAM,IAAI;AAC3B,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,WAAW,MAAM,gBAAgB,gBAAgB,EAAE;AAEzD,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,uBAAuB,KAAK;AAC1C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,cAAc,OAAO,MAAM;AAClC,MAAI;AACF,UAAM,eAAe,MAAM,EAAE,IAAI,KAAK;AACtC,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAG/C,UAAM,aAAa,gBAAgB,qBAAqB,YAAY;AACpE,QAAI,CAAC,WAAW,OAAO;AACrB,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,MAAM,EAAE,QAAQ,WAAW,OAAO;AAAA,MACpC,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,YAAY,MAAM,gBAAgB,uBAAuB,aAAa,GAAG;AAC/E,QAAI,WAAW;AACb,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,WAAW,MAAM,gBAAgB,eAAe,YAAY;AAElE,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG;AAAA,EACR,SAAS,OAAP;AACA,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,kBAAkB,OAAO,MAAM;AACrC,MAAI;AACF,UAAM,KAAK,EAAE,IAAI,MAAM,IAAI;AAC3B,UAAM,UAAU,MAAM,EAAE,IAAI,KAAK;AACjC,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAE/C,UAAM,WAAW,MAAM,gBAAgB,eAAe,IAAI,OAAO;AAEjE,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,OAAO,kBAAkB,OAAO,MAAM;AACxC,MAAI;AACF,UAAM,KAAK,EAAE,IAAI,MAAM,IAAI;AAC3B,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAE/C,UAAM,UAAU,MAAM,gBAAgB,eAAe,EAAE;AAEvD,QAAI,CAAC,SAAS;AACZ,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,2BAA2B,OAAO,MAAM;AAC/C,MAAI;AACF,UAAM,EAAE,IAAI,IAAI,MAAM,EAAE,IAAI,KAAK;AAEjC,QAAI,CAAC,MAAM,QAAQ,GAAG,KAAK,IAAI,WAAW,GAAG;AAC3C,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,SAAS,MAAM,gBAAgB,gBAAgB,GAAG;AAExD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,WAAW,OAAO,QAAQ;AAAA,IACrC,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,iCAAiC,KAAK;AACpD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAKD,IAAI,IAAI,eAAe,OAAO,MAAM;AAClC,MAAI;AACF,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,aAAa,MAAM,gBAAgB,iBAAiB;AAE1D,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,mBAAmB,OAAO,MAAM;AACtC,MAAI;AACF,UAAM,KAAK,EAAE,IAAI,MAAM,IAAI;AAC3B,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,WAAW,MAAM,gBAAgB,gBAAgB,EAAE;AAEzD,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,uBAAuB,KAAK;AAC1C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,eAAe,OAAO,MAAM;AACnC,MAAI;AACF,UAAM,eAAe,MAAM,EAAE,IAAI,KAAK;AACtC,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAG/C,UAAM,aAAa,gBAAgB,qBAAqB,YAAY;AACpE,QAAI,CAAC,WAAW,OAAO;AACrB,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,MAAM,EAAE,QAAQ,WAAW,OAAO;AAAA,MACpC,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,YAAY,MAAM,gBAAgB,2BAA2B,aAAa,IAAI;AACpF,QAAI,WAAW;AACb,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,WAAW,MAAM,gBAAgB,eAAe,YAAY;AAElE,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG;AAAA,EACR,SAAS,OAAP;AACA,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,mBAAmB,OAAO,MAAM;AACtC,MAAI;AACF,UAAM,KAAK,EAAE,IAAI,MAAM,IAAI;AAC3B,UAAM,UAAU,MAAM,EAAE,IAAI,KAAK;AACjC,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAE/C,UAAM,WAAW,MAAM,gBAAgB,eAAe,IAAI,OAAO;AAEjE,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,OAAO,mBAAmB,OAAO,MAAM;AACzC,MAAI;AACF,UAAM,KAAK,EAAE,IAAI,MAAM,IAAI;AAC3B,UAAM,EAAE,iBAAiB,gBAAgB,IAAI,EAAE,IAAI,MAAM;AACzD,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAE/C,UAAM,UAAe,CAAC;AACtB,QAAI;AAAiB,cAAQ,kBAAkB;AAC/C,QAAI,oBAAoB;AAAQ,cAAQ,kBAAkB;AAE1D,UAAM,SAAS,MAAM,gBAAgB,eAAe,IAAI,OAAO;AAE/D,QAAI,CAAC,OAAO,SAAS;AACnB,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO,iBAAiB,QAAQ,MAAM,UAAU;AAAA,IAClD,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,qBAAqB,OAAO,MAAM;AACxC,MAAI;AACF,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,QAAQ,MAAM,gBAAgB,iBAAiB;AAErD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,6BAA6B,KAAK;AAChD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,UAAU,OAAO,MAAM;AAC7B,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,QAAQ,MAAM,UAAU,SAAS;AAEvC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,oBAAoB,KAAK;AACvC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAKD,IAAI,KAAK,gBAAgB,OAAO,MAAM;AACpC,MAAI;AACF,UAAM,EAAE,WAAW,IAAI,MAAM,EAAE,IAAI,KAAK;AAExC,QAAI,CAAC,YAAY;AACf,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,UAAU,MAAM,gBAAgB,oBAAoB,UAAU;AAEpE,QAAI,SAAS;AACX,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH,OAAO;AACL,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAAA,EACF,SAAS,OAAP;AACA,YAAQ,MAAM,uBAAuB,KAAK;AAC1C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,sBAAsB,OAAO,MAAM;AACzC,MAAI;AACF,UAAM,QAAQ,SAAS,EAAE,IAAI,MAAM,OAAO,KAAK,IAAI;AACnD,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,YAAY,MAAM,gBAAgB,oBAAoB,KAAK;AAEjE,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,gCAAgC,KAAK;AACnD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,qBAAqB,OAAO,MAAM;AACxC,MAAI;AACF,UAAM,QAAQ,SAAS,EAAE,IAAI,MAAM,OAAO,KAAK,IAAI;AACnD,UAAM,kBAAkB,EAAE,IAAI,iBAAiB;AAC/C,UAAM,YAAY,MAAM,gBAAgB,mBAAmB,KAAK;AAEhE,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,+BAA+B,KAAK;AAClD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAKD,IAAI,IAAI,qBAAqB,OAAO,MAAM;AACxC,MAAI;AACF,UAAM,mBAAmB,EAAE,IAAI,kBAAkB;AACjD,UAAM,SAAS,MAAM,iBAAiB,mBAAmB;AAEzD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,+BAA+B,KAAK;AAClD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,sBAAsB,OAAO,MAAM;AAC1C,MAAI;AACF,UAAM,mBAAmB,EAAE,IAAI,kBAAkB;AACjD,UAAM,SAAS,MAAM,iBAAiB,iBAAiB;AAEvD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS,OAAO;AAAA,MAChB,MAAM,OAAO;AAAA,MACb,SAAS,OAAO;AAAA,IAClB,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,oBAAoB,KAAK;AACvC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,0BAA0B,OAAO,MAAM;AAC9C,MAAI;AACF,UAAM,mBAAmB,EAAE,IAAI,kBAAkB;AACjD,UAAM,SAAS,MAAM,iBAAiB,iBAAiB;AAEvD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS,OAAO;AAAA,MAChB,MAAM;AAAA,QACJ,mBAAmB,OAAO;AAAA,QAC1B,kBAAkB,OAAO;AAAA,QACzB,QAAQ,OAAO;AAAA,MACjB;AAAA,MACA,SAAS,WAAW,OAAO,oCAAoC,OAAO;AAAA,IACxE,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,6BAA6B,KAAK;AAChD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,oBAAoB,OAAO,MAAM;AACxC,MAAI;AACF,UAAM,mBAAmB,EAAE,IAAI,kBAAkB;AACjD,UAAM,UAAU,MAAM,iBAAiB,aAAa;AAEpD,WAAO,EAAE,KAAkB;AAAA,MACzB;AAAA,MACA,SAAS,UAAU,kCAAkC;AAAA,IACvD,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,qBAAqB,KAAK;AACxC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAKD,IAAI,KAAK,qBAAqB,OAAO,MAAM;AACzC,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,SAAS,UAAU,CAAC,EAAE,IAAI;AAElC,QAAI,CAAC,SAAS;AACZ,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,wBAAwB,EAAE,IAAI,uBAAuB;AAC3D,UAAM,SAAS,MAAM,sBAAsB,gBAAgB,SAAS;AAAA,MAClE,kBAAkB,QAAQ,qBAAqB;AAAA,MAC/C,gBAAgB,QAAQ,mBAAmB;AAAA,MAC3C,iBAAiB,QAAQ;AAAA,MACzB,QAAQ,QAAQ,UAAU;AAAA,IAC5B,CAAC;AAED,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS,OAAO;AAAA,MAChB,MAAM;AAAA,QACJ,UAAU,OAAO;AAAA,QACjB,SAAS,OAAO;AAAA,QAChB,QAAQ,OAAO;AAAA,MACjB;AAAA,MACA,SAAS,OAAO,UACZ,4BAAQ,OAAO,iDAAmB,OAAO,qCACzC;AAAA,IACN,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,kBAAkB,OAAO,MAAM;AACtC,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,aAAa,UAAU,CAAC,EAAE,IAAI;AAEtC,QAAI,CAAC,aAAa;AAChB,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,wBAAwB,EAAE,IAAI,uBAAuB;AAC3D,UAAM,SAAS,MAAM,sBAAsB,sBAAsB,aAAa;AAAA,MAC5E,kBAAkB,QAAQ,qBAAqB;AAAA,MAC/C,gBAAgB,QAAQ,mBAAmB;AAAA,MAC3C,iBAAiB,QAAQ;AAAA,IAC3B,CAAC;AAED,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS,OAAO;AAAA,MAChB,MAAM;AAAA,QACJ,UAAU,OAAO;AAAA,QACjB,SAAS,OAAO;AAAA,QAChB,QAAQ,OAAO;AAAA,MACjB;AAAA,MACA,SAAS,OAAO,UACZ,4BAAQ,OAAO,iDAAmB,OAAO,qCACzC;AAAA,IACN,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,kBAAkB,OAAO,MAAM;AACrC,MAAI;AACF,UAAM,wBAAwB,EAAE,IAAI,uBAAuB;AAC3D,UAAM,aAAa,MAAM,sBAAsB,qBAAqB;AAGpE,MAAE,OAAO,gBAAgB,kBAAkB;AAC3C,MAAE,OAAO,uBAAuB,gDAAgD;AAEhF,WAAO,EAAE,KAAK,UAAU;AAAA,EAC1B,SAAS,OAAP;AACA,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,oBAAoB,OAAO,MAAM;AACvC,MAAI;AACF,UAAM,wBAAwB,EAAE,IAAI,uBAAuB;AAC3D,UAAM,eAAe,MAAM,sBAAsB,uBAAuB;AAGxE,MAAE,OAAO,gBAAgB,WAAW;AACpC,MAAE,OAAO,uBAAuB,gDAAgD;AAEhF,WAAO,EAAE,KAAK,YAAY;AAAA,EAC5B,SAAS,OAAP;AACA,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,iBAAiB,OAAO,MAAM;AACpC,MAAI;AACF,UAAM,wBAAwB,EAAE,IAAI,uBAAuB;AAC3D,UAAM,QAAQ,MAAM,sBAAsB,eAAe;AAEzD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,uBAAuB,KAAK;AAC1C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAKD,IAAI,KAAK,eAAe,OAAO,MAAM;AACnC,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,UAAU;AAAA,MACd,+BAA+B,KAAK,kCAAkC;AAAA,MACtE,0BAA0B,KAAK,6BAA6B;AAAA,MAC5D,6BAA6B,KAAK,gCAAgC;AAAA,MAClE,sBAAsB,KAAK,yBAAyB;AAAA,MACpD,2BAA2B,KAAK,8BAA8B;AAAA,MAC9D,qBAAqB,KAAK,uBAAuB;AAAA,MACjD,WAAW,KAAK,aAAa;AAAA,IAC/B;AAEA,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,SAAS,MAAM,UAAU,iBAAiB,OAAO;AAEvD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,iDAAc,OAAO,WAAW,2DAA6B,OAAO,WAAW;AAAA,IAC1F,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,qBAAqB,KAAK;AACxC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,aAAa,OAAO,MAAM;AACjC,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,gBAAgB,oBAAoB,IAAI;AAEhD,QAAI,CAAC,kBAAkB,CAAC,qBAAqB;AAC3C,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,SAAS,MAAM,UAAU,mBAAmB,gBAAgB,mBAAmB;AAErF,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS,4BAAQ,OAAO,iCAAkB,OAAO,OAAO,SAAS,IAAI,SAAI,OAAO,OAAO,8BAAe;AAAA,IACxG,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,mBAAmB,KAAK;AACtC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,cAAc,OAAO,MAAM;AACjC,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,SAAS,UAAU,YAAY;AAErC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,cAAc,OAAO,MAAM;AACjC,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,YAAY,EAAE,IAAI,WAAW;AAEnC,cAAU,eAAe,IAAI;AAE7B,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAKD,IAAI,IAAI,wBAAwB,OAAO,MAAM;AAC3C,MAAI;AACF,UAAM,oBAAoB,EAAE,IAAI,mBAAmB;AACnD,UAAM,QAAQ,MAAM,kBAAkB,iBAAiB;AAEvD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,kCAAkC,KAAK;AACrD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,yBAAyB,OAAO,MAAM;AAC7C,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,OAAO,YAAY;AAC3C,UAAM,oBAAoB,EAAE,IAAI,mBAAmB;AAEnD,UAAM,kBAAkB,eAAe,SAAS;AAEhD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,sBAAsB,OAAO,MAAM;AAC1C,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,MAAM,IAAI;AAElB,QAAI,CAAC,OAAO;AACV,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,oBAAoB,EAAE,IAAI,mBAAmB;AACnD,UAAM,kBAAkB,aAAa,KAAK;AAE1C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,sBAAsB,OAAO,MAAM;AACzC,MAAI;AACF,UAAM,oBAAoB,EAAE,IAAI,mBAAmB;AACnD,UAAM,QAAQ,MAAM,kBAAkB,YAAY;AAGlD,MAAE,OAAO,gBAAgB,kBAAkB;AAC3C,MAAE,OAAO,uBAAuB,iDAAiD;AAEjF,WAAO,EAAE,KAAK,KAAK,UAAU,OAAO,MAAM,CAAC,CAAC;AAAA,EAC9C,SAAS,OAAP;AACA,YAAQ,MAAM,4BAA4B,KAAK;AAC/C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,qBAAqB,OAAO,MAAM;AACzC,MAAI;AACF,UAAM,oBAAoB,EAAE,IAAI,mBAAmB;AACnD,UAAM,kBAAkB,WAAW;AAEnC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,uBAAuB,OAAO,MAAM;AAC3C,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,aAAa,KAAK,cAAc;AAEtC,UAAM,oBAAoB,EAAE,IAAI,mBAAmB;AACnD,UAAM,kBAAkB,gBAAgB,UAAU;AAElD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,SAAS,oCAAoC;AAAA,IAC/C,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,6BAA6B,KAAK;AAChD,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,WAAW,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,SAAS,MAAM,UAAU,UAAU;AAGzC,UAAM,eAAe;AAAA,MACnB,UAAU,OAAO;AAAA,MACjB,iBAAiB,OAAO;AAAA,MACxB,UAAU,OAAO;AAAA,MACjB,OAAO,OAAO;AAAA,IAChB;AAEA,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,qBAAqB,KAAK;AACxC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,WAAW,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,QAAQ,EAAE,IAAI,MAAM,GAAG,KAAK;AAElC,QAAI,CAAC,MAAM,KAAK,GAAG;AACjB,aAAO,EAAE,KAAkB;AAAA,QACzB,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,YAAY,MAAM,UAAU,aAAa;AAG/C,UAAM,oBAAoB,UAAU;AAAA,MAAO,cACzC,SAAS,MAAM,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,KACzD,SAAS,aAAa,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,KAChE,SAAS,WAAW,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,KAC9D,SAAS,MAAM,KAAK,SAAO,IAAI,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,CAAC;AAAA,IAC5E;AAGA,UAAM,QAAQ,MAAM,UAAU,SAAS;AACvC,UAAM,YAAY,KAAK,KAAK,MAAM,YAAY,KAAK,KAAK,KAAK;AAC7D,UAAM,UAAU,YAAY,KAAK;AAEjC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,QACJ;AAAA,QACA,SAAS;AAAA,QACT,OAAO,kBAAkB;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,iBAAiB,KAAK;AACpC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,WAAW,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,SAAS,MAAM,UAAU,OAAO;AAEtC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,SAAS,OAAP;AACA,YAAQ,MAAM,iBAAiB,KAAK;AACpC,WAAO,EAAE,KAAkB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,WAAW,CAAC,MAAM;AACxB,SAAO,EAAE,KAAkB;AAAA,IACzB,SAAS;AAAA,IACT,MAAM;AAAA,MACJ,QAAQ;AAAA,MACR,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH,CAAC;AAGD,IAAI,IAAI,SAAS,CAAC,MAAM;AACtB,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuiBb,SAAO,EAAE,KAAK,IAAI;AACpB,CAAC;;;AC/iDM,SAAS,SAAS,EAAE,WAAW,YAAY,QAAQ,aAAa,gBAAgB,GAA0B;AAE/G,MAAI,oBAAoB;AAExB,MAAI,aAAa;AACf,wBAAoB,UAAU;AAAA,MAAO,cACnC,SAAS,MAAM,YAAY,EAAE,SAAS,YAAY,YAAY,CAAC,KAC/D,SAAS,aAAa,YAAY,EAAE,SAAS,YAAY,YAAY,CAAC,KACtE,SAAS,WAAW,YAAY,EAAE,SAAS,YAAY,YAAY,CAAC,KACpE,SAAS,MAAM,KAAK,SAAO,IAAI,YAAY,EAAE,SAAS,YAAY,YAAY,CAAC,CAAC;AAAA,IAClF;AAAA,EACF;AAEA,MAAI,iBAAiB;AACnB,wBAAoB,kBAAkB,OAAO,cAAY,SAAS,aAAa,gBAAgB,EAAE;AAAA,EACnG;AAGA,QAAM,sBAAsB,WAAW,IAAI,eAAa;AAAA,IACtD;AAAA,IACA,WAAW,kBAAkB,OAAO,cAAY,SAAS,aAAa,SAAS,EAAE;AAAA,EACnF,EAAE,EAAE,OAAO,WAAS,MAAM,UAAU,SAAS,CAAC;AAG9C,QAAM,mBAAmB,CAAC,GAAG,iBAAiB,EAC3C,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU,EAC1C,MAAM,GAAG,CAAC;AAGb,QAAM,kBAAkB,CAAC,GAAG,iBAAiB,EAC1C,KAAK,CAAC,GAAG,MAAM,EAAE,YAAY,EAAE,SAAS,EACxC,MAAM,GAAG,CAAC;AAEb,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOK,OAAO;AAAA;AAAA,qCAEgB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAUnB,eAAe;AAAA;AAAA;AAAA;AAAA,oEAI4B,cAAc,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAYpD,UAAU;AAAA;AAAA;AAAA;AAAA,0CAIV,WAAW;AAAA;AAAA;AAAA;AAAA,0CAIX,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iDAmBX,CAAC,kBAAkB,WAAW;AAAA;AAAA;AAAA,+CAGhC,UAAU;AAAA;AAAA,gBAEzC,WAAW,IAAI,cAAY;AAAA,sCACL,SAAS,4BAA4B,iBAAiB,OAAO,SAAS,KAAK,WAAW;AAAA,gDAC5E,SAAS,QAAQ;AAAA,gDACjB,SAAS;AAAA,iDACR,UAAU,OAAO,OAAK,EAAE,aAAa,SAAS,EAAE,EAAE;AAAA;AAAA,eAEpF,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMV,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,wDAKkB,kBAAkB;AAAA;AAAA;AAAA,oBAG5C,kBAAkB,IAAI,cAAY,mBAAmB,UAAU,UAAU,CAAC,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA,gBAGvF,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,kDAKgB,gBAAgB,QAAQ;AAAA,sBACpD,gBAAgB;AAAA;AAAA,oDAEc,gBAAgB,eAAe;AAAA;AAAA;AAAA,oBAG/D,kBAAkB,IAAI,cAAY,mBAAmB,UAAU,UAAU,CAAC,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA,gBAGvF;AAAA;AAAA,gBAEA,iBAAiB,SAAS,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAOxB,iBAAiB,IAAI,cAAY,mBAAmB,UAAU,YAAY,IAAI,CAAC,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA,kBAG5F;AAAA;AAAA;AAAA,gBAGF,gBAAgB,SAAS,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAOvB,gBAAgB,IAAI,cAAY,mBAAmB,UAAU,UAAU,CAAC,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA,kBAGrF;AAAA;AAAA;AAAA,gBAGF,oBAAoB,IAAI,WAAS;AAAA;AAAA;AAAA;AAAA,oDAIG,MAAM,SAAS,QAAQ;AAAA,wBACnD,MAAM,SAAS;AAAA;AAAA,0CAEG,MAAM,SAAS;AAAA,iDAC5B,MAAM,UAAU;AAAA;AAAA;AAAA;AAAA,sBAIvB,MAAM,UAAU,MAAM,GAAG,CAAC,EAAE,IAAI,cAAY,mBAAmB,UAAU,UAAU,CAAC,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA,eAGpG,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA,cAIV,kBAAkB,WAAW,IAAI;AAAA;AAAA;AAAA;AAAA,qBAI1B,cAAc,6EAAiB;AAAA,kBAClC,CAAC,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,oBAKb;AAAA;AAAA,gBAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAurBhB;AA33BgB;AA83BhB,SAAS,mBAAmB,UAAoB,YAAwB,cAAc,OAAe;AACnG,QAAM,WAAW,WAAW,KAAK,OAAK,EAAE,OAAO,SAAS,QAAQ;AAEhE,SAAO;AAAA,gCACuB,cAAc,YAAY,yBAAyB,SAAS;AAAA,iBAC3E,SAAS,qDAAqD,SAAS;AAAA;AAAA,YAE5E,SAAS,OAAO;AAAA,wBACJ,SAAS;AAAA,cACnB;AAAA;AAAA;AAAA;AAAA,yCAI2B,SAAS;AAAA,sCACZ,IAAI,IAAI,SAAS,GAAG,EAAE;AAAA;AAAA,YAEhD,cAAc;AAAA;AAAA,cAEZ;AAAA;AAAA;AAAA,UAGJ,SAAS,YAAY;AAAA,4CACa,SAAS;AAAA,YACzC;AAAA;AAAA;AAAA;AAAA,0CAI8B,UAAU,QAAQ;AAAA,0CAClB,UAAU,QAAQ;AAAA;AAAA;AAAA,wCAGpB,SAAS;AAAA;AAAA;AAAA;AAAA,UAIvC,SAAS,QAAQ,SAAS,KAAK,SAAS,IAAI;AAAA;AAAA,cAExC,SAAS,KAAK,MAAM,GAAG,CAAC,EAAE,IAAI,SAAO;AAAA,kCACjB;AAAA,aACrB,EAAE,KAAK,EAAE;AAAA,cACR,SAAS,KAAK,SAAS,IAAI;AAAA,wCACD,SAAS,KAAK,SAAS;AAAA,gBAC/C;AAAA;AAAA,YAEJ;AAAA;AAAA;AAAA;AAIZ;AAhDS;;;AC/3BF,IAAM,aAAa,wBAAC,EAAE,WAAW,YAAY,OAAO,OAAO,MAA+B;AAC/F,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAgFK,OAAO,SAAS,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAMxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAkBE,WAAW,IAAI,cAAY;AAAA,mCACV,SAAS,OAAO,SAAS;AAAA,iBAC3C,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAiBR,UAAU,IAAI,cAAY;AAC1B,UAAM,WAAW,WAAW,KAAK,SAAO,IAAI,OAAO,SAAS,QAAQ;AACpE,WAAO;AAAA,4CACmB,SAAS,sBAAsB,SAAS;AAAA;AAAA;AAAA,4BAGxD,SAAS,OAAO,aAAa,SAAS,gDAAgD;AAAA;AAAA,0DAExD,SAAS;AAAA,yDACV,SAAS,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,mCAK5C,SAAS;AAAA,4BAChB,SAAS;AAAA;AAAA;AAAA;AAAA,uDAIkB,UAAU,QAAQ;AAAA;AAAA,4BAE7C,SAAS;AAAA;AAAA;AAAA,2DAGsB,SAAS;AAAA,6DACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD,CAAC,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAaZ,WAAW,IAAI,cAAY;AAAA,6DACoB,SAAS;AAAA;AAAA;AAAA,sBAGhD,SAAS,OAAO,+BAA+B,SAAS,gBAAgB;AAAA,0BACpE,SAAS;AAAA;AAAA;AAAA,qDAGkB,SAAS;AAAA,uDACP,SAAS;AAAA;AAAA;AAAA;AAAA,kBAI9C,SAAS,cAAc,mCAAmC,SAAS,oBAAoB;AAAA;AAAA;AAAA;AAAA,sBAInF,UAAU,OAAO,OAAK,EAAE,aAAa,SAAS,EAAE,EAAE;AAAA;AAAA;AAAA,sBAGlD,MAAM,cAAc,SAAS,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA,aAI7C,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgTtB,GA/e0B;;;ACFnB,IAAM,SAAS,wBAAC,EAAE,OAAO,aAAa,SAAS,MAA2B;AAC/E,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA,iBAKQ;AAAA,4CAC2B;AAAA;AAAA;AAAA,6CAGC;AAAA,mDACM;AAAA;AAAA;AAAA,8CAGL;AAAA,oDACM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UA6O1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCV,GAhSsB;;;ACAtB,IAAMC,OAAM,IAAIC,MAAc;AAG9BD,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,cAAc,EAAE,IAAI,MAAM,QAAQ,KAAK;AAC7C,UAAM,aAAa,EAAE,IAAI,MAAM,UAAU,KAAK;AAG9C,UAAM,CAAC,WAAW,YAAY,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MACxD,UAAU,aAAa;AAAA,MACvB,UAAU,cAAc;AAAA,MACxB,UAAU,UAAU;AAAA,IACtB,CAAC;AAGD,UAAM,QAAQ,MAAM,UAAU,SAAS;AACvC,UAAM,UAAU,YAAY;AAAA,MAC1B,GAAG;AAAA,MACH,YAAY,MAAM,aAAa;AAAA,IACjC,CAAC;AAGD,QAAI,aAAa;AACf,YAAM,YAAY,WAAW,KAAK,MAAM,YAAY,WAAW,KAAK,KAAK;AACzE,YAAM,UAAU,YAAY,KAAK;AAAA,IACnC;AAGA,UAAM,kBAAkB,aAAa,WAAW,KAAK,SAAO,IAAI,OAAO,UAAU,IAAI;AAGrF,UAAM,UAAU,SAAS;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,eAAe;AAAA,MAC5B;AAAA,IACF,CAAC;AAED,QAAI,QAAQ,OAAO;AACnB,QAAI,cAAc,OAAO;AAEzB,QAAI,aAAa;AACf,cAAQ,iBAAO,iBAAiB,OAAO;AACvC,oBAAc,6BAAS;AAAA,IACzB,WAAW,iBAAiB;AAC1B,cAAQ,GAAG,gBAAgB,UAAU,OAAO;AAC5C,oBAAc,gBAAgB,eAAe,GAAG,gBAAgB;AAAA,IAClE;AAEA,UAAM,OAAO,OAAO;AAAA,MAClB;AAAA,MACA;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,SAAS,OAAP;AACA,YAAQ,MAAM,mBAAmB,KAAK;AACtC,WAAO,EAAE,KAAK,OAAO;AAAA,MACnB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;AAGDA,KAAI,IAAI,WAAW,OAAO,MAAM;AAC9B,QAAM,QAAQ,EAAE,IAAI,MAAM,GAAG,KAAK;AAClC,QAAM,YAAY,EAAE,IAAI,WAAW;AAEnC,MAAI;AACF,UAAM,CAAC,WAAW,YAAY,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MACxD,UAAU,aAAa;AAAA,MACvB,UAAU,cAAc;AAAA,MACxB,UAAU,UAAU;AAAA,IACtB,CAAC;AAGD,UAAM,oBAAoB,UAAU;AAAA,MAAO,cACzC,SAAS,MAAM,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,KACzD,SAAS,aAAa,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,KAChE,SAAS,WAAW,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,KAC9D,SAAS,MAAM,KAAK,SAAO,IAAI,YAAY,EAAE,SAAS,MAAM,YAAY,CAAC,CAAC;AAAA,IAC5E;AAGA,QAAI,OAAO;AACT,YAAM,QAAQ,MAAM,UAAU,SAAS;AACvC,YAAM,YAAY,KAAK,KAAK,MAAM,YAAY,KAAK,KAAK,KAAK;AAC7D,YAAM,UAAU,YAAY,KAAK;AAAA,IACnC;AAEA,UAAM,UAAU,SAAS;AAAA,MACvB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf,CAAC;AAED,UAAM,OAAO,OAAO;AAAA,MAClB,OAAO,iBAAO,WAAW,OAAO;AAAA,MAChC,aAAa,6BAAS;AAAA,MACtB,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,SAAS,OAAP;AACA,YAAQ,MAAM,iBAAiB,KAAK;AACpC,WAAO,EAAE,KAAK,OAAO;AAAA,MACnB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;AAGDA,KAAI,IAAI,iBAAiB,OAAO,MAAM;AACpC,QAAM,aAAa,EAAE,IAAI,MAAM,IAAI;AACnC,QAAM,YAAY,EAAE,IAAI,WAAW;AAEnC,MAAI;AACF,UAAM,CAAC,WAAW,YAAY,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MACxD,UAAU,aAAa;AAAA,MACvB,UAAU,cAAc;AAAA,MACxB,UAAU,UAAU;AAAA,IACtB,CAAC;AAED,UAAM,WAAW,WAAW,KAAK,SAAO,IAAI,OAAO,UAAU;AAC7D,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAK,OAAO;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,MACZ,CAAC,GAAG,GAAG;AAAA,IACT;AAEA,UAAM,oBAAoB,UAAU,OAAO,cAAY,SAAS,aAAa,UAAU;AAGvF,UAAM,QAAQ,MAAM,UAAU,SAAS;AACvC,UAAM,cAAc,UAAU,KAAK,MAAM,cAAc,UAAU,KAAK,KAAK;AAC3E,UAAM,UAAU,YAAY,KAAK;AAEjC,UAAM,UAAU,SAAS;AAAA,MACvB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAED,UAAM,OAAO,OAAO;AAAA,MAClB,OAAO,GAAG,SAAS,UAAU,OAAO;AAAA,MACpC,aAAa,SAAS,eAAe,GAAG,SAAS;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,SAAS,OAAP;AACA,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAK,OAAO;AAAA,MACnB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;AAGDA,KAAI,IAAI,UAAU,OAAO,MAAM;AAC7B,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AAGnC,UAAM,CAAC,WAAW,YAAY,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MAC/D,UAAU,aAAa;AAAA,MACvB,UAAU,cAAc;AAAA,MACxB,UAAU,SAAS;AAAA,MACnB,UAAU,UAAU;AAAA,IACtB,CAAC;AAGD,UAAM,UAAU,WAAW,EAAE,WAAW,YAAY,OAAO,OAAO,CAAC;AACnE,UAAM,OAAO,OAAO;AAAA,MAClB,OAAO,8BAAU,OAAO;AAAA,MACxB,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,SAAS,OAAP;AACA,YAAQ,MAAM,sBAAsB,KAAK;AACzC,WAAO,EAAE,KAAK,OAAO;AAAA,MACnB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;AAGDA,KAAI,IAAI,UAAU,OAAO,MAAM;AAC7B,QAAM,YAAY,EAAE,IAAI,WAAW;AACnC,QAAM,SAAS,MAAM,UAAU,UAAU;AAEzC,QAAM,eAAe;AAAA;AAAA;AAAA;AAAA,6BAIJ,OAAO;AAAA,yCACe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6M9C,QAAM,OAAO,OAAO;AAAA,IAClB,OAAO,kBAAQ,OAAO;AAAA,IACtB,aAAa,gBAAM,OAAO;AAAA,IAC1B,UAAU;AAAA,EACZ,CAAC;AAED,SAAO,EAAE,KAAK,IAAI;AACpB,CAAC;;;AC3aD,IAAME,OAAM,IAAIC,MAAc;AAG9BD,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AAGnC,UAAM,CAAC,WAAW,YAAY,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MAC/D,UAAU,aAAa;AAAA,MACvB,UAAU,cAAc;AAAA,MACxB,UAAU,SAAS;AAAA,MACnB,UAAU,UAAU;AAAA,IACtB,CAAC;AAED,UAAM,UAAU,WAAW,EAAE,WAAW,YAAY,OAAO,OAAO,CAAC;AACnE,UAAM,OAAO,OAAO;AAAA,MAClB,OAAO,8BAAU,OAAO;AAAA,MACxB,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,SAAS,OAAP;AACA,YAAQ,MAAM,sBAAsB,KAAK;AACzC,WAAO,EAAE,KAAK,OAAO;AAAA,MACnB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;AAGDA,KAAI,IAAI,UAAU,OAAO,MAAM;AAC7B,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,CAAC,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MACxC,UAAU,SAAS;AAAA,MACnB,UAAU,UAAU;AAAA,IACtB,CAAC;AAED,UAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAOc,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,uCAKN,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,uCAKN,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,uCAKlB,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAO3C,OAAO,QAAQ,MAAM,aAAa,EACjC,KAAK,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,EAAC,CAAC,MAAM,IAAI,CAAC,EAC1B,MAAM,GAAG,EAAE,EACX,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM;AAAA;AAAA,8CAEU;AAAA,8CACA;AAAA;AAAA,eAE/B,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOV,OAAO,QAAQ,MAAM,WAAW,EAC/B,KAAK,CAAC,CAAC,EAAC,CAAC,GAAG,CAAC,EAAC,CAAC,MAAM,IAAI,CAAC,EAC1B,MAAM,GAAG,EAAE,EACX,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM;AAAA;AAAA,8CAEO;AAAA,8CACA;AAAA;AAAA,eAE/B,EAAE,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0GpB,UAAM,OAAO,OAAO;AAAA,MAClB,OAAO,8BAAU,OAAO;AAAA,MACxB,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,SAAS,OAAP;AACA,YAAQ,MAAM,sBAAsB,KAAK;AACzC,WAAO,EAAE,KAAK,OAAO;AAAA,MACnB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;AAGDA,KAAI,IAAI,aAAa,OAAO,MAAM;AAChC,MAAI;AACF,UAAM,YAAY,EAAE,IAAI,WAAW;AACnC,UAAM,SAAS,MAAM,UAAU,UAAU;AAEzC,UAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wEAU4C,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,wFAKS,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yEAStB,OAAO,SAAS,UAAU,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uEAOxC,OAAO,SAAS,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,4EAKrB,OAAO,SAAS,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,sEAKhC,OAAO,SAAS,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+EAShB,OAAO,SAAS,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iFAOlC,OAAO,SAAS,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iFAOrC,OAAO,SAAS,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmIlH,UAAM,OAAO,OAAO;AAAA,MAClB,OAAO,8BAAU,OAAO;AAAA,MACxB,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,EAAE,KAAK,IAAI;AAAA,EACpB,SAAS,OAAP;AACA,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK,OAAO;AAAA,MACnB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACF,CAAC;;;ACxaD,IAAME,OAAM,IAAIC,MAAc;AAG9BD,KAAI,IAAI,KAAK,OAAO,CAAC;AACrBA,KAAI,IAAI,KAAK,cAAc,CAAC;AAC5BA,KAAI,IAAI,KAAK,WAAW,CAAC;AACzBA,KAAI,IAAI,KAAK,KAAK;AAAA,EAChB,QAAQ;AAAA,EACR,cAAc,CAAC,OAAO,QAAQ,OAAO,UAAU,SAAS;AAAA,EACxD,cAAc,CAAC,gBAAgB,eAAe;AAChD,CAAC,CAAC;AAGFA,KAAI,IAAI,KAAK,OAAO,GAAG,SAAS;AAC9B,QAAM,YAAY,IAAI,UAAU,EAAE,IAAI,WAAW;AACjD,IAAE,IAAI,aAAa,SAAS;AAC5B,QAAM,KAAK;AACb,CAAC;AAGDA,KAAI,IAAI,WAAW,CAAC,MAAM;AACxB,SAAO,EAAE,KAAK;AAAA,IACZ,QAAQ;AAAA,IACR,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IAClC,SAAS;AAAA,EACX,CAAC;AACH,CAAC;AAKDA,KAAI,MAAM,QAAQ,GAAS;AAC3BA,KAAI,MAAM,UAAUA,IAAW;AAE/BA,KAAI,MAAM,KAAKA,IAAU;AAGzBA,KAAI,SAAS,CAAC,MAAM;AAClB,SAAO,EAAE,KAAK,EAAE,OAAO,YAAY,GAAG,GAAG;AAC3C,CAAC;AAGDA,KAAI,QAAQ,CAAC,KAAK,MAAM;AACtB,UAAQ,MAAM,sBAAsB,GAAG;AACvC,SAAO,EAAE,KAAK;AAAA,IACZ,OAAO;AAAA,IACP,SAAS,EAAE,IAAI,gBAAgB,gBAAgB,IAAI,UAAU;AAAA,EAC/D,GAAG,GAAG;AACR,CAAC;AAED,IAAO,cAAQA;;;AChEf,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAP;AACD,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAP;AACD,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAoE;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EARS;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,iCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAlBM;AAoBN,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,CACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IAEA,cAA0B,CAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["raw", "app", "Node", "Node", "<PERSON><PERSON>", "success", "urls", "<PERSON><PERSON>", "app", "<PERSON><PERSON>", "app", "<PERSON><PERSON>", "app", "<PERSON><PERSON>"]}