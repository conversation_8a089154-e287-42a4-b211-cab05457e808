// CloudNav 2.0 - 性能优化服务
import type { Bookmark, Category, Config } from '@/types';
import { KVService } from './kv';

export interface CacheConfig {
  bookmarksTTL: number; // 书签缓存时间（秒）
  categoriesTTL: number; // 分类缓存时间（秒）
  configTTL: number; // 配置缓存时间（秒）
  statsTTL: number; // 统计缓存时间（秒）
  enableCompression: boolean; // 启用数据压缩
  enableCDN: boolean; // 启用 CDN 缓存
}

export interface PerformanceMetrics {
  responseTime: number;
  cacheHitRate: number;
  dataSize: number;
  compressionRatio: number;
  requestCount: number;
  errorRate: number;
}

export class PerformanceService {
  private kvService: KVService;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }>;
  private metrics: PerformanceMetrics;
  private cacheConfig: CacheConfig;

  constructor(kvService: KVService) {
    this.kvService = kvService;
    this.cache = new Map();
    this.metrics = {
      responseTime: 0,
      cacheHitRate: 0,
      dataSize: 0,
      compressionRatio: 0,
      requestCount: 0,
      errorRate: 0
    };
    this.cacheConfig = {
      bookmarksTTL: 300, // 5分钟
      categoriesTTL: 600, // 10分钟
      configTTL: 3600, // 1小时
      statsTTL: 60, // 1分钟
      enableCompression: true,
      enableCDN: true
    };
  }

  // 获取缓存数据
  async getCachedData<T>(key: string, fetcher: () => Promise<T>, ttl: number): Promise<T> {
    const startTime = Date.now();
    this.metrics.requestCount++;

    try {
      // 检查内存缓存
      const cached = this.cache.get(key);
      if (cached && Date.now() - cached.timestamp < cached.ttl * 1000) {
        this.updateCacheHitRate(true);
        this.updateResponseTime(Date.now() - startTime);
        return cached.data;
      }

      // 缓存未命中，获取新数据
      const data = await fetcher();
      
      // 存储到内存缓存
      this.cache.set(key, {
        data,
        timestamp: Date.now(),
        ttl
      });

      this.updateCacheHitRate(false);
      this.updateResponseTime(Date.now() - startTime);
      
      return data;
    } catch (error) {
      this.metrics.errorRate++;
      throw error;
    }
  }

  // 获取优化后的书签数据
  async getOptimizedBookmarks(): Promise<Bookmark[]> {
    return this.getCachedData(
      'bookmarks',
      () => this.kvService.getBookmarks(),
      this.cacheConfig.bookmarksTTL
    );
  }

  // 获取优化后的分类数据
  async getOptimizedCategories(): Promise<Category[]> {
    return this.getCachedData(
      'categories',
      () => this.kvService.getCategories(),
      this.cacheConfig.categoriesTTL
    );
  }

  // 获取优化后的配置数据
  async getOptimizedConfig(): Promise<Config> {
    return this.getCachedData(
      'config',
      () => this.kvService.getConfig(),
      this.cacheConfig.configTTL
    );
  }

  // 获取优化后的统计数据
  async getOptimizedStats() {
    return this.getCachedData(
      'stats',
      () => this.kvService.getStats(),
      this.cacheConfig.statsTTL
    );
  }

  // 数据压缩
  compressData(data: any): string {
    if (!this.cacheConfig.enableCompression) {
      return JSON.stringify(data);
    }

    try {
      const jsonString = JSON.stringify(data);
      // 简单的压缩：移除不必要的空格和换行
      const compressed = jsonString.replace(/\s+/g, ' ').trim();
      
      this.updateCompressionRatio(jsonString.length, compressed.length);
      return compressed;
    } catch (error) {
      console.error('Compression failed:', error);
      return JSON.stringify(data);
    }
  }

  // 数据解压缩
  decompressData(compressedData: string): any {
    try {
      return JSON.parse(compressedData);
    } catch (error) {
      console.error('Decompression failed:', error);
      return null;
    }
  }

  // 批量获取数据（优化版）
  async getBatchData(): Promise<{
    bookmarks: Bookmark[];
    categories: Category[];
    config: Config;
    stats: any;
  }> {
    const startTime = Date.now();

    try {
      // 并行获取所有数据
      const [bookmarks, categories, config, stats] = await Promise.all([
        this.getOptimizedBookmarks(),
        this.getOptimizedCategories(),
        this.getOptimizedConfig(),
        this.getOptimizedStats()
      ]);

      this.updateResponseTime(Date.now() - startTime);

      return { bookmarks, categories, config, stats };
    } catch (error) {
      this.metrics.errorRate++;
      throw error;
    }
  }

  // 预加载数据
  async preloadData(): Promise<void> {
    try {
      // 预加载常用数据到缓存
      await Promise.all([
        this.getOptimizedBookmarks(),
        this.getOptimizedCategories(),
        this.getOptimizedConfig()
      ]);
      
      console.log('Data preloaded successfully');
    } catch (error) {
      console.error('Preload failed:', error);
    }
  }

  // 清除缓存
  clearCache(pattern?: string): void {
    if (pattern) {
      // 清除匹配模式的缓存
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      // 清除所有缓存
      this.cache.clear();
    }
    
    console.log(`Cache cleared${pattern ? ` for pattern: ${pattern}` : ''}`);
  }

  // 缓存预热
  async warmupCache(): Promise<void> {
    console.log('Starting cache warmup...');
    
    try {
      await this.preloadData();
      
      // 预加载热门数据
      const bookmarks = await this.getOptimizedBookmarks();
      const categories = await this.getOptimizedCategories();
      
      // 预计算一些常用的数据组合
      const popularBookmarks = bookmarks
        .sort((a, b) => b.clickCount - a.clickCount)
        .slice(0, 10);
      
      const recentBookmarks = bookmarks
        .sort((a, b) => b.createdAt - a.createdAt)
        .slice(0, 10);
      
      // 缓存预计算的数据
      this.cache.set('popular_bookmarks', {
        data: popularBookmarks,
        timestamp: Date.now(),
        ttl: this.cacheConfig.bookmarksTTL
      });
      
      this.cache.set('recent_bookmarks', {
        data: recentBookmarks,
        timestamp: Date.now(),
        ttl: this.cacheConfig.bookmarksTTL
      });
      
      console.log('Cache warmup completed');
    } catch (error) {
      console.error('Cache warmup failed:', error);
    }
  }

  // 获取性能指标
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // 重置性能指标
  resetMetrics(): void {
    this.metrics = {
      responseTime: 0,
      cacheHitRate: 0,
      dataSize: 0,
      compressionRatio: 0,
      requestCount: 0,
      errorRate: 0
    };
  }

  // 更新缓存命中率
  private updateCacheHitRate(hit: boolean): void {
    const totalRequests = this.metrics.requestCount;
    const currentHits = this.metrics.cacheHitRate * (totalRequests - 1);
    const newHits = hit ? currentHits + 1 : currentHits;
    this.metrics.cacheHitRate = newHits / totalRequests;
  }

  // 更新响应时间
  private updateResponseTime(time: number): void {
    const count = this.metrics.requestCount;
    this.metrics.responseTime = (this.metrics.responseTime * (count - 1) + time) / count;
  }

  // 更新压缩比率
  private updateCompressionRatio(originalSize: number, compressedSize: number): void {
    this.metrics.dataSize = originalSize;
    this.metrics.compressionRatio = compressedSize / originalSize;
  }

  // 获取缓存统计
  getCacheStats(): {
    size: number;
    hitRate: number;
    entries: Array<{ key: string; age: number; ttl: number }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, value]) => ({
      key,
      age: Date.now() - value.timestamp,
      ttl: value.ttl * 1000
    }));

    return {
      size: this.cache.size,
      hitRate: this.metrics.cacheHitRate,
      entries
    };
  }

  // 清理过期缓存
  cleanupExpiredCache(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > value.ttl * 1000) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} expired cache entries`);
    }
  }

  // 更新缓存配置
  updateCacheConfig(config: Partial<CacheConfig>): void {
    this.cacheConfig = { ...this.cacheConfig, ...config };
    console.log('Cache configuration updated:', this.cacheConfig);
  }

  // 获取缓存配置
  getCacheConfig(): CacheConfig {
    return { ...this.cacheConfig };
  }

  // 生成性能报告
  generatePerformanceReport(): {
    metrics: PerformanceMetrics;
    cacheStats: any;
    recommendations: string[];
  } {
    const metrics = this.getPerformanceMetrics();
    const cacheStats = this.getCacheStats();
    const recommendations: string[] = [];

    // 生成优化建议
    if (metrics.cacheHitRate < 0.8) {
      recommendations.push('考虑增加缓存时间以提高命中率');
    }

    if (metrics.responseTime > 1000) {
      recommendations.push('响应时间较长，建议优化数据查询');
    }

    if (metrics.errorRate > 0.05) {
      recommendations.push('错误率较高，需要检查系统稳定性');
    }

    if (cacheStats.size > 1000) {
      recommendations.push('缓存条目过多，建议定期清理');
    }

    if (metrics.compressionRatio > 0.8) {
      recommendations.push('压缩效果不佳，考虑优化数据结构');
    }

    return {
      metrics,
      cacheStats,
      recommendations
    };
  }
}
