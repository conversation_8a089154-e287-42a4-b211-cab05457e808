// CloudNav 2.0 - 分类管理服务
import { nanoid } from 'nanoid';
import type { Category, Bookmark } from '@/types';
import { KVService } from './kv';

export class CategoryService {
  private kvService: KVService;

  constructor(kvService: KVService) {
    this.kvService = kvService;
  }

  // 获取所有分类
  async getAllCategories(): Promise<Category[]> {
    const categories = await this.kvService.getCategories();
    return categories.sort((a, b) => a.order - b.order);
  }

  // 根据ID获取分类
  async getCategoryById(id: string): Promise<Category | null> {
    const categories = await this.getAllCategories();
    return categories.find(category => category.id === id) || null;
  }

  // 创建新分类
  async createCategory(categoryData: Omit<Category, 'id' | 'createdAt'>): Promise<Category> {
    const categories = await this.getAllCategories();
    const maxOrder = categories.length > 0 ? Math.max(...categories.map(c => c.order)) : 0;
    
    const newCategory: Category = {
      id: nanoid(),
      ...categoryData,
      order: categoryData.order ?? maxOrder + 1,
      createdAt: Date.now()
    };

    const success = await this.kvService.addCategory(newCategory);
    if (!success) {
      throw new Error('Failed to create category');
    }

    return newCategory;
  }

  // 更新分类
  async updateCategory(id: string, updates: Partial<Omit<Category, 'id' | 'createdAt'>>): Promise<Category | null> {
    const category = await this.getCategoryById(id);
    if (!category) {
      return null;
    }

    const success = await this.kvService.updateCategory(id, updates);
    if (!success) {
      throw new Error('Failed to update category');
    }

    return { ...category, ...updates };
  }

  // 删除分类
  async deleteCategory(id: string, options: { 
    moveBookmarksTo?: string; 
    deleteBookmarks?: boolean 
  } = {}): Promise<{ success: boolean; movedBookmarks?: number; deletedBookmarks?: number }> {
    const category = await this.getCategoryById(id);
    if (!category) {
      return { success: false };
    }

    // 获取该分类下的所有书签
    const bookmarks = await this.kvService.getBookmarks();
    const categoryBookmarks = bookmarks.filter(bookmark => bookmark.category === id);

    let movedBookmarks = 0;
    let deletedBookmarks = 0;

    // 处理分类下的书签
    if (categoryBookmarks.length > 0) {
      if (options.moveBookmarksTo) {
        // 移动书签到其他分类
        for (const bookmark of categoryBookmarks) {
          const success = await this.kvService.updateBookmark(bookmark.id, {
            category: options.moveBookmarksTo,
            updatedAt: Date.now()
          });
          if (success) movedBookmarks++;
        }
      } else if (options.deleteBookmarks) {
        // 删除所有书签
        for (const bookmark of categoryBookmarks) {
          const success = await this.kvService.deleteBookmark(bookmark.id);
          if (success) deletedBookmarks++;
        }
      } else {
        // 如果没有指定处理方式，不允许删除
        throw new Error('Cannot delete category with bookmarks. Please specify how to handle existing bookmarks.');
      }
    }

    // 删除分类
    const success = await this.kvService.deleteCategory(id);
    
    return { 
      success, 
      movedBookmarks: movedBookmarks > 0 ? movedBookmarks : undefined,
      deletedBookmarks: deletedBookmarks > 0 ? deletedBookmarks : undefined
    };
  }

  // 重新排序分类
  async reorderCategories(categoryOrders: { id: string; order: number }[]): Promise<boolean> {
    try {
      for (const { id, order } of categoryOrders) {
        await this.kvService.updateCategory(id, { order });
      }
      return true;
    } catch (error) {
      console.error('Failed to reorder categories:', error);
      return false;
    }
  }

  // 获取分类统计信息
  async getCategoryStats(): Promise<Array<{
    category: Category;
    bookmarkCount: number;
    totalClicks: number;
    lastUpdated: number;
  }>> {
    const [categories, bookmarks, stats] = await Promise.all([
      this.getAllCategories(),
      this.kvService.getBookmarks(),
      this.kvService.getStats()
    ]);

    return categories.map(category => {
      const categoryBookmarks = bookmarks.filter(bookmark => bookmark.category === category.id);
      const totalClicks = categoryBookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);
      const lastUpdated = categoryBookmarks.length > 0 
        ? Math.max(...categoryBookmarks.map(bookmark => bookmark.updatedAt))
        : category.createdAt;

      return {
        category,
        bookmarkCount: categoryBookmarks.length,
        totalClicks,
        lastUpdated
      };
    });
  }

  // 验证分类数据
  validateCategoryData(data: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
      errors.push('分类名称是必需的');
    }

    if (data.order !== undefined && (typeof data.order !== 'number' || data.order < 0)) {
      errors.push('排序必须是非负数');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // 检查分类名称是否重复
  async checkDuplicateCategoryName(name: string, excludeId?: string): Promise<Category | null> {
    const categories = await this.getAllCategories();
    return categories.find(category => 
      category.name.toLowerCase() === name.toLowerCase() && category.id !== excludeId
    ) || null;
  }

  // 获取空分类（没有书签的分类）
  async getEmptyCategories(): Promise<Category[]> {
    const [categories, bookmarks] = await Promise.all([
      this.getAllCategories(),
      this.kvService.getBookmarks()
    ]);

    return categories.filter(category => 
      !bookmarks.some(bookmark => bookmark.category === category.id)
    );
  }

  // 获取热门分类
  async getPopularCategories(limit: number = 10): Promise<Array<{
    category: Category;
    bookmarkCount: number;
    totalClicks: number;
  }>> {
    const stats = await this.getCategoryStats();
    return stats
      .sort((a, b) => b.totalClicks - a.totalClicks)
      .slice(0, limit)
      .map(({ category, bookmarkCount, totalClicks }) => ({
        category,
        bookmarkCount,
        totalClicks
      }));
  }

  // 创建默认分类
  async createDefaultCategories(): Promise<Category[]> {
    const defaultCategories = [
      { name: '开发工具', icon: '🛠️', description: '开发相关的工具和资源', order: 1 },
      { name: '设计资源', icon: '🎨', description: '设计工具和素材', order: 2 },
      { name: '学习资料', icon: '📚', description: '学习和教育相关资源', order: 3 },
      { name: '娱乐休闲', icon: '🎮', description: '娱乐和休闲网站', order: 4 },
      { name: '新闻资讯', icon: '📰', description: '新闻和资讯网站', order: 5 },
      { name: '社交媒体', icon: '💬', description: '社交网络和通讯工具', order: 6 },
      { name: '购物网站', icon: '🛒', description: '电商和购物平台', order: 7 },
      { name: '其他', icon: '📁', description: '其他未分类的网站', order: 8 }
    ];

    const createdCategories: Category[] = [];

    for (const categoryData of defaultCategories) {
      try {
        // 检查是否已存在同名分类
        const existing = await this.checkDuplicateCategoryName(categoryData.name);
        if (!existing) {
          const category = await this.createCategory(categoryData);
          createdCategories.push(category);
        }
      } catch (error) {
        console.error(`Failed to create default category ${categoryData.name}:`, error);
      }
    }

    return createdCategories;
  }

  // 导出分类数据
  async exportCategories(): Promise<Category[]> {
    return await this.getAllCategories();
  }

  // 导入分类数据
  async importCategories(categories: Category[], options: { 
    overwrite?: boolean; 
    skipDuplicates?: boolean 
  } = {}): Promise<{ 
    imported: number; 
    skipped: number; 
    errors: string[] 
  }> {
    const result = { imported: 0, skipped: 0, errors: [] };
    
    for (const category of categories) {
      try {
        // 验证数据
        const validation = this.validateCategoryData(category);
        if (!validation.valid) {
          result.errors.push(`分类 "${category.name}" 验证失败: ${validation.errors.join(', ')}`);
          continue;
        }

        // 检查重复
        if (options.skipDuplicates) {
          const duplicate = await this.checkDuplicateCategoryName(category.name);
          if (duplicate) {
            result.skipped++;
            continue;
          }
        }

        // 创建分类
        await this.createCategory({
          name: category.name,
          icon: category.icon,
          description: category.description,
          order: category.order
        });

        result.imported++;
      } catch (error) {
        result.errors.push(`导入分类 "${category.name}" 失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return result;
  }

  // 合并分类
  async mergeCategories(sourceId: string, targetId: string): Promise<{ success: boolean; movedBookmarks: number }> {
    const [sourceCategory, targetCategory] = await Promise.all([
      this.getCategoryById(sourceId),
      this.getCategoryById(targetId)
    ]);

    if (!sourceCategory || !targetCategory) {
      throw new Error('Source or target category not found');
    }

    // 移动所有书签到目标分类
    const bookmarks = await this.kvService.getBookmarks();
    const sourceBookmarks = bookmarks.filter(bookmark => bookmark.category === sourceId);
    
    let movedBookmarks = 0;
    for (const bookmark of sourceBookmarks) {
      const success = await this.kvService.updateBookmark(bookmark.id, {
        category: targetId,
        updatedAt: Date.now()
      });
      if (success) movedBookmarks++;
    }

    // 删除源分类
    const deleteSuccess = await this.kvService.deleteCategory(sourceId);

    return {
      success: deleteSuccess,
      movedBookmarks
    };
  }
}
