// CloudNav 2.0 - 管理面板组件
import type { Bookmark, Category, Stats, Config } from '@/types';

interface AdminPanelProps {
  bookmarks: Bookmark[];
  categories: Category[];
  stats: Stats;
  config: Config;
}

export const AdminPanel = ({ bookmarks, categories, stats, config }: AdminPanelProps): string => {
  return `
    <div class="admin-panel">
      {/* 管理面板头部 */}
      <div class="admin-header">
        <div class="container">
          <div class="admin-nav">
            <h1>管理面板</h1>
            <div class="admin-nav-links">
              <a href="/" class="btn btn-outline">返回首页</a>
              <a href="/admin/stats" class="btn btn-outline">统计数据</a>
              <a href="/admin/settings" class="btn btn-outline">系统设置</a>
            </div>
          </div>
        </div>
      </div>

      <div class="container">
        {/* 概览卡片 */}
        <div class="overview-grid">
          <div class="overview-card">
            <div class="overview-icon">📚</div>
            <div class="overview-content">
              <h3>书签总数</h3>
              <div class="overview-number">{bookmarks.length}</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="overview-icon">📁</div>
            <div class="overview-content">
              <h3>分类总数</h3>
              <div class="overview-number">{categories.length}</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="overview-icon">👆</div>
            <div class="overview-content">
              <h3>总点击量</h3>
              <div class="overview-number">{stats.totalClicks}</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="overview-icon">👀</div>
            <div class="overview-content">
              <h3>总访问量</h3>
              <div class="overview-number">{stats.totalViews}</div>
            </div>
          </div>
        </div>

        {/* 快速操作 */}
        <div class="quick-actions">
          <h2>快速操作</h2>
          <div class="action-grid">
            <button onclick="showAddBookmarkModal()" class="action-card">
              <div class="action-icon">➕</div>
              <div class="action-title">添加书签</div>
              <div class="action-desc">添加新的书签到收藏</div>
            </button>
            
            <button onclick="showAddCategoryModal()" class="action-card">
              <div class="action-icon">📁</div>
              <div class="action-title">添加分类</div>
              <div class="action-desc">创建新的书签分类</div>
            </button>
            
            <button onclick="showImportModal()" class="action-card">
              <div class="action-icon">📥</div>
              <div class="action-title">导入书签</div>
              <div class="action-desc">从 Chrome 导入书签</div>
            </button>
            
            <button onclick="exportBookmarks()" class="action-card">
              <div class="action-icon">📤</div>
              <div class="action-title">导出书签</div>
              <div class="action-desc">导出为 Chrome 格式</div>
            </button>
            
            {config.aiConfig.enabled && (
              <button onclick="organizeWithAI()" class="action-card">
                <div class="action-icon">🤖</div>
                <div class="action-title">AI 整理</div>
                <div class="action-desc">使用 AI 智能整理书签</div>
              </button>
            )}
            
            <button onclick="backupData()" class="action-card">
              <div class="action-icon">💾</div>
              <div class="action-title">备份数据</div>
              <div class="action-desc">备份所有数据</div>
            </button>
          </div>
        </div>

        {/* 书签管理 */}
        <div class="bookmarks-section">
          <div class="section-header">
            <h2>书签管理</h2>
            <div class="section-actions">
              <input type="text" id="bookmarkSearch" placeholder="搜索书签..." class="search-input" />
              <select id="categoryFilter" class="filter-select">
                <option value="">所有分类</option>
                {categories.map(category => (
                  <option value={category.id}>{category.name}</option>
                ))}
              </select>
            </div>
          </div>
          
          <div class="bookmarks-table">
            <table>
              <thead>
                <tr>
                  <th>标题</th>
                  <th>URL</th>
                  <th>分类</th>
                  <th>点击量</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody id="bookmarksTableBody">
                {bookmarks.map(bookmark => {
                  const category = categories.find(cat => cat.id === bookmark.category);
                  return (
                    <tr data-bookmark-id={bookmark.id} data-category={bookmark.category}>
                      <td>
                        <div class="bookmark-cell">
                          {bookmark.icon && (
                            <img src={bookmark.icon} alt="" class="bookmark-icon-small" />
                          )}
                          <div>
                            <div class="bookmark-title">{bookmark.title}</div>
                            <div class="bookmark-desc">{bookmark.shortDesc}</div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <a href={bookmark.url} target="_blank" class="bookmark-url">
                          {bookmark.url}
                        </a>
                      </td>
                      <td>
                        <span class="category-badge">{category?.name || '未分类'}</span>
                      </td>
                      <td>{bookmark.clickCount}</td>
                      <td>
                        <div class="table-actions">
                          <button onclick={`editBookmark('${bookmark.id}')`} class="btn-small btn-edit">编辑</button>
                          <button onclick={`deleteBookmark('${bookmark.id}')`} class="btn-small btn-delete">删除</button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* 分类管理 */}
        <div class="categories-section">
          <div class="section-header">
            <h2>分类管理</h2>
          </div>
          
          <div class="categories-grid">
            {categories.map(category => (
              <div class="category-card" data-category-id={category.id}>
                <div class="category-header">
                  <div class="category-info">
                    {category.icon && <span class="category-icon">{category.icon}</span>}
                    <h3>{category.name}</h3>
                  </div>
                  <div class="category-actions">
                    <button onclick={`editCategory('${category.id}')`} class="btn-small btn-edit">编辑</button>
                    <button onclick={`deleteCategory('${category.id}')`} class="btn-small btn-delete">删除</button>
                  </div>
                </div>
                
                {category.description && (
                  <p class="category-description">{category.description}</p>
                )}
                
                <div class="category-stats">
                  <span class="category-count">
                    {bookmarks.filter(b => b.category === category.id).length} 个书签
                  </span>
                  <span class="category-clicks">
                    {stats.categoryStats[category.id] || 0} 次访问
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 模态框占位符 */}
      <div id="modalContainer"></div>

      {/* 样式 */}
      <style>
        .admin-panel {
          min-height: 100vh;
          background: #f8f9fa;
        }
        
        .admin-header {
          background: white;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin-bottom: 30px;
        }
        
        .admin-nav {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 0;
        }
        
        .admin-nav h1 {
          color: #1e293b;
          margin: 0;
        }
        
        .admin-nav-links {
          display: flex;
          gap: 10px;
        }
        
        .btn-outline {
          background: transparent;
          border: 2px solid #7C3AED;
          color: #7C3AED;
        }
        
        .btn-outline:hover {
          background: #7C3AED;
          color: white;
        }
        
        .overview-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-bottom: 40px;
        }
        
        .overview-card {
          background: white;
          padding: 25px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          display: flex;
          align-items: center;
        }
        
        .overview-icon {
          font-size: 2.5em;
          margin-right: 20px;
        }
        
        .overview-number {
          font-size: 2em;
          font-weight: bold;
          color: #7C3AED;
        }
        
        .quick-actions {
          margin-bottom: 40px;
        }
        
        .action-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-top: 20px;
        }
        
        .action-card {
          background: white;
          border: none;
          padding: 25px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
        }
        
        .action-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .action-icon {
          font-size: 2.5em;
          margin-bottom: 15px;
        }
        
        .action-title {
          font-size: 1.2em;
          font-weight: 600;
          margin-bottom: 8px;
          color: #1e293b;
        }
        
        .action-desc {
          color: #64748b;
          font-size: 0.9em;
        }
        
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }
        
        .section-actions {
          display: flex;
          gap: 10px;
        }
        
        .search-input, .filter-select {
          padding: 8px 12px;
          border: 2px solid #e2e8f0;
          border-radius: 6px;
          font-size: 14px;
        }
        
        .bookmarks-table {
          background: white;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .bookmarks-table table {
          width: 100%;
          border-collapse: collapse;
        }
        
        .bookmarks-table th,
        .bookmarks-table td {
          padding: 15px;
          text-align: left;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .bookmarks-table th {
          background: #f8f9fa;
          font-weight: 600;
          color: #374151;
        }
        
        .bookmark-cell {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        
        .bookmark-icon-small {
          width: 24px;
          height: 24px;
          border-radius: 4px;
        }
        
        .bookmark-title {
          font-weight: 500;
          color: #1e293b;
        }
        
        .bookmark-desc {
          font-size: 0.85em;
          color: #64748b;
        }
        
        .bookmark-url {
          color: #7C3AED;
          text-decoration: none;
          font-size: 0.9em;
        }
        
        .category-badge {
          background: #e0e7ff;
          color: #3730a3;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.8em;
        }
        
        .table-actions {
          display: flex;
          gap: 5px;
        }
        
        .btn-small {
          padding: 4px 8px;
          font-size: 0.8em;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
        
        .btn-edit {
          background: #3b82f6;
          color: white;
        }
        
        .btn-delete {
          background: #ef4444;
          color: white;
        }
        
        .categories-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
        }
        
        .category-card {
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .category-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }
        
        .category-info {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .category-info h3 {
          margin: 0;
          color: #1e293b;
        }
        
        .category-description {
          color: #64748b;
          margin: 10px 0;
        }
        
        .category-stats {
          display: flex;
          justify-content: space-between;
          font-size: 0.9em;
          color: #64748b;
        }
      </style>

      {/* JavaScript */}
      <script>
        // 搜索和过滤功能
        document.getElementById('bookmarkSearch').addEventListener('input', filterBookmarks);
        document.getElementById('categoryFilter').addEventListener('change', filterBookmarks);
        
        function filterBookmarks() {
          const searchTerm = document.getElementById('bookmarkSearch').value.toLowerCase();
          const categoryFilter = document.getElementById('categoryFilter').value;
          const rows = document.querySelectorAll('#bookmarksTableBody tr');
          
          rows.forEach(row => {
            const title = row.querySelector('.bookmark-title').textContent.toLowerCase();
            const url = row.querySelector('.bookmark-url').textContent.toLowerCase();
            const category = row.dataset.category;
            
            const matchesSearch = title.includes(searchTerm) || url.includes(searchTerm);
            const matchesCategory = !categoryFilter || category === categoryFilter;
            
            row.style.display = matchesSearch && matchesCategory ? '' : 'none';
          });
        }
        
        // 占位符函数，将在后续实现
        function showAddBookmarkModal() { alert('添加书签功能即将实现'); }
        function showAddCategoryModal() { alert('添加分类功能即将实现'); }
        function showImportModal() { alert('导入功能即将实现'); }
        function exportBookmarks() { alert('导出功能即将实现'); }
        function organizeWithAI() { alert('AI整理功能即将实现'); }
        function backupData() { alert('备份功能即将实现'); }
        function editBookmark(id) { alert('编辑书签: ' + id); }
        function deleteBookmark(id) { alert('删除书签: ' + id); }
        function editCategory(id) { alert('编辑分类: ' + id); }
        function deleteCategory(id) { alert('删除分类: ' + id); }
      </script>
    </div>
  `;
};
