// CloudNav 2.0 - 全新管理面板组件（全功能版）
import type { Bookmark, Category, Stats, Config } from '@/types';

interface AdminPanelProps {
  bookmarks: Bookmark[];
  categories: Category[];
  stats: Stats;
  config: Config;
}

export const NewAdminPanel = ({ bookmarks, categories, stats, config }: AdminPanelProps): string => {
  
  // 生成侧边栏导航
  const sidebarNav = `
    <aside class="w-64 h-screen bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-y-auto pt-16">
      <div class="p-6">
        <!-- Logo -->
        <div class="flex items-center space-x-2 mb-8">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold">C</span>
          </div>
          <span class="text-xl font-bold text-gray-900 dark:text-gray-100">管理后台</span>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="space-y-2">
          <a href="#dashboard" class="nav-item active flex items-center px-3 py-2 text-blue-600 bg-blue-50 dark:bg-blue-900/20 rounded-lg transition-colors">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
            </svg>
            仪表盘
          </a>
          
          <a href="#bookmarks" class="nav-item flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
            </svg>
            书签管理
            <span class="ml-auto text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded-full">${bookmarks.length}</span>
          </a>
          
          <a href="#categories" class="nav-item flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            分类管理
            <span class="ml-auto text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded-full">${categories.length}</span>
          </a>
          
          <a href="#import-export" class="nav-item flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
            </svg>
            导入导出
          </a>
          
          <a href="#ai-tools" class="nav-item flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            AI 工具
          </a>
          
          <a href="#statistics" class="nav-item flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            统计分析
          </a>
          
          <a href="#settings" class="nav-item flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            系统设置
          </a>
        </nav>
        
        <!-- 返回首页 -->
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <a href="/" class="flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            返回首页
          </a>
        </div>
      </div>
    </aside>
  `;

  // 生成顶部导航栏
  const topNav = `
    <header class="fixed top-0 left-0 w-full h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 z-30">
      <div class="flex items-center justify-between h-full px-6">
        <div class="flex items-center space-x-4">
          <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100" id="page-title">仪表盘</h1>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- 快速添加 -->
          <div class="relative">
            <button id="quick-add-btn" class="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              快速添加
            </button>
            <div id="quick-add-menu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg hidden z-50">
              <a href="#" onclick="showAddBookmarkModal()" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">添加书签</a>
              <a href="#" onclick="showAddCategoryModal()" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">添加分类</a>
            </div>
          </div>
          
          <!-- 主题切换 -->
          <button id="theme-toggle" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>
  `;

  return `
    <!DOCTYPE html>
    <html lang="zh-CN" class="scroll-smooth">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>管理后台 - ${config.siteName}</title>
      <script src="https://cdn.tailwindcss.com"></script>
      <script>
        tailwind.config = {
          darkMode: 'class',
          theme: {
            extend: {
              colors: {
                primary: '#7C3AED',
                secondary: '#3B82F6'
              }
            }
          }
        }
      </script>
      <style>
        .nav-item.active {
          background-color: rgb(239 246 255);
          color: rgb(37 99 235);
        }
        .dark .nav-item.active {
          background-color: rgb(30 58 138 / 0.2);
          color: rgb(96 165 250);
        }
      </style>
    </head>
    <body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      ${sidebarNav}
      ${topNav}
      
      <!-- 主内容区域 -->
      <div class="w-full flex">
        ${sidebarNav}
        <main class="flex-1 pt-16 min-h-screen">
          <div class="p-6">

          <!-- 仪表盘页面 -->
          <div id="dashboard-page" class="page-content">
            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                  <div class="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                    </svg>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">书签总数</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">${bookmarks.length}</p>
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                  <div class="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">分类总数</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">${categories.length}</p>
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                  <div class="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
                    </svg>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">总点击量</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">${stats.totalClicks || 0}</p>
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                  <div class="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">总访问量</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">${stats.totalViews || 0}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 快速操作 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">快速操作</h3>
                <div class="grid grid-cols-2 gap-4">
                  <button onclick="showAddBookmarkModal()" class="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span class="text-sm font-medium text-blue-600">添加书签</span>
                  </button>

                  <button onclick="showAddCategoryModal()" class="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                    <svg class="w-8 h-8 text-green-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <span class="text-sm font-medium text-green-600">添加分类</span>
                  </button>

                  <button onclick="showImportModal()" class="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                    <svg class="w-8 h-8 text-purple-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                    </svg>
                    <span class="text-sm font-medium text-purple-600">导入书签</span>
                  </button>

                  <button onclick="exportBookmarks()" class="flex flex-col items-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors">
                    <svg class="w-8 h-8 text-orange-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span class="text-sm font-medium text-orange-600">导出书签</span>
                  </button>
                </div>
              </div>

              <!-- 最近活动 -->
              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">最近添加的书签</h3>
                <div class="space-y-3">
                  ${bookmarks.slice(0, 5).map(bookmark => `
                    <div class="flex items-center space-x-3">
                      ${bookmark.icon ? `<img src="${bookmark.icon}" alt="" class="w-6 h-6 rounded">` : '<div class="w-6 h-6 bg-gray-300 rounded"></div>'}
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">${bookmark.title}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">${bookmark.url}</p>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>
          </div>

          <!-- 书签管理页面 -->
          <div id="bookmarks-page" class="page-content hidden">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">书签管理</h2>
              <button onclick="showAddBookmarkModal()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                添加书签
              </button>
            </div>

            <!-- 搜索和筛选 -->
            <div class="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
              <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                  <input type="text" id="bookmark-search" placeholder="搜索书签..."
                         class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                </div>
                <div class="sm:w-48">
                  <select id="bookmark-category-filter"
                          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    <option value="">所有分类</option>
                    ${categories.map(category => `
                      <option value="${category.id}">${category.name}</option>
                    `).join('')}
                  </select>
                </div>
                <button onclick="refreshBookmarks()" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                  刷新
                </button>
              </div>
            </div>

            <!-- 书签列表 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div class="overflow-x-auto">
                <table class="w-full">
                  <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">书签</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">分类</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">点击量</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">创建时间</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody id="bookmarks-table-body" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    ${bookmarks.map(bookmark => {
                      const category = categories.find(cat => cat.id === bookmark.category);
                      return `
                        <tr class="bookmark-row hover:bg-gray-50 dark:hover:bg-gray-700" data-bookmark-id="${bookmark.id}" data-category="${bookmark.category}">
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              ${bookmark.icon ? `<img src="${bookmark.icon}" alt="" class="w-8 h-8 rounded mr-3">` : '<div class="w-8 h-8 bg-gray-300 rounded mr-3"></div>'}
                              <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${bookmark.title}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">${bookmark.url}</div>
                                ${bookmark.shortDesc ? `<div class="text-xs text-gray-400 dark:text-gray-500 mt-1">${bookmark.shortDesc}</div>` : ''}
                              </div>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                              ${category ? category.name : '未分类'}
                            </span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            ${bookmark.clickCount || 0}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            ${new Date(bookmark.createdAt).toLocaleDateString()}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                              <button onclick="editBookmark('${bookmark.id}')" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                编辑
                              </button>
                              <button onclick="deleteBookmark('${bookmark.id}')" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                删除
                              </button>
                            </div>
                          </td>
                        </tr>
                      `;
                    }).join('')}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 分类管理页面 -->
          <div id="categories-page" class="page-content hidden">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">分类管理</h2>
              <button onclick="showAddCategoryModal()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                添加分类
              </button>
            </div>

            <!-- 分类网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              ${categories.map(category => {
                const categoryBookmarks = bookmarks.filter(b => b.category === category.id);
                return `
                  <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                          <span class="text-white text-lg">${category.icon || '📁'}</span>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">${category.name}</h3>
                          <p class="text-sm text-gray-500 dark:text-gray-400">${categoryBookmarks.length} 个书签</p>
                        </div>
                      </div>
                      <div class="flex space-x-2">
                        <button onclick="editCategory('${category.id}')" class="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg transition-colors">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                          </svg>
                        </button>
                        <button onclick="deleteCategory('${category.id}')" class="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg transition-colors">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                          </svg>
                        </button>
                      </div>
                    </div>

                    ${category.description ? `<p class="text-gray-600 dark:text-gray-400 text-sm mb-4">${category.description}</p>` : ''}

                    <div class="flex items-center justify-between text-sm">
                      <span class="text-gray-500 dark:text-gray-400">排序: ${category.order || 0}</span>
                      <span class="text-gray-500 dark:text-gray-400">创建: ${new Date(category.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                `;
              }).join('')}
            </div>
          </div>

          <!-- 导入导出页面 -->
          <div id="import-export-page" class="page-content hidden">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">导入导出</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- 导入区域 -->
              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">导入书签</h3>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">选择文件</label>
                    <input type="file" id="import-file" accept=".html,.json"
                           class="block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                  </div>
                  <button onclick="importBookmarks()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    开始导入
                  </button>
                </div>
              </div>

              <!-- 导出区域 -->
              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">导出书签</h3>
                <div class="space-y-4">
                  <div class="space-y-2">
                    <button onclick="exportBookmarks('html')" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                      导出为 HTML 格式
                    </button>
                    <button onclick="exportBookmarks('json')" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                      导出为 JSON 格式
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI 工具页面 -->
          <div id="ai-tools-page" class="page-content hidden">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">AI 工具</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">智能整理</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">使用 AI 自动整理和分类您的书签</p>
                <button onclick="organizeWithAI()" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                  开始 AI 整理
                </button>
              </div>

              <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">重复检测</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">检测并处理重复的书签</p>
                <button onclick="detectDuplicates()" class="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                  检测重复书签
                </button>
              </div>
            </div>
          </div>
          </div>
        </main>
      </div>

      <!-- 模态框容器 -->
      <div id="modal-container"></div>

      <!-- JavaScript -->
      <script>
        // 页面导航功能
        function initNavigation() {
          const navItems = document.querySelectorAll('.nav-item');
          const pages = document.querySelectorAll('.page-content');
          const pageTitle = document.getElementById('page-title');

          navItems.forEach(item => {
            item.addEventListener('click', (e) => {
              e.preventDefault();
              const target = item.getAttribute('href').substring(1);

              // 更新导航状态
              navItems.forEach(nav => nav.classList.remove('active'));
              item.classList.add('active');

              // 显示对应页面
              pages.forEach(page => page.classList.add('hidden'));
              const targetPage = document.getElementById(target + '-page');
              if (targetPage) {
                targetPage.classList.remove('hidden');

                // 更新页面标题
                const titles = {
                  'dashboard': '仪表盘',
                  'bookmarks': '书签管理',
                  'categories': '分类管理',
                  'import-export': '导入导出',
                  'ai-tools': 'AI 工具',
                  'statistics': '统计分析',
                  'settings': '系统设置'
                };
                pageTitle.textContent = titles[target] || '管理后台';
              }
            });
          });
        }

        // 主题切换功能
        function initTheme() {
          const themeToggle = document.getElementById('theme-toggle');
          const html = document.documentElement;

          // 检查本地存储的主题设置
          const savedTheme = localStorage.getItem('admin-theme');
          if (savedTheme) {
            html.classList.toggle('dark', savedTheme === 'dark');
          } else {
            // 默认跟随系统主题
            html.classList.toggle('dark', window.matchMedia('(prefers-color-scheme: dark)').matches);
          }

          themeToggle?.addEventListener('click', () => {
            html.classList.toggle('dark');
            localStorage.setItem('admin-theme', html.classList.contains('dark') ? 'dark' : 'light');
          });
        }

        // 快速添加菜单
        function initQuickAdd() {
          const quickAddBtn = document.getElementById('quick-add-btn');
          const quickAddMenu = document.getElementById('quick-add-menu');

          quickAddBtn?.addEventListener('click', () => {
            quickAddMenu?.classList.toggle('hidden');
          });

          // 点击外部关闭菜单
          document.addEventListener('click', (e) => {
            if (!quickAddBtn?.contains(e.target) && !quickAddMenu?.contains(e.target)) {
              quickAddMenu?.classList.add('hidden');
            }
          });
        }

        // 搜索和筛选功能
        function initSearch() {
          const searchInput = document.getElementById('bookmark-search');
          const categoryFilter = document.getElementById('bookmark-category-filter');
          const bookmarkRows = document.querySelectorAll('.bookmark-row');

          function filterBookmarks() {
            const searchTerm = searchInput?.value.toLowerCase() || '';
            const selectedCategory = categoryFilter?.value || '';

            bookmarkRows.forEach(row => {
              const title = row.querySelector('.text-sm.font-medium')?.textContent.toLowerCase() || '';
              const url = row.querySelector('.text-sm.text-gray-500')?.textContent.toLowerCase() || '';
              const category = row.getAttribute('data-category') || '';

              const matchesSearch = title.includes(searchTerm) || url.includes(searchTerm);
              const matchesCategory = !selectedCategory || category === selectedCategory;

              if (matchesSearch && matchesCategory) {
                row.style.display = '';
              } else {
                row.style.display = 'none';
              }
            });
          }

          searchInput?.addEventListener('input', filterBookmarks);
          categoryFilter?.addEventListener('change', filterBookmarks);
        }

        // 模态框功能
        function showModal(title, content) {
          const modalContainer = document.getElementById('modal-container');
          modalContainer.innerHTML = \`
            <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
              <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">\${title}</h3>
                  <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
                <div class="p-6">
                  \${content}
                </div>
              </div>
            </div>
          \`;
        }

        function closeModal() {
          document.getElementById('modal-container').innerHTML = '';
        }

        // 添加书签模态框
        function showAddBookmarkModal() {
          const categoriesOptions = ${JSON.stringify(categories.map(cat => ({ id: cat.id, name: cat.name })))};
          const content = \`
            <form id="add-bookmark-form" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标题</label>
                <input type="text" id="bookmark-title" required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL</label>
                <input type="url" id="bookmark-url" required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
                <textarea id="bookmark-description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
                <select id="bookmark-category" required
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                  \${categoriesOptions.map(cat => \`<option value="\${cat.id}">\${cat.name}</option>\`).join('')}
                </select>
              </div>
              <div class="flex space-x-3 pt-4">
                <button type="submit" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  添加书签
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors">
                  取消
                </button>
              </div>
            </form>
          \`;

          showModal('添加书签', content);

          // 绑定表单提交事件
          document.getElementById('add-bookmark-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            await addBookmark();
          });
        }

        // 添加分类模态框
        function showAddCategoryModal() {
          const content = \`
            <form id="add-category-form" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类名称</label>
                <input type="text" id="category-name" required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">图标 (Emoji)</label>
                <input type="text" id="category-icon" placeholder="📁"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
                <textarea id="category-description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"></textarea>
              </div>
              <div class="flex space-x-3 pt-4">
                <button type="submit" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                  添加分类
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors">
                  取消
                </button>
              </div>
            </form>
          \`;

          showModal('添加分类', content);

          // 绑定表单提交事件
          document.getElementById('add-category-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            await addCategory();
          });
        }

        // API 调用函数
        async function addBookmark() {
          const title = document.getElementById('bookmark-title').value;
          const url = document.getElementById('bookmark-url').value;
          const description = document.getElementById('bookmark-description').value;
          const category = document.getElementById('bookmark-category').value;

          try {
            const response = await fetch('/api/bookmarks', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ title, url, description, category })
            });

            if (response.ok) {
              closeModal();
              location.reload(); // 简单刷新页面
            } else {
              alert('添加失败，请稍后再试。');
            }
          } catch (error) {
            console.error('Add bookmark error:', error);
            alert('添加失败，请稍后再试。');
          }
        }

        async function addCategory() {
          const name = document.getElementById('category-name').value;
          const icon = document.getElementById('category-icon').value;
          const description = document.getElementById('category-description').value;

          try {
            const response = await fetch('/api/categories', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ name, icon, description })
            });

            if (response.ok) {
              closeModal();
              location.reload(); // 简单刷新页面
            } else {
              alert('添加失败，请稍后再试。');
            }
          } catch (error) {
            console.error('Add category error:', error);
            alert('添加失败，请稍后再试。');
          }
        }

        async function deleteBookmark(id) {
          if (!confirm('确定要删除这个书签吗？')) return;

          try {
            const response = await fetch(\`/api/bookmarks/\${id}\`, {
              method: 'DELETE'
            });

            if (response.ok) {
              location.reload();
            } else {
              alert('删除失败，请稍后再试。');
            }
          } catch (error) {
            console.error('Delete bookmark error:', error);
            alert('删除失败，请稍后再试。');
          }
        }

        async function deleteCategory(id) {
          if (!confirm('确定要删除这个分类吗？分类下的书签将移动到"未分类"。')) return;

          try {
            const response = await fetch(\`/api/categories/\${id}\`, {
              method: 'DELETE'
            });

            if (response.ok) {
              location.reload();
            } else {
              alert('删除失败，请稍后再试。');
            }
          } catch (error) {
            console.error('Delete category error:', error);
            alert('删除失败，请稍后再试。');
          }
        }

        async function exportBookmarks(format = 'html') {
          try {
            const response = await fetch(\`/api/export?format=\${format}\`);
            if (response.ok) {
              const blob = await response.blob();
              const url = window.URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = \`bookmarks.\${format}\`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              window.URL.revokeObjectURL(url);
            } else {
              alert('导出失败，请稍后再试。');
            }
          } catch (error) {
            console.error('Export error:', error);
            alert('导出失败，请稍后再试。');
          }
        }

        async function importBookmarks() {
          const fileInput = document.getElementById('import-file');
          const file = fileInput.files[0];

          if (!file) {
            alert('请选择要导入的文件。');
            return;
          }

          const formData = new FormData();
          formData.append('file', file);

          try {
            const response = await fetch('/api/import', {
              method: 'POST',
              body: formData
            });

            if (response.ok) {
              const result = await response.json();
              alert(\`导入成功！导入了 \${result.imported} 个书签。\`);
              location.reload();
            } else {
              alert('导入失败，请稍后再试。');
            }
          } catch (error) {
            console.error('Import error:', error);
            alert('导入失败，请稍后再试。');
          }
        }

        async function organizeWithAI() {
          if (!confirm('AI 整理可能需要一些时间，确定要继续吗？')) return;

          try {
            const response = await fetch('/api/ai/organize', {
              method: 'POST'
            });

            if (response.ok) {
              const result = await response.json();
              alert('AI 整理完成！');
              location.reload();
            } else {
              alert('AI 整理失败，请稍后再试。');
            }
          } catch (error) {
            console.error('AI organize error:', error);
            alert('AI 整理失败，请稍后再试。');
          }
        }

        // 占位符函数
        function editBookmark(id) { alert('编辑书签功能即将实现: ' + id); }
        function editCategory(id) { alert('编辑分类功能即将实现: ' + id); }
        function refreshBookmarks() { location.reload(); }
        function detectDuplicates() { alert('重复检测功能即将实现'); }
        function showImportModal() { alert('导入功能请使用导入导出页面'); }

        // 初始化所有功能
        document.addEventListener('DOMContentLoaded', () => {
          initNavigation();
          initTheme();
          initQuickAdd();
          initSearch();
        });
      </script>
    </body>
    </html>
  `;
};
