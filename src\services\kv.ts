// CloudNav 2.0 - KV 存储服务
import type { KVData, Bookmark, Category, Stats, Config } from '@/types';

export class KVService {
  private kv: KVNamespace;
  private readonly VERSION = '2.0.0';

  constructor(kv: KVNamespace) {
    this.kv = kv;
  }

  // 通用 KV 操作
  private async get<T>(key: string): Promise<T | null> {
    try {
      const data = await this.kv.get(key, 'json');
      return data as T;
    } catch (error) {
      console.error(`KV get error for key ${key}:`, error);
      return null;
    }
  }

  private async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    try {
      const kvData: KVData<T> = {
        data: value,
        lastUpdated: Date.now(),
        version: this.VERSION
      };
      
      await this.kv.put(key, JSON.stringify(kvData), {
        expirationTtl: ttl
      });
      return true;
    } catch (error) {
      console.error(`KV set error for key ${key}:`, error);
      return false;
    }
  }

  private async getWithMetadata<T>(key: string): Promise<KVData<T> | null> {
    try {
      const result = await this.kv.get(key, 'json');
      return result as KVData<T>;
    } catch (error) {
      console.error(`KV getWithMetadata error for key ${key}:`, error);
      return null;
    }
  }

  // 书签操作
  async getBookmarks(): Promise<Bookmark[]> {
    const result = await this.getWithMetadata<Bookmark[]>('cloudnav:bookmarks');
    return result?.data || [];
  }

  async setBookmarks(bookmarks: Bookmark[]): Promise<boolean> {
    return await this.set('cloudnav:bookmarks', bookmarks);
  }

  async addBookmark(bookmark: Bookmark): Promise<boolean> {
    const bookmarks = await this.getBookmarks();
    bookmarks.push(bookmark);
    return await this.setBookmarks(bookmarks);
  }

  async updateBookmark(id: string, updates: Partial<Bookmark>): Promise<boolean> {
    const bookmarks = await this.getBookmarks();
    const index = bookmarks.findIndex(b => b.id === id);
    
    if (index === -1) return false;
    
    bookmarks[index] = { ...bookmarks[index], ...updates, updatedAt: Date.now() };
    return await this.setBookmarks(bookmarks);
  }

  async deleteBookmark(id: string): Promise<boolean> {
    const bookmarks = await this.getBookmarks();
    const filtered = bookmarks.filter(b => b.id !== id);
    
    if (filtered.length === bookmarks.length) return false;
    
    return await this.setBookmarks(filtered);
  }

  // 分类操作
  async getCategories(): Promise<Category[]> {
    const result = await this.getWithMetadata<Category[]>('cloudnav:categories');
    return result?.data || [];
  }

  async setCategories(categories: Category[]): Promise<boolean> {
    return await this.set('cloudnav:categories', categories);
  }

  async addCategory(category: Category): Promise<boolean> {
    const categories = await this.getCategories();
    categories.push(category);
    return await this.setCategories(categories);
  }

  async updateCategory(id: string, updates: Partial<Category>): Promise<boolean> {
    const categories = await this.getCategories();
    const index = categories.findIndex(c => c.id === id);
    
    if (index === -1) return false;
    
    categories[index] = { ...categories[index], ...updates };
    return await this.setCategories(categories);
  }

  async deleteCategory(id: string): Promise<boolean> {
    const categories = await this.getCategories();
    const filtered = categories.filter(c => c.id !== id);
    
    if (filtered.length === categories.length) return false;
    
    return await this.setCategories(filtered);
  }

  // 统计操作
  async getStats(): Promise<Stats> {
    const result = await this.getWithMetadata<Stats>('cloudnav:stats');
    return result?.data || {
      totalClicks: 0,
      totalViews: 0,
      bookmarkStats: {},
      categoryStats: {},
      searchStats: {},
      dailyStats: {},
      deviceStats: { mobile: 0, desktop: 0, tablet: 0 },
      lastUpdated: Date.now()
    };
  }

  async updateStats(updates: Partial<Stats>): Promise<boolean> {
    const stats = await this.getStats();
    const updatedStats = { ...stats, ...updates, lastUpdated: Date.now() };
    return await this.set('cloudnav:stats', updatedStats);
  }

  async incrementBookmarkClick(bookmarkId: string): Promise<boolean> {
    const stats = await this.getStats();
    stats.totalClicks++;
    stats.bookmarkStats[bookmarkId] = (stats.bookmarkStats[bookmarkId] || 0) + 1;
    
    // 更新日统计
    const today = new Date().toISOString().split('T')[0];
    stats.dailyStats[today] = (stats.dailyStats[today] || 0) + 1;
    
    return await this.updateStats(stats);
  }

  // 配置操作
  async getConfig(): Promise<Config> {
    const result = await this.getWithMetadata<Config>('cloudnav:config');
    return result?.data || {
      siteName: 'CloudNav',
      siteDescription: '智能书签导航站',
      aiConfig: { enabled: false },
      features: { stats: true, ai: false, import: true, export: true },
      theme: { primaryColor: '#7C3AED', darkMode: false }
    };
  }

  async updateConfig(updates: Partial<Config>): Promise<boolean> {
    const config = await this.getConfig();
    const updatedConfig = { ...config, ...updates };
    return await this.set('cloudnav:config', updatedConfig);
  }

  // 数据备份和恢复
  async backup(): Promise<{ bookmarks: Bookmark[], categories: Category[], stats: Stats, config: Config }> {
    const [bookmarks, categories, stats, config] = await Promise.all([
      this.getBookmarks(),
      this.getCategories(),
      this.getStats(),
      this.getConfig()
    ]);

    return { bookmarks, categories, stats, config };
  }

  async restore(data: { bookmarks?: Bookmark[], categories?: Category[], stats?: Stats, config?: Config }): Promise<boolean> {
    try {
      const promises = [];
      
      if (data.bookmarks) promises.push(this.setBookmarks(data.bookmarks));
      if (data.categories) promises.push(this.setCategories(data.categories));
      if (data.stats) promises.push(this.updateStats(data.stats));
      if (data.config) promises.push(this.updateConfig(data.config));
      
      await Promise.all(promises);
      return true;
    } catch (error) {
      console.error('Restore error:', error);
      return false;
    }
  }

  // 清理和维护
  async cleanup(): Promise<boolean> {
    try {
      // 清理过期的统计数据（保留30天）
      const stats = await this.getStats();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      Object.keys(stats.dailyStats).forEach(date => {
        if (new Date(date) < thirtyDaysAgo) {
          delete stats.dailyStats[date];
        }
      });
      
      await this.updateStats(stats);
      return true;
    } catch (error) {
      console.error('Cleanup error:', error);
      return false;
    }
  }
}
