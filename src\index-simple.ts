// CloudNav 2.0 - 简化版本用于测试
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';

interface Env {
  CLOUDNAV_KV: any; // 简化类型定义用于测试
}

const app = new Hono<{ Bindings: Env }>();

// 中间件
app.use('*', logger());
app.use('*', cors());

// 健康检查
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// 简单的主页
app.get('/', (c) => {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CloudNav 2.0 - 测试版</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 py-4">
              <h1 class="text-2xl font-bold text-gray-900">CloudNav 2.0</h1>
              <p class="text-gray-600">基础架构测试成功！</p>
            </div>
          </header>
          
          <div class="max-w-7xl mx-auto px-4 py-8">
            <div class="bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">🎉 测试状态</h2>
              <div class="space-y-3">
                <div class="flex items-center">
                  <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                  <span>Hono 框架运行正常</span>
                </div>
                <div class="flex items-center">
                  <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                  <span>TypeScript 编译成功</span>
                </div>
                <div class="flex items-center">
                  <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                  <span>Cloudflare Workers 部署就绪</span>
                </div>
              </div>
              
              <div class="mt-6 space-x-4">
                <a href="/api/health" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                  API 健康检查
                </a>
                <a href="/admin-simple" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                  管理面板测试
                </a>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;
  
  return c.html(html);
});

// 简单的 API 健康检查
app.get('/api/health', (c) => {
  return c.json({
    success: true,
    message: 'API 运行正常',
    timestamp: new Date().toISOString(),
    kv_available: !!c.env.CLOUDNAV_KV
  });
});

// API 测试页面
app.get('/test-api', (c) => {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CloudNav 2.0 - API 测试</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 py-4">
              <h1 class="text-2xl font-bold text-gray-900">API 测试页面</h1>
            </div>
          </header>

          <div class="max-w-7xl mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📊 迁移状态</h2>
                <button onclick="testMigrationStatus()" class="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mb-4">
                  获取迁移状态
                </button>
                <pre id="migrationStatus" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">🎯 创建示例数据</h2>
                <button onclick="createSampleData()" class="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mb-4">
                  创建示例数据
                </button>
                <pre id="sampleDataResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📚 书签列表</h2>
                <button onclick="getBookmarks()" class="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 mb-4">
                  获取书签列表
                </button>
                <pre id="bookmarksList" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📁 分类列表</h2>
                <button onclick="getCategories()" class="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 mb-4">
                  获取分类列表
                </button>
                <pre id="categoriesList" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">➕ 创建新书签</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <input type="text" id="bookmarkTitle" placeholder="书签标题" class="px-3 py-2 border border-gray-300 rounded">
                <input type="url" id="bookmarkUrl" placeholder="书签URL" class="px-3 py-2 border border-gray-300 rounded">
                <input type="text" id="bookmarkCategory" placeholder="分类ID" class="px-3 py-2 border border-gray-300 rounded">
                <input type="text" id="bookmarkDesc" placeholder="描述" class="px-3 py-2 border border-gray-300 rounded">
              </div>
              <button onclick="createBookmark()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4">
                创建书签
              </button>
              <pre id="createBookmarkResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>
          </div>
        </div>

        <script>
          async function testMigrationStatus() {
            try {
              const response = await fetch('/api/migration/status');
              const data = await response.json();
              document.getElementById('migrationStatus').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('migrationStatus').textContent = 'Error: ' + error.message;
            }
          }

          async function createSampleData() {
            try {
              const response = await fetch('/api/migration/sample-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('sampleDataResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('sampleDataResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getBookmarks() {
            try {
              const response = await fetch('/api/bookmarks');
              const data = await response.json();
              document.getElementById('bookmarksList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('bookmarksList').textContent = 'Error: ' + error.message;
            }
          }

          async function getCategories() {
            try {
              const response = await fetch('/api/categories');
              const data = await response.json();
              document.getElementById('categoriesList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('categoriesList').textContent = 'Error: ' + error.message;
            }
          }

          async function createBookmark() {
            try {
              const title = document.getElementById('bookmarkTitle').value;
              const url = document.getElementById('bookmarkUrl').value;
              const category = document.getElementById('bookmarkCategory').value;
              const description = document.getElementById('bookmarkDesc').value;

              if (!title || !url || !category) {
                alert('请填写标题、URL和分类ID');
                return;
              }

              const response = await fetch('/api/bookmarks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  title,
                  url,
                  category,
                  description,
                  shortDesc: description
                })
              });
              const data = await response.json();
              document.getElementById('createBookmarkResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('createBookmarkResult').textContent = 'Error: ' + error.message;
            }
          }
        </script>
      </body>
    </html>
  `;

  return c.html(html);
});

// 简单的管理面板
app.get('/admin-simple', (c) => {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CloudNav 2.0 - 管理面板测试</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 py-4">
              <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900">管理面板测试</h1>
                <a href="/" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">返回首页</a>
              </div>
            </div>
          </header>
          
          <div class="max-w-7xl mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-3">📚 书签管理</h3>
                <p class="text-gray-600 mb-4">管理您的书签收藏</p>
                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                  即将实现
                </button>
              </div>
              
              <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-3">📁 分类管理</h3>
                <p class="text-gray-600 mb-4">创建和管理分类</p>
                <button class="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                  即将实现
                </button>
              </div>
              
              <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-3">📊 统计数据</h3>
                <p class="text-gray-600 mb-4">查看使用统计</p>
                <button class="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                  即将实现
                </button>
              </div>
              
              <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-3">📥 导入书签</h3>
                <p class="text-gray-600 mb-4">从 Chrome 导入</p>
                <button class="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700">
                  即将实现
                </button>
              </div>
              
              <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-3">🤖 AI 整理</h3>
                <p class="text-gray-600 mb-4">智能整理书签</p>
                <button class="w-full px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
                  即将实现
                </button>
              </div>
              
              <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-3">⚙️ 系统设置</h3>
                <p class="text-gray-600 mb-4">配置系统参数</p>
                <button class="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                  即将实现
                </button>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;
  
  return c.html(html);
});

// KV 测试
app.get('/test-kv', async (c) => {
  try {
    // 测试写入
    await c.env.CLOUDNAV_KV.put('test-key', JSON.stringify({
      message: 'KV 存储测试成功',
      timestamp: new Date().toISOString()
    }));
    
    // 测试读取
    const data = await c.env.CLOUDNAV_KV.get('test-key', 'json');
    
    return c.json({
      success: true,
      message: 'KV 存储测试成功',
      data: data
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'KV 存储测试失败',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// 404 处理
app.notFound((c) => {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>404 - 页面未找到</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen flex items-center justify-center">
          <div class="text-center">
            <h1 class="text-6xl font-bold text-gray-400 mb-4">404</h1>
            <p class="text-xl text-gray-600 mb-6">页面未找到</p>
            <a href="/" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              返回首页
            </a>
          </div>
        </div>
      </body>
    </html>
  `;
  
  return c.html(html);
});

export default app;
