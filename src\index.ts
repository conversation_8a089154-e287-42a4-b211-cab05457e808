// CloudNav 2.0 - Workers 入口文件
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';

import type { Env } from '@/types';
import { KVService } from '@/services/kv';

// 导入路由
import { apiRoutes } from '@/routes/api';
import { pageRoutes } from '@/routes/pages';
import { adminRoutes } from '@/routes/admin';

// 创建 Hono 应用
const app = new Hono<{ Bindings: Env }>();

// 中间件
app.use('*', logger());
app.use('*', secureHeaders());
app.use('*', prettyJSON());
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// 添加 KV 服务到上下文
app.use('*', async (c, next) => {
  c.set('kvService', new KVService(c.env.CLOUDNAV_KV));
  await next();
});

// 健康检查
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// 注册路由
app.route('/api', apiRoutes);
app.route('/admin', adminRoutes);
app.route('/', pageRoutes);

// 404 处理
app.notFound((c) => {
  return c.json({ error: 'Not Found' }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Application error:', err);
  return c.json({ 
    error: 'Internal Server Error',
    message: c.env.ENVIRONMENT === 'development' ? err.message : undefined
  }, 500);
});

export default app;
