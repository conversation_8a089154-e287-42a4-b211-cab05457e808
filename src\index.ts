// CloudNav 2.0 - Workers 入口文件
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';

import type { Env } from '@/types';
import { KVService } from '@/services/kv';

// 导入路由
import { apiRoutes } from '@/routes/api';
import { pageRoutes } from '@/routes/pages';
import { adminRoutes } from '@/routes/admin';

// 创建 Hono 应用
const app = new Hono<{ Bindings: Env }>();

// 中间件
app.use('*', logger());
app.use('*', secureHeaders());
app.use('*', prettyJSON());
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// 添加 KV 服务到上下文
app.use('*', async (c, next) => {
  const kvService = new KVService(c.env.CLOUDNAV_KV);
  c.set('kvService', kvService);
  await next();
});

// 健康检查
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// API 测试页面
app.get('/test-api', (c) => {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CloudNav 2.0 - API 测试</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 py-4">
              <h1 class="text-2xl font-bold text-gray-900">API 测试页面</h1>
              <p class="text-gray-600">测试 CloudNav 2.0 的核心 API 功能</p>
            </div>
          </header>

          <div class="max-w-7xl mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📊 迁移状态</h2>
                <button onclick="testMigrationStatus()" class="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mb-4">
                  获取迁移状态
                </button>
                <pre id="migrationStatus" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">🎯 创建示例数据</h2>
                <button onclick="createSampleData()" class="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mb-4">
                  创建示例数据
                </button>
                <pre id="sampleDataResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📚 书签列表</h2>
                <button onclick="getBookmarks()" class="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 mb-4">
                  获取书签列表
                </button>
                <pre id="bookmarksList" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📁 分类列表</h2>
                <button onclick="getCategories()" class="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 mb-4">
                  获取分类列表
                </button>
                <pre id="categoriesList" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">➕ 创建新书签</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <input type="text" id="bookmarkTitle" placeholder="书签标题" class="px-3 py-2 border border-gray-300 rounded">
                <input type="url" id="bookmarkUrl" placeholder="书签URL" class="px-3 py-2 border border-gray-300 rounded">
                <input type="text" id="bookmarkCategory" placeholder="分类ID" class="px-3 py-2 border border-gray-300 rounded">
                <input type="text" id="bookmarkDesc" placeholder="描述" class="px-3 py-2 border border-gray-300 rounded">
              </div>
              <button onclick="createBookmark()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4">
                创建书签
              </button>
              <pre id="createBookmarkResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">🧹 清理数据</h2>
              <button onclick="clearAllData()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4">
                清空所有数据
              </button>
              <pre id="clearDataResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>
          </div>
        </div>

        <script>
          async function testMigrationStatus() {
            try {
              const response = await fetch('/api/migration/status');
              const data = await response.json();
              document.getElementById('migrationStatus').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('migrationStatus').textContent = 'Error: ' + error.message;
            }
          }

          async function createSampleData() {
            try {
              const response = await fetch('/api/migration/sample-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('sampleDataResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('sampleDataResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getBookmarks() {
            try {
              const response = await fetch('/api/bookmarks');
              const data = await response.json();
              document.getElementById('bookmarksList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('bookmarksList').textContent = 'Error: ' + error.message;
            }
          }

          async function getCategories() {
            try {
              const response = await fetch('/api/categories');
              const data = await response.json();
              document.getElementById('categoriesList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('categoriesList').textContent = 'Error: ' + error.message;
            }
          }

          async function createBookmark() {
            try {
              const title = document.getElementById('bookmarkTitle').value;
              const url = document.getElementById('bookmarkUrl').value;
              const category = document.getElementById('bookmarkCategory').value;
              const description = document.getElementById('bookmarkDesc').value;

              if (!title || !url || !category) {
                alert('请填写标题、URL和分类ID');
                return;
              }

              const response = await fetch('/api/bookmarks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  title,
                  url,
                  category,
                  description,
                  shortDesc: description
                })
              });
              const data = await response.json();
              document.getElementById('createBookmarkResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('createBookmarkResult').textContent = 'Error: ' + error.message;
            }
          }

          async function clearAllData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {
              return;
            }

            try {
              const response = await fetch('/api/migration/clear', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('clearDataResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('clearDataResult').textContent = 'Error: ' + error.message;
            }
          }
        </script>
      </body>
    </html>
  `;

  return c.html(html);
});

// 注册路由（顺序很重要！）
app.route('/api', apiRoutes);
app.route('/admin', adminRoutes);
// 注意：页面路由必须放在最后，因为它会匹配所有路径
app.route('/', pageRoutes);

// 404 处理
app.notFound((c) => {
  return c.json({ error: 'Not Found' }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Application error:', err);
  return c.json({ 
    error: 'Internal Server Error',
    message: c.env.ENVIRONMENT === 'development' ? err.message : undefined
  }, 500);
});

export default app;
