// CloudNav 2.0 - 管理路由
import { Hono } from 'hono';
import type { HonoApp } from '@/types/hono';
import { KVService } from '@/services/kv';
import { NewAdminPanel } from '@/components/NewAdminPanel';
import { Layout } from '@/components/Layout';

const app = new Hono<HonoApp>();

// 管理面板主页
app.get('/', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    
    // 获取所有数据
    const [bookmarks, categories, stats, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getStats(),
      kvService.getConfig()
    ]);

    const html = NewAdminPanel({ bookmarks, categories, stats, config });
    return c.html(html);
  } catch (error) {
    console.error('Admin panel error:', error);
    return c.html(Layout({ 
      title: 'CloudNav - Admin Error',
      description: 'Admin error',
      children: '<div class="error">管理面板加载失败。</div>'
    }));
  }
});

// 统计页面
app.get('/stats', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const [stats, config] = await Promise.all([
      kvService.getStats(),
      kvService.getConfig()
    ]);

    const statsContent = `
      <div class="admin-stats">
        <h1>统计数据</h1>
        
        <div class="stats-grid">
          <div class="stat-card">
            <h3>总点击量</h3>
            <div class="stat-number">${stats.totalClicks}</div>
          </div>
          
          <div class="stat-card">
            <h3>总访问量</h3>
            <div class="stat-number">${stats.totalViews}</div>
          </div>
          
          <div class="stat-card">
            <h3>移动端访问</h3>
            <div class="stat-number">${stats.deviceStats.mobile}</div>
          </div>
          
          <div class="stat-card">
            <h3>桌面端访问</h3>
            <div class="stat-number">${stats.deviceStats.desktop}</div>
          </div>
        </div>
        
        <div class="stats-section">
          <h2>热门书签</h2>
          <div class="stats-list">
            ${Object.entries(stats.bookmarkStats)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 10)
              .map(([id, count]) => `
                <div class="stats-item">
                  <span class="stats-label">${id}</span>
                  <span class="stats-value">${count} 次点击</span>
                </div>
              `).join('')}
          </div>
        </div>
        
        <div class="stats-section">
          <h2>热门搜索</h2>
          <div class="stats-list">
            ${Object.entries(stats.searchStats)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 10)
              .map(([query, count]) => `
                <div class="stats-item">
                  <span class="stats-label">${query}</span>
                  <span class="stats-value">${count} 次搜索</span>
                </div>
              `).join('')}
          </div>
        </div>
        
        <div class="admin-actions">
          <a href="/admin" class="btn">返回管理面板</a>
          <button onclick="exportStats()" class="btn">导出统计数据</button>
        </div>
      </div>
      
      <style>
        .admin-stats {
          max-width: 1000px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin: 30px 0;
        }
        
        .stat-card {
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          text-align: center;
        }
        
        .stat-number {
          font-size: 2.5em;
          font-weight: bold;
          color: #7C3AED;
          margin-top: 10px;
        }
        
        .stats-section {
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin: 20px 0;
        }
        
        .stats-list {
          margin-top: 15px;
        }
        
        .stats-item {
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .stats-item:last-child {
          border-bottom: none;
        }
        
        .stats-label {
          font-weight: 500;
        }
        
        .stats-value {
          color: #7C3AED;
          font-weight: 600;
        }
        
        .admin-actions {
          text-align: center;
          margin-top: 30px;
        }
        
        .admin-actions .btn {
          margin: 0 10px;
        }
      </style>
      
      <script>
        function exportStats() {
          fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
              const blob = new Blob([JSON.stringify(data.data, null, 2)], {
                type: 'application/json'
              });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'cloudnav-stats-' + new Date().toISOString().split('T')[0] + '.json';
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            })
            .catch(error => {
              console.error('Export error:', error);
              alert('导出失败，请稍后再试。');
            });
        }
      </script>
    `;

    const html = Layout({ 
      title: `统计数据 - ${config.siteName}`,
      description: '查看网站统计数据',
      children: statsContent 
    });

    return c.html(html);
  } catch (error) {
    console.error('Admin stats error:', error);
    return c.html(Layout({ 
      title: 'CloudNav - Stats Error',
      description: 'Stats error',
      children: '<div class="error">统计数据加载失败。</div>'
    }));
  }
});

// 设置页面
app.get('/settings', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const config = await kvService.getConfig();

    const settingsContent = `
      <div class="admin-settings">
        <h1>系统设置</h1>
        
        <form id="settingsForm" class="settings-form">
          <div class="form-section">
            <h2>基本设置</h2>
            
            <div class="form-group">
              <label for="siteName">网站名称</label>
              <input type="text" id="siteName" name="siteName" value="${config.siteName}" required>
            </div>
            
            <div class="form-group">
              <label for="siteDescription">网站描述</label>
              <textarea id="siteDescription" name="siteDescription" rows="3" required>${config.siteDescription}</textarea>
            </div>
          </div>
          
          <div class="form-section">
            <h2>AI 设置</h2>
            
            <div class="form-group">
              <label>
                <input type="checkbox" id="aiEnabled" name="aiEnabled" ${config.aiConfig.enabled ? 'checked' : ''}>
                启用 AI 功能
              </label>
            </div>
            
            <div class="form-group">
              <label for="aiApiUrl">AI API 地址</label>
              <input type="url" id="aiApiUrl" name="aiApiUrl" value="${config.aiConfig.apiUrl || ''}" placeholder="https://api.openai.com/v1">
            </div>
            
            <div class="form-group">
              <label for="aiApiKey">AI API 密钥</label>
              <input type="password" id="aiApiKey" name="aiApiKey" value="${config.aiConfig.apiKey || ''}" placeholder="sk-...">
            </div>
            
            <div class="form-group">
              <label for="aiModel">AI 模型</label>
              <input type="text" id="aiModel" name="aiModel" value="${config.aiConfig.model || ''}" placeholder="gpt-3.5-turbo">
            </div>
          </div>
          
          <div class="form-section">
            <h2>功能设置</h2>
            
            <div class="form-group">
              <label>
                <input type="checkbox" id="statsEnabled" name="statsEnabled" ${config.features.stats ? 'checked' : ''}>
                启用统计功能
              </label>
            </div>
            
            <div class="form-group">
              <label>
                <input type="checkbox" id="importEnabled" name="importEnabled" ${config.features.import ? 'checked' : ''}>
                启用导入功能
              </label>
            </div>
            
            <div class="form-group">
              <label>
                <input type="checkbox" id="exportEnabled" name="exportEnabled" ${config.features.export ? 'checked' : ''}>
                启用导出功能
              </label>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">保存设置</button>
            <a href="/admin" class="btn">取消</a>
          </div>
        </form>
      </div>
      
      <style>
        .admin-settings {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .settings-form {
          background: white;
          padding: 30px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-section {
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .form-section:last-of-type {
          border-bottom: none;
        }
        
        .form-group {
          margin-bottom: 20px;
        }
        
        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
          color: #374151;
        }
        
        .form-group input,
        .form-group textarea {
          width: 100%;
          padding: 10px 12px;
          border: 2px solid #e2e8f0;
          border-radius: 8px;
          font-size: 16px;
          transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
          outline: none;
          border-color: #7C3AED;
        }
        
        .form-group input[type="checkbox"] {
          width: auto;
          margin-right: 8px;
        }
        
        .form-actions {
          text-align: center;
          margin-top: 30px;
        }
        
        .form-actions .btn {
          margin: 0 10px;
        }
        
        .btn-primary {
          background: #7C3AED;
          color: white;
        }
        
        .btn-primary:hover {
          background: #6D28D9;
        }
      </style>
      
      <script>
        document.getElementById('settingsForm').addEventListener('submit', async function(e) {
          e.preventDefault();
          
          const formData = new FormData(this);
          const settings = {
            siteName: formData.get('siteName'),
            siteDescription: formData.get('siteDescription'),
            aiConfig: {
              enabled: formData.has('aiEnabled'),
              apiUrl: formData.get('aiApiUrl'),
              apiKey: formData.get('aiApiKey'),
              model: formData.get('aiModel')
            },
            features: {
              stats: formData.has('statsEnabled'),
              ai: formData.has('aiEnabled'),
              import: formData.has('importEnabled'),
              export: formData.has('exportEnabled')
            }
          };
          
          try {
            const response = await fetch('/admin/api/config', {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(settings)
            });
            
            if (response.ok) {
              alert('设置保存成功！');
              window.location.href = '/admin';
            } else {
              alert('保存失败，请稍后再试。');
            }
          } catch (error) {
            console.error('Save error:', error);
            alert('保存失败，请稍后再试。');
          }
        });
      </script>
    `;

    const html = Layout({ 
      title: `系统设置 - ${config.siteName}`,
      description: '配置系统设置',
      children: settingsContent 
    });

    return c.html(html);
  } catch (error) {
    console.error('Admin settings error:', error);
    return c.html(Layout({ 
      title: 'CloudNav - Settings Error',
      description: 'Settings error',
      children: '<div class="error">设置页面加载失败。</div>'
    }));
  }
});

export { app as adminRoutes };
