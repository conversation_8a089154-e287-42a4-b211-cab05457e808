// CloudNav 2.0 - 现代化主页组件
import type { Bookmark, Category, Config } from '@/types';

interface HomePageProps {
  bookmarks: Bookmark[];
  categories: Category[];
  config: Config;
  searchQuery?: string;
  currentCategory?: Category;
}

export function HomePage({ bookmarks, categories, config, searchQuery, currentCategory }: HomePageProps): string {
  // 过滤书签
  let filteredBookmarks = bookmarks;
  
  if (searchQuery) {
    filteredBookmarks = bookmarks.filter(bookmark => 
      bookmark.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bookmark.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bookmark.shortDesc?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bookmark.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }
  
  if (currentCategory) {
    filteredBookmarks = filteredBookmarks.filter(bookmark => bookmark.category === currentCategory.id);
  }

  // 按分类分组书签
  const bookmarksByCategory = categories.map(category => ({
    category,
    bookmarks: filteredBookmarks.filter(bookmark => bookmark.category === category.id)
  })).filter(group => group.bookmarks.length > 0);

  // 热门书签（按点击量排序）
  const popularBookmarks = [...filteredBookmarks]
    .sort((a, b) => b.clickCount - a.clickCount)
    .slice(0, 6);

  // 最近添加的书签
  const recentBookmarks = [...filteredBookmarks]
    .sort((a, b) => b.createdAt - a.createdAt)
    .slice(0, 6);

  return `
    <div class="homepage">
      <!-- 现代化头部 -->
      <header class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">
            <span class="hero-icon">🌟</span>
            ${config.siteName}
          </h1>
          <p class="hero-subtitle">${config.siteDescription}</p>
          
          <!-- 增强搜索栏 -->
          <div class="search-container">
            <div class="search-wrapper">
              <span class="search-icon">🔍</span>
              <input 
                type="text" 
                id="searchInput" 
                placeholder="搜索书签、分类或标签..." 
                value="${searchQuery || ''}"
                class="search-input"
                autocomplete="off"
              />
              <button onclick="clearSearch()" class="search-clear ${searchQuery ? 'visible' : ''}" title="清除搜索">
                <span>✕</span>
              </button>
            </div>
            <button onclick="performSearch()" class="search-button">
              搜索
            </button>
          </div>
          
          <!-- 快速统计 -->
          <div class="quick-stats">
            <div class="stat-item">
              <span class="stat-number">${bookmarks.length}</span>
              <span class="stat-label">个书签</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">${categories.length}</span>
              <span class="stat-label">个分类</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">${filteredBookmarks.length}</span>
              <span class="stat-label">个结果</span>
            </div>
          </div>
        </div>
      </header>

      <!-- 主要内容区域 -->
      <main class="main-content">
        <div class="container">
          <!-- 分类导航 -->
          <nav class="category-nav">
            <div class="category-nav-header">
              <h2>📁 分类导航</h2>
              <button onclick="toggleCategoryNav()" class="category-toggle">
                <span class="toggle-icon">▼</span>
              </button>
            </div>
            <div class="category-list" id="categoryList">
              <a href="/" class="category-item ${!currentCategory ? 'active' : ''}">
                <span class="category-icon">🏠</span>
                <span class="category-name">全部书签</span>
                <span class="category-count">${bookmarks.length}</span>
              </a>
              ${categories.map(category => `
                <a href="/?category=${category.id}" class="category-item ${currentCategory?.id === category.id ? 'active' : ''}">
                  <span class="category-icon">${category.icon || '📂'}</span>
                  <span class="category-name">${category.name}</span>
                  <span class="category-count">${bookmarks.filter(b => b.category === category.id).length}</span>
                </a>
              `).join('')}
            </div>
          </nav>

          <!-- 书签内容区域 -->
          <div class="content-area">
            ${searchQuery ? `
              <!-- 搜索结果 -->
              <section class="search-results">
                <div class="section-header">
                  <h2>🔍 搜索结果</h2>
                  <p class="search-info">找到 ${filteredBookmarks.length} 个相关书签</p>
                </div>
                <div class="bookmarks-grid">
                  ${filteredBookmarks.map(bookmark => renderBookmarkCard(bookmark, categories)).join('')}
                </div>
              </section>
            ` : currentCategory ? `
              <!-- 分类书签 -->
              <section class="category-bookmarks">
                <div class="section-header">
                  <h2>
                    <span class="category-icon">${currentCategory.icon || '📂'}</span>
                    ${currentCategory.name}
                  </h2>
                  <p class="category-description">${currentCategory.description || ''}</p>
                </div>
                <div class="bookmarks-grid">
                  ${filteredBookmarks.map(bookmark => renderBookmarkCard(bookmark, categories)).join('')}
                </div>
              </section>
            ` : `
              <!-- 热门书签 -->
              ${popularBookmarks.length > 0 ? `
                <section class="popular-bookmarks">
                  <div class="section-header">
                    <h2>🔥 热门书签</h2>
                    <p class="section-subtitle">最受欢迎的书签</p>
                  </div>
                  <div class="bookmarks-grid">
                    ${popularBookmarks.map(bookmark => renderBookmarkCard(bookmark, categories, true)).join('')}
                  </div>
                </section>
              ` : ''}

              <!-- 最近添加 -->
              ${recentBookmarks.length > 0 ? `
                <section class="recent-bookmarks">
                  <div class="section-header">
                    <h2>⭐ 最近添加</h2>
                    <p class="section-subtitle">新添加的书签</p>
                  </div>
                  <div class="bookmarks-grid">
                    ${recentBookmarks.map(bookmark => renderBookmarkCard(bookmark, categories)).join('')}
                  </div>
                </section>
              ` : ''}

              <!-- 按分类显示 -->
              ${bookmarksByCategory.map(group => `
                <section class="category-section">
                  <div class="section-header">
                    <h2>
                      <span class="category-icon">${group.category.icon || '📂'}</span>
                      ${group.category.name}
                    </h2>
                    <a href="/?category=${group.category.id}" class="view-all-link">
                      查看全部 ${group.bookmarks.length} 个 →
                    </a>
                  </div>
                  <div class="bookmarks-grid">
                    ${group.bookmarks.slice(0, 6).map(bookmark => renderBookmarkCard(bookmark, categories)).join('')}
                  </div>
                </section>
              `).join('')}
            `}

            <!-- 空状态 -->
            ${filteredBookmarks.length === 0 ? `
              <div class="empty-state">
                <div class="empty-icon">📭</div>
                <h3>没有找到书签</h3>
                <p>${searchQuery ? '尝试使用不同的关键词搜索' : '还没有添加任何书签'}</p>
                ${!searchQuery ? `
                  <div class="empty-actions">
                    <a href="/admin" class="btn btn-primary">添加书签</a>
                    <a href="/api/test" class="btn btn-secondary">导入书签</a>
                  </div>
                ` : ''}
              </div>
            ` : ''}
          </div>
        </div>
      </main>

      <!-- 浮动操作按钮 -->
      <div class="fab-container">
        <button onclick="scrollToTop()" class="fab fab-scroll" title="回到顶部">
          <span>↑</span>
        </button>
        <button onclick="toggleTheme()" class="fab fab-theme" title="切换主题">
          <span>🌙</span>
        </button>
        <a href="/admin" class="fab fab-admin" title="管理面板">
          <span>⚙️</span>
        </a>
      </div>

      <!-- 现代化样式 -->
      <style>
        :root {
          --primary-color: #7C3AED;
          --primary-light: #A855F7;
          --primary-dark: #5B21B6;
          --secondary-color: #3B82F6;
          --success-color: #10B981;
          --warning-color: #F59E0B;
          --error-color: #EF4444;
          --text-primary: #1F2937;
          --text-secondary: #6B7280;
          --text-muted: #9CA3AF;
          --bg-primary: #FFFFFF;
          --bg-secondary: #F9FAFB;
          --bg-tertiary: #F3F4F6;
          --border-color: #E5E7EB;
          --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
          --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
          --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
          --radius-sm: 0.375rem;
          --radius-md: 0.5rem;
          --radius-lg: 0.75rem;
          --radius-xl: 1rem;
        }

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: var(--text-primary);
          background: var(--bg-secondary);
        }

        .homepage {
          min-height: 100vh;
        }

        /* 英雄区域 */
        .hero-section {
          background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
          color: white;
          padding: 4rem 2rem 3rem;
          text-align: center;
        }

        .hero-content {
          max-width: 800px;
          margin: 0 auto;
        }

        .hero-title {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
        }

        .hero-icon {
          font-size: 3.5rem;
        }

        .hero-subtitle {
          font-size: 1.25rem;
          opacity: 0.9;
          margin-bottom: 2rem;
        }

        /* 搜索容器 */
        .search-container {
          display: flex;
          gap: 1rem;
          max-width: 600px;
          margin: 0 auto 2rem;
        }

        .search-wrapper {
          flex: 1;
          position: relative;
          display: flex;
          align-items: center;
          background: white;
          border-radius: var(--radius-lg);
          padding: 0 1rem;
          box-shadow: var(--shadow-lg);
        }

        .search-icon {
          color: var(--text-muted);
          margin-right: 0.75rem;
        }

        .search-input {
          flex: 1;
          border: none;
          outline: none;
          padding: 1rem 0;
          font-size: 1rem;
          color: var(--text-primary);
        }

        .search-clear {
          background: none;
          border: none;
          color: var(--text-muted);
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 50%;
          opacity: 0;
          transition: all 0.2s;
        }

        .search-clear.visible {
          opacity: 1;
        }

        .search-clear:hover {
          background: var(--bg-tertiary);
          color: var(--text-primary);
        }

        .search-button {
          background: white;
          color: var(--primary-color);
          border: none;
          padding: 1rem 2rem;
          border-radius: var(--radius-lg);
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
          box-shadow: var(--shadow-lg);
        }

        .search-button:hover {
          background: var(--bg-secondary);
          transform: translateY(-1px);
        }

        /* 快速统计 */
        .quick-stats {
          display: flex;
          justify-content: center;
          gap: 2rem;
          margin-top: 1rem;
        }

        .stat-item {
          text-align: center;
        }

        .stat-number {
          display: block;
          font-size: 2rem;
          font-weight: 700;
        }

        .stat-label {
          font-size: 0.875rem;
          opacity: 0.8;
        }

        /* 主要内容 */
        .main-content {
          padding: 2rem 0;
        }

        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 2rem;
          display: grid;
          grid-template-columns: 280px 1fr;
          gap: 2rem;
        }

        /* 分类导航 */
        .category-nav {
          background: var(--bg-primary);
          border-radius: var(--radius-xl);
          padding: 1.5rem;
          box-shadow: var(--shadow-md);
          height: fit-content;
          position: sticky;
          top: 2rem;
        }

        .category-nav-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .category-nav-header h2 {
          font-size: 1.125rem;
          color: var(--text-primary);
        }

        .category-toggle {
          background: none;
          border: none;
          cursor: pointer;
          padding: 0.25rem;
          color: var(--text-muted);
        }

        .category-list {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .category-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem;
          border-radius: var(--radius-md);
          text-decoration: none;
          color: var(--text-secondary);
          transition: all 0.2s;
        }

        .category-item:hover {
          background: var(--bg-secondary);
          color: var(--text-primary);
        }

        .category-item.active {
          background: var(--primary-color);
          color: white;
        }

        .category-name {
          flex: 1;
        }

        .category-count {
          background: var(--bg-tertiary);
          color: var(--text-muted);
          padding: 0.25rem 0.5rem;
          border-radius: var(--radius-sm);
          font-size: 0.75rem;
        }

        .category-item.active .category-count {
          background: rgba(255, 255, 255, 0.2);
          color: white;
        }

        /* 内容区域 */
        .content-area {
          display: flex;
          flex-direction: column;
          gap: 3rem;
        }

        .section-header {
          margin-bottom: 1.5rem;
        }

        .section-header h2 {
          font-size: 1.5rem;
          color: var(--text-primary);
          margin-bottom: 0.5rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .section-subtitle {
          color: var(--text-secondary);
          font-size: 0.875rem;
        }

        .view-all-link {
          color: var(--primary-color);
          text-decoration: none;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .view-all-link:hover {
          text-decoration: underline;
        }

        /* 书签网格 */
        .bookmarks-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 1.5rem;
        }

        /* 空状态 */
        .empty-state {
          text-align: center;
          padding: 4rem 2rem;
          color: var(--text-secondary);
        }

        .empty-icon {
          font-size: 4rem;
          margin-bottom: 1rem;
        }

        .empty-state h3 {
          font-size: 1.5rem;
          margin-bottom: 0.5rem;
          color: var(--text-primary);
        }

        .empty-actions {
          margin-top: 2rem;
          display: flex;
          gap: 1rem;
          justify-content: center;
        }

        .btn {
          padding: 0.75rem 1.5rem;
          border-radius: var(--radius-md);
          text-decoration: none;
          font-weight: 500;
          transition: all 0.2s;
        }

        .btn-primary {
          background: var(--primary-color);
          color: white;
        }

        .btn-primary:hover {
          background: var(--primary-dark);
        }

        .btn-secondary {
          background: var(--bg-primary);
          color: var(--text-primary);
          border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
          background: var(--bg-secondary);
        }

        /* 浮动操作按钮 */
        .fab-container {
          position: fixed;
          bottom: 2rem;
          right: 2rem;
          display: flex;
          flex-direction: column;
          gap: 1rem;
          z-index: 1000;
        }

        .fab {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          border: none;
          background: var(--primary-color);
          color: white;
          font-size: 1.25rem;
          cursor: pointer;
          box-shadow: var(--shadow-lg);
          transition: all 0.2s;
          text-decoration: none;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .fab:hover {
          background: var(--primary-dark);
          transform: translateY(-2px);
          box-shadow: var(--shadow-xl);
        }

        /* 书签卡片样式 */
        .bookmark-card {
          background: var(--bg-primary);
          border-radius: var(--radius-lg);
          box-shadow: var(--shadow-md);
          transition: all 0.2s;
          overflow: hidden;
          position: relative;
        }

        .bookmark-card:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-lg);
        }

        .bookmark-card.popular {
          border: 2px solid var(--warning-color);
        }

        .bookmark-link {
          display: block;
          text-decoration: none;
          color: inherit;
          padding: 1.5rem;
        }

        .bookmark-header {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          margin-bottom: 1rem;
          position: relative;
        }

        .bookmark-icon {
          width: 48px;
          height: 48px;
          border-radius: var(--radius-md);
          object-fit: cover;
          flex-shrink: 0;
        }

        .bookmark-icon-placeholder {
          width: 48px;
          height: 48px;
          background: var(--bg-tertiary);
          border-radius: var(--radius-md);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          flex-shrink: 0;
        }

        .bookmark-meta {
          flex: 1;
          min-width: 0;
        }

        .bookmark-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 0.25rem;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .bookmark-url {
          color: var(--text-muted);
          font-size: 0.875rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .popular-badge {
          position: absolute;
          top: -0.5rem;
          right: -0.5rem;
          background: var(--warning-color);
          color: white;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1rem;
          box-shadow: var(--shadow-md);
        }

        .bookmark-description {
          color: var(--text-secondary);
          font-size: 0.875rem;
          line-height: 1.5;
          margin-bottom: 1rem;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .bookmark-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .bookmark-category {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: var(--bg-secondary);
          padding: 0.25rem 0.75rem;
          border-radius: var(--radius-sm);
          font-size: 0.75rem;
          color: var(--text-secondary);
        }

        .bookmark-stats {
          font-size: 0.75rem;
          color: var(--text-muted);
        }

        .bookmark-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
        }

        .tag {
          background: var(--primary-color);
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: var(--radius-sm);
          font-size: 0.75rem;
          font-weight: 500;
        }

        .tag-more {
          background: var(--bg-tertiary);
          color: var(--text-muted);
          padding: 0.25rem 0.5rem;
          border-radius: var(--radius-sm);
          font-size: 0.75rem;
        }

        /* 暗色主题 */
        .dark-theme {
          --text-primary: #F9FAFB;
          --text-secondary: #D1D5DB;
          --text-muted: #9CA3AF;
          --bg-primary: #1F2937;
          --bg-secondary: #111827;
          --bg-tertiary: #374151;
          --border-color: #374151;
        }

        .dark-theme .hero-section {
          background: linear-gradient(135deg, #1F2937 0%, #374151 100%);
        }

        .dark-theme .search-wrapper,
        .dark-theme .search-button {
          background: var(--bg-primary);
          color: var(--text-primary);
        }

        .dark-theme .search-input {
          color: var(--text-primary);
        }

        .dark-theme .search-input::placeholder {
          color: var(--text-muted);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .hero-title {
            font-size: 2rem;
          }

          .hero-icon {
            font-size: 2.5rem;
          }

          .container {
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          .category-nav {
            position: static;
          }

          .search-container {
            flex-direction: column;
          }

          .quick-stats {
            gap: 1rem;
          }

          .bookmarks-grid {
            grid-template-columns: 1fr;
          }

          .fab-container {
            bottom: 1rem;
            right: 1rem;
          }
        }
      </style>

      <!-- JavaScript -->
      <script>
        // 搜索功能
        function performSearch() {
          const query = document.getElementById('searchInput').value.trim();
          if (query) {
            window.location.href = \`/?search=\${encodeURIComponent(query)}\`;
          }
        }

        function clearSearch() {
          document.getElementById('searchInput').value = '';
          window.location.href = '/';
        }

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            performSearch();
          }
        });

        // 分类导航切换
        function toggleCategoryNav() {
          const categoryList = document.getElementById('categoryList');
          const toggleIcon = document.querySelector('.toggle-icon');
          
          if (categoryList.style.display === 'none') {
            categoryList.style.display = 'flex';
            toggleIcon.textContent = '▼';
          } else {
            categoryList.style.display = 'none';
            toggleIcon.textContent = '▶';
          }
        }

        // 回到顶部
        function scrollToTop() {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 主题切换
        function toggleTheme() {
          document.body.classList.toggle('dark-theme');
          const isDark = document.body.classList.contains('dark-theme');
          localStorage.setItem('theme', isDark ? 'dark' : 'light');
          
          // 更新图标
          const themeIcon = document.querySelector('.fab-theme span');
          themeIcon.textContent = isDark ? '☀️' : '🌙';
        }

        // 加载保存的主题
        document.addEventListener('DOMContentLoaded', function() {
          const savedTheme = localStorage.getItem('theme');
          if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            document.querySelector('.fab-theme span').textContent = '☀️';
          }
        });

        // 书签点击统计
        function trackBookmarkClick(bookmarkId) {
          fetch('/api/bookmarks/' + bookmarkId + '/click', {
            method: 'POST'
          }).catch(console.error);
        }
      </script>
    </div>
  `;
}

// 渲染书签卡片的辅助函数
function renderBookmarkCard(bookmark: Bookmark, categories: Category[], showPopular = false): string {
  const category = categories.find(c => c.id === bookmark.category);
  
  return `
    <div class="bookmark-card ${showPopular ? 'popular' : ''}" data-bookmark-id="${bookmark.id}">
      <a href="${bookmark.url}" target="_blank" onclick="trackBookmarkClick('${bookmark.id}')" class="bookmark-link">
        <div class="bookmark-header">
          ${bookmark.icon ? `
            <img src="${bookmark.icon}" alt="" class="bookmark-icon" />
          ` : `
            <div class="bookmark-icon-placeholder">🔗</div>
          `}
          <div class="bookmark-meta">
            <h3 class="bookmark-title">${bookmark.title}</h3>
            <p class="bookmark-url">${new URL(bookmark.url).hostname}</p>
          </div>
          ${showPopular ? `
            <div class="popular-badge">🔥</div>
          ` : ''}
        </div>
        
        ${bookmark.shortDesc ? `
          <p class="bookmark-description">${bookmark.shortDesc}</p>
        ` : ''}
        
        <div class="bookmark-footer">
          <div class="bookmark-category">
            <span class="category-icon">${category?.icon || '📂'}</span>
            <span class="category-name">${category?.name || '未分类'}</span>
          </div>
          <div class="bookmark-stats">
            <span class="click-count">${bookmark.clickCount} 次点击</span>
          </div>
        </div>
        
        ${bookmark.tags && bookmark.tags.length > 0 ? `
          <div class="bookmark-tags">
            ${bookmark.tags.slice(0, 3).map(tag => `
              <span class="tag">${tag}</span>
            `).join('')}
            ${bookmark.tags.length > 3 ? `
              <span class="tag-more">+${bookmark.tags.length - 3}</span>
            ` : ''}
          </div>
        ` : ''}
      </a>
    </div>
  `;
}
