// CloudNav 2.0 - 主页组件
import type { Bookmark, Category, Config } from '@/types';

interface HomePageProps {
  bookmarks: Bookmark[];
  categories: Category[];
  config: Config;
  searchQuery?: string;
  currentCategory?: Category;
}

export const HomePage = ({ 
  bookmarks, 
  categories, 
  config, 
  searchQuery,
  currentCategory 
}: HomePageProps): string => {
  
  // 生成分类导航
  const categoryNav = categories.map(category => 
    `<a href="/category/${category.id}" class="px-4 py-2 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors">
      ${category.name}
    </a>`
  ).join('');

  // 生成书签卡片
  const bookmarkCards = bookmarks.map(bookmark => 
    `<div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <div class="flex items-center mb-3">
        ${bookmark.icon ? `<img src="${bookmark.icon}" alt="${bookmark.title}" class="w-8 h-8 mr-3 rounded">` : ''}
        <h3 class="text-lg font-semibold text-gray-800">${bookmark.title}</h3>
      </div>
      <p class="text-gray-600 text-sm mb-3">${bookmark.shortDesc || bookmark.description || ''}</p>
      <a href="${bookmark.url}" target="_blank" rel="noopener noreferrer" 
         onclick="trackClick('${bookmark.id}')"
         class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
        访问
      </a>
      ${bookmark.clickCount > 0 ? `<span class="ml-3 text-sm text-gray-500">${bookmark.clickCount} 次点击</span>` : ''}
    </div>`
  ).join('');

  return `
    <div class="min-h-screen bg-gray-50">
      <!-- 头部 -->
      <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-4">
          <div class="flex items-center justify-between">
            <h1 class="text-2xl font-bold text-gray-900">${config.siteName}</h1>
            <div class="flex items-center space-x-4">
              <input type="text" 
                     placeholder="搜索书签..." 
                     value="${searchQuery || ''}"
                     class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                     onkeypress="if(event.key==='Enter') window.location.href='/search?q='+encodeURIComponent(this.value)">
              <a href="/admin" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">管理</a>
            </div>
          </div>
        </div>
      </header>

      <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- 分类导航 -->
        <div class="mb-8">
          <div class="flex flex-wrap gap-3">
            <a href="/" class="px-4 py-2 ${!currentCategory ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700'} rounded-lg hover:bg-blue-500 hover:text-white transition-colors">
              全部
            </a>
            ${categoryNav}
          </div>
        </div>

        <!-- 书签展示 -->
        ${bookmarks.length > 0 ? `
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            ${bookmarkCards}
          </div>
        ` : `
          <div class="text-center py-12">
            <div class="text-gray-400 text-6xl mb-4">📚</div>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无书签</h3>
            <p class="text-gray-500 mb-6">还没有添加任何书签，去管理页面添加一些吧！</p>
            <a href="/admin" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">添加书签</a>
          </div>
        `}
      </div>
    </div>
  `;
};
