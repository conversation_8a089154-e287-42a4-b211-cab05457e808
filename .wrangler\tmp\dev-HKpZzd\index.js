var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-iThTat/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// .wrangler/tmp/bundle-iThTat/strip-cf-connecting-ip-header.js
function stripCfConnectingIPHeader(input, init) {
  const request = new Request(input, init);
  request.headers.delete("CF-Connecting-IP");
  return request;
}
__name(stripCfConnectingIPHeader, "stripCfConnectingIPHeader");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    return Reflect.apply(target, thisArg, [
      stripCfConnectingIPHeader.apply(null, argArray)
    ]);
  }
});

// node_modules/hono/dist/compose.js
var compose = /* @__PURE__ */ __name((middleware, onError, onNotFound) => {
  return (context, next) => {
    let index = -1;
    return dispatch(0);
    async function dispatch(i) {
      if (i <= index) {
        throw new Error("next() called multiple times");
      }
      index = i;
      let res;
      let isError = false;
      let handler;
      if (middleware[i]) {
        handler = middleware[i][0][0];
        context.req.routeIndex = i;
      } else {
        handler = i === middleware.length && next || void 0;
      }
      if (handler) {
        try {
          res = await handler(context, () => dispatch(i + 1));
        } catch (err) {
          if (err instanceof Error && onError) {
            context.error = err;
            res = await onError(err, context);
            isError = true;
          } else {
            throw err;
          }
        }
      } else {
        if (context.finalized === false && onNotFound) {
          res = await onNotFound(context);
        }
      }
      if (res && (context.finalized === false || isError)) {
        context.res = res;
      }
      return context;
    }
    __name(dispatch, "dispatch");
  };
}, "compose");

// node_modules/hono/dist/request/constants.js
var GET_MATCH_RESULT = Symbol();

// node_modules/hono/dist/utils/body.js
var parseBody = /* @__PURE__ */ __name(async (request, options = /* @__PURE__ */ Object.create(null)) => {
  const { all = false, dot = false } = options;
  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;
  const contentType = headers.get("Content-Type");
  if (contentType?.startsWith("multipart/form-data") || contentType?.startsWith("application/x-www-form-urlencoded")) {
    return parseFormData(request, { all, dot });
  }
  return {};
}, "parseBody");
async function parseFormData(request, options) {
  const formData = await request.formData();
  if (formData) {
    return convertFormDataToBodyData(formData, options);
  }
  return {};
}
__name(parseFormData, "parseFormData");
function convertFormDataToBodyData(formData, options) {
  const form = /* @__PURE__ */ Object.create(null);
  formData.forEach((value, key) => {
    const shouldParseAllValues = options.all || key.endsWith("[]");
    if (!shouldParseAllValues) {
      form[key] = value;
    } else {
      handleParsingAllValues(form, key, value);
    }
  });
  if (options.dot) {
    Object.entries(form).forEach(([key, value]) => {
      const shouldParseDotValues = key.includes(".");
      if (shouldParseDotValues) {
        handleParsingNestedValues(form, key, value);
        delete form[key];
      }
    });
  }
  return form;
}
__name(convertFormDataToBodyData, "convertFormDataToBodyData");
var handleParsingAllValues = /* @__PURE__ */ __name((form, key, value) => {
  if (form[key] !== void 0) {
    if (Array.isArray(form[key])) {
      ;
      form[key].push(value);
    } else {
      form[key] = [form[key], value];
    }
  } else {
    if (!key.endsWith("[]")) {
      form[key] = value;
    } else {
      form[key] = [value];
    }
  }
}, "handleParsingAllValues");
var handleParsingNestedValues = /* @__PURE__ */ __name((form, key, value) => {
  let nestedForm = form;
  const keys = key.split(".");
  keys.forEach((key2, index) => {
    if (index === keys.length - 1) {
      nestedForm[key2] = value;
    } else {
      if (!nestedForm[key2] || typeof nestedForm[key2] !== "object" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {
        nestedForm[key2] = /* @__PURE__ */ Object.create(null);
      }
      nestedForm = nestedForm[key2];
    }
  });
}, "handleParsingNestedValues");

// node_modules/hono/dist/utils/url.js
var splitPath = /* @__PURE__ */ __name((path) => {
  const paths = path.split("/");
  if (paths[0] === "") {
    paths.shift();
  }
  return paths;
}, "splitPath");
var splitRoutingPath = /* @__PURE__ */ __name((routePath) => {
  const { groups, path } = extractGroupsFromPath(routePath);
  const paths = splitPath(path);
  return replaceGroupMarks(paths, groups);
}, "splitRoutingPath");
var extractGroupsFromPath = /* @__PURE__ */ __name((path) => {
  const groups = [];
  path = path.replace(/\{[^}]+\}/g, (match, index) => {
    const mark = `@${index}`;
    groups.push([mark, match]);
    return mark;
  });
  return { groups, path };
}, "extractGroupsFromPath");
var replaceGroupMarks = /* @__PURE__ */ __name((paths, groups) => {
  for (let i = groups.length - 1; i >= 0; i--) {
    const [mark] = groups[i];
    for (let j = paths.length - 1; j >= 0; j--) {
      if (paths[j].includes(mark)) {
        paths[j] = paths[j].replace(mark, groups[i][1]);
        break;
      }
    }
  }
  return paths;
}, "replaceGroupMarks");
var patternCache = {};
var getPattern = /* @__PURE__ */ __name((label, next) => {
  if (label === "*") {
    return "*";
  }
  const match = label.match(/^\:([^\{\}]+)(?:\{(.+)\})?$/);
  if (match) {
    const cacheKey = `${label}#${next}`;
    if (!patternCache[cacheKey]) {
      if (match[2]) {
        patternCache[cacheKey] = next && next[0] !== ":" && next[0] !== "*" ? [cacheKey, match[1], new RegExp(`^${match[2]}(?=/${next})`)] : [label, match[1], new RegExp(`^${match[2]}$`)];
      } else {
        patternCache[cacheKey] = [label, match[1], true];
      }
    }
    return patternCache[cacheKey];
  }
  return null;
}, "getPattern");
var tryDecode = /* @__PURE__ */ __name((str, decoder) => {
  try {
    return decoder(str);
  } catch {
    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {
      try {
        return decoder(match);
      } catch {
        return match;
      }
    });
  }
}, "tryDecode");
var tryDecodeURI = /* @__PURE__ */ __name((str) => tryDecode(str, decodeURI), "tryDecodeURI");
var getPath = /* @__PURE__ */ __name((request) => {
  const url = request.url;
  const start = url.indexOf(
    "/",
    url.charCodeAt(9) === 58 ? 13 : 8
  );
  let i = start;
  for (; i < url.length; i++) {
    const charCode = url.charCodeAt(i);
    if (charCode === 37) {
      const queryIndex = url.indexOf("?", i);
      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);
      return tryDecodeURI(path.includes("%25") ? path.replace(/%25/g, "%2525") : path);
    } else if (charCode === 63) {
      break;
    }
  }
  return url.slice(start, i);
}, "getPath");
var getPathNoStrict = /* @__PURE__ */ __name((request) => {
  const result = getPath(request);
  return result.length > 1 && result.at(-1) === "/" ? result.slice(0, -1) : result;
}, "getPathNoStrict");
var mergePath = /* @__PURE__ */ __name((base, sub, ...rest) => {
  if (rest.length) {
    sub = mergePath(sub, ...rest);
  }
  return `${base?.[0] === "/" ? "" : "/"}${base}${sub === "/" ? "" : `${base?.at(-1) === "/" ? "" : "/"}${sub?.[0] === "/" ? sub.slice(1) : sub}`}`;
}, "mergePath");
var checkOptionalParameter = /* @__PURE__ */ __name((path) => {
  if (path.charCodeAt(path.length - 1) !== 63 || !path.includes(":")) {
    return null;
  }
  const segments = path.split("/");
  const results = [];
  let basePath = "";
  segments.forEach((segment) => {
    if (segment !== "" && !/\:/.test(segment)) {
      basePath += "/" + segment;
    } else if (/\:/.test(segment)) {
      if (/\?/.test(segment)) {
        if (results.length === 0 && basePath === "") {
          results.push("/");
        } else {
          results.push(basePath);
        }
        const optionalSegment = segment.replace("?", "");
        basePath += "/" + optionalSegment;
        results.push(basePath);
      } else {
        basePath += "/" + segment;
      }
    }
  });
  return results.filter((v, i, a) => a.indexOf(v) === i);
}, "checkOptionalParameter");
var _decodeURI = /* @__PURE__ */ __name((value) => {
  if (!/[%+]/.test(value)) {
    return value;
  }
  if (value.indexOf("+") !== -1) {
    value = value.replace(/\+/g, " ");
  }
  return value.indexOf("%") !== -1 ? tryDecode(value, decodeURIComponent_) : value;
}, "_decodeURI");
var _getQueryParam = /* @__PURE__ */ __name((url, key, multiple) => {
  let encoded;
  if (!multiple && key && !/[%+]/.test(key)) {
    let keyIndex2 = url.indexOf(`?${key}`, 8);
    if (keyIndex2 === -1) {
      keyIndex2 = url.indexOf(`&${key}`, 8);
    }
    while (keyIndex2 !== -1) {
      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);
      if (trailingKeyCode === 61) {
        const valueIndex = keyIndex2 + key.length + 2;
        const endIndex = url.indexOf("&", valueIndex);
        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));
      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {
        return "";
      }
      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);
    }
    encoded = /[%+]/.test(url);
    if (!encoded) {
      return void 0;
    }
  }
  const results = {};
  encoded ??= /[%+]/.test(url);
  let keyIndex = url.indexOf("?", 8);
  while (keyIndex !== -1) {
    const nextKeyIndex = url.indexOf("&", keyIndex + 1);
    let valueIndex = url.indexOf("=", keyIndex);
    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {
      valueIndex = -1;
    }
    let name = url.slice(
      keyIndex + 1,
      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex
    );
    if (encoded) {
      name = _decodeURI(name);
    }
    keyIndex = nextKeyIndex;
    if (name === "") {
      continue;
    }
    let value;
    if (valueIndex === -1) {
      value = "";
    } else {
      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);
      if (encoded) {
        value = _decodeURI(value);
      }
    }
    if (multiple) {
      if (!(results[name] && Array.isArray(results[name]))) {
        results[name] = [];
      }
      ;
      results[name].push(value);
    } else {
      results[name] ??= value;
    }
  }
  return key ? results[key] : results;
}, "_getQueryParam");
var getQueryParam = _getQueryParam;
var getQueryParams = /* @__PURE__ */ __name((url, key) => {
  return _getQueryParam(url, key, true);
}, "getQueryParams");
var decodeURIComponent_ = decodeURIComponent;

// node_modules/hono/dist/request.js
var tryDecodeURIComponent = /* @__PURE__ */ __name((str) => tryDecode(str, decodeURIComponent_), "tryDecodeURIComponent");
var HonoRequest = /* @__PURE__ */ __name(class {
  raw;
  #validatedData;
  #matchResult;
  routeIndex = 0;
  path;
  bodyCache = {};
  constructor(request, path = "/", matchResult = [[]]) {
    this.raw = request;
    this.path = path;
    this.#matchResult = matchResult;
    this.#validatedData = {};
  }
  param(key) {
    return key ? this.#getDecodedParam(key) : this.#getAllDecodedParams();
  }
  #getDecodedParam(key) {
    const paramKey = this.#matchResult[0][this.routeIndex][1][key];
    const param = this.#getParamValue(paramKey);
    return param ? /\%/.test(param) ? tryDecodeURIComponent(param) : param : void 0;
  }
  #getAllDecodedParams() {
    const decoded = {};
    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);
    for (const key of keys) {
      const value = this.#getParamValue(this.#matchResult[0][this.routeIndex][1][key]);
      if (value && typeof value === "string") {
        decoded[key] = /\%/.test(value) ? tryDecodeURIComponent(value) : value;
      }
    }
    return decoded;
  }
  #getParamValue(paramKey) {
    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;
  }
  query(key) {
    return getQueryParam(this.url, key);
  }
  queries(key) {
    return getQueryParams(this.url, key);
  }
  header(name) {
    if (name) {
      return this.raw.headers.get(name) ?? void 0;
    }
    const headerData = {};
    this.raw.headers.forEach((value, key) => {
      headerData[key] = value;
    });
    return headerData;
  }
  async parseBody(options) {
    return this.bodyCache.parsedBody ??= await parseBody(this, options);
  }
  #cachedBody = (key) => {
    const { bodyCache, raw: raw2 } = this;
    const cachedBody = bodyCache[key];
    if (cachedBody) {
      return cachedBody;
    }
    const anyCachedKey = Object.keys(bodyCache)[0];
    if (anyCachedKey) {
      return bodyCache[anyCachedKey].then((body) => {
        if (anyCachedKey === "json") {
          body = JSON.stringify(body);
        }
        return new Response(body)[key]();
      });
    }
    return bodyCache[key] = raw2[key]();
  };
  json() {
    return this.#cachedBody("json");
  }
  text() {
    return this.#cachedBody("text");
  }
  arrayBuffer() {
    return this.#cachedBody("arrayBuffer");
  }
  blob() {
    return this.#cachedBody("blob");
  }
  formData() {
    return this.#cachedBody("formData");
  }
  addValidatedData(target, data) {
    this.#validatedData[target] = data;
  }
  valid(target) {
    return this.#validatedData[target];
  }
  get url() {
    return this.raw.url;
  }
  get method() {
    return this.raw.method;
  }
  get [GET_MATCH_RESULT]() {
    return this.#matchResult;
  }
  get matchedRoutes() {
    return this.#matchResult[0].map(([[, route]]) => route);
  }
  get routePath() {
    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;
  }
}, "HonoRequest");

// node_modules/hono/dist/utils/html.js
var HtmlEscapedCallbackPhase = {
  Stringify: 1,
  BeforeStream: 2,
  Stream: 3
};
var raw = /* @__PURE__ */ __name((value, callbacks) => {
  const escapedString = new String(value);
  escapedString.isEscaped = true;
  escapedString.callbacks = callbacks;
  return escapedString;
}, "raw");
var resolveCallback = /* @__PURE__ */ __name(async (str, phase, preserveCallbacks, context, buffer) => {
  if (typeof str === "object" && !(str instanceof String)) {
    if (!(str instanceof Promise)) {
      str = str.toString();
    }
    if (str instanceof Promise) {
      str = await str;
    }
  }
  const callbacks = str.callbacks;
  if (!callbacks?.length) {
    return Promise.resolve(str);
  }
  if (buffer) {
    buffer[0] += str;
  } else {
    buffer = [str];
  }
  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(
    (res) => Promise.all(
      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))
    ).then(() => buffer[0])
  );
  if (preserveCallbacks) {
    return raw(await resStr, callbacks);
  } else {
    return resStr;
  }
}, "resolveCallback");

// node_modules/hono/dist/context.js
var TEXT_PLAIN = "text/plain; charset=UTF-8";
var setDefaultContentType = /* @__PURE__ */ __name((contentType, headers) => {
  return {
    "Content-Type": contentType,
    ...headers
  };
}, "setDefaultContentType");
var Context = /* @__PURE__ */ __name(class {
  #rawRequest;
  #req;
  env = {};
  #var;
  finalized = false;
  error;
  #status;
  #executionCtx;
  #res;
  #layout;
  #renderer;
  #notFoundHandler;
  #preparedHeaders;
  #matchResult;
  #path;
  constructor(req, options) {
    this.#rawRequest = req;
    if (options) {
      this.#executionCtx = options.executionCtx;
      this.env = options.env;
      this.#notFoundHandler = options.notFoundHandler;
      this.#path = options.path;
      this.#matchResult = options.matchResult;
    }
  }
  get req() {
    this.#req ??= new HonoRequest(this.#rawRequest, this.#path, this.#matchResult);
    return this.#req;
  }
  get event() {
    if (this.#executionCtx && "respondWith" in this.#executionCtx) {
      return this.#executionCtx;
    } else {
      throw Error("This context has no FetchEvent");
    }
  }
  get executionCtx() {
    if (this.#executionCtx) {
      return this.#executionCtx;
    } else {
      throw Error("This context has no ExecutionContext");
    }
  }
  get res() {
    return this.#res ||= new Response(null, {
      headers: this.#preparedHeaders ??= new Headers()
    });
  }
  set res(_res) {
    if (this.#res && _res) {
      _res = new Response(_res.body, _res);
      for (const [k, v] of this.#res.headers.entries()) {
        if (k === "content-type") {
          continue;
        }
        if (k === "set-cookie") {
          const cookies = this.#res.headers.getSetCookie();
          _res.headers.delete("set-cookie");
          for (const cookie of cookies) {
            _res.headers.append("set-cookie", cookie);
          }
        } else {
          _res.headers.set(k, v);
        }
      }
    }
    this.#res = _res;
    this.finalized = true;
  }
  render = (...args) => {
    this.#renderer ??= (content) => this.html(content);
    return this.#renderer(...args);
  };
  setLayout = (layout) => this.#layout = layout;
  getLayout = () => this.#layout;
  setRenderer = (renderer) => {
    this.#renderer = renderer;
  };
  header = (name, value, options) => {
    if (this.finalized) {
      this.#res = new Response(this.#res.body, this.#res);
    }
    const headers = this.#res ? this.#res.headers : this.#preparedHeaders ??= new Headers();
    if (value === void 0) {
      headers.delete(name);
    } else if (options?.append) {
      headers.append(name, value);
    } else {
      headers.set(name, value);
    }
  };
  status = (status) => {
    this.#status = status;
  };
  set = (key, value) => {
    this.#var ??= /* @__PURE__ */ new Map();
    this.#var.set(key, value);
  };
  get = (key) => {
    return this.#var ? this.#var.get(key) : void 0;
  };
  get var() {
    if (!this.#var) {
      return {};
    }
    return Object.fromEntries(this.#var);
  }
  #newResponse(data, arg, headers) {
    const responseHeaders = this.#res ? new Headers(this.#res.headers) : this.#preparedHeaders ?? new Headers();
    if (typeof arg === "object" && "headers" in arg) {
      const argHeaders = arg.headers instanceof Headers ? arg.headers : new Headers(arg.headers);
      for (const [key, value] of argHeaders) {
        if (key.toLowerCase() === "set-cookie") {
          responseHeaders.append(key, value);
        } else {
          responseHeaders.set(key, value);
        }
      }
    }
    if (headers) {
      for (const [k, v] of Object.entries(headers)) {
        if (typeof v === "string") {
          responseHeaders.set(k, v);
        } else {
          responseHeaders.delete(k);
          for (const v2 of v) {
            responseHeaders.append(k, v2);
          }
        }
      }
    }
    const status = typeof arg === "number" ? arg : arg?.status ?? this.#status;
    return new Response(data, { status, headers: responseHeaders });
  }
  newResponse = (...args) => this.#newResponse(...args);
  body = (data, arg, headers) => this.#newResponse(data, arg, headers);
  text = (text, arg, headers) => {
    return !this.#preparedHeaders && !this.#status && !arg && !headers && !this.finalized ? new Response(text) : this.#newResponse(
      text,
      arg,
      setDefaultContentType(TEXT_PLAIN, headers)
    );
  };
  json = (object, arg, headers) => {
    return this.#newResponse(
      JSON.stringify(object),
      arg,
      setDefaultContentType("application/json", headers)
    );
  };
  html = (html, arg, headers) => {
    const res = /* @__PURE__ */ __name((html2) => this.#newResponse(html2, arg, setDefaultContentType("text/html; charset=UTF-8", headers)), "res");
    return typeof html === "object" ? resolveCallback(html, HtmlEscapedCallbackPhase.Stringify, false, {}).then(res) : res(html);
  };
  redirect = (location, status) => {
    this.header("Location", String(location));
    return this.newResponse(null, status ?? 302);
  };
  notFound = () => {
    this.#notFoundHandler ??= () => new Response();
    return this.#notFoundHandler(this);
  };
}, "Context");

// node_modules/hono/dist/router.js
var METHOD_NAME_ALL = "ALL";
var METHOD_NAME_ALL_LOWERCASE = "all";
var METHODS = ["get", "post", "put", "delete", "options", "patch"];
var MESSAGE_MATCHER_IS_ALREADY_BUILT = "Can not add a route since the matcher is already built.";
var UnsupportedPathError = /* @__PURE__ */ __name(class extends Error {
}, "UnsupportedPathError");

// node_modules/hono/dist/utils/constants.js
var COMPOSED_HANDLER = "__COMPOSED_HANDLER";

// node_modules/hono/dist/hono-base.js
var notFoundHandler = /* @__PURE__ */ __name((c) => {
  return c.text("404 Not Found", 404);
}, "notFoundHandler");
var errorHandler = /* @__PURE__ */ __name((err, c) => {
  if ("getResponse" in err) {
    const res = err.getResponse();
    return c.newResponse(res.body, res);
  }
  console.error(err);
  return c.text("Internal Server Error", 500);
}, "errorHandler");
var Hono = /* @__PURE__ */ __name(class {
  get;
  post;
  put;
  delete;
  options;
  patch;
  all;
  on;
  use;
  router;
  getPath;
  _basePath = "/";
  #path = "/";
  routes = [];
  constructor(options = {}) {
    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];
    allMethods.forEach((method) => {
      this[method] = (args1, ...args) => {
        if (typeof args1 === "string") {
          this.#path = args1;
        } else {
          this.#addRoute(method, this.#path, args1);
        }
        args.forEach((handler) => {
          this.#addRoute(method, this.#path, handler);
        });
        return this;
      };
    });
    this.on = (method, path, ...handlers) => {
      for (const p of [path].flat()) {
        this.#path = p;
        for (const m of [method].flat()) {
          handlers.map((handler) => {
            this.#addRoute(m.toUpperCase(), this.#path, handler);
          });
        }
      }
      return this;
    };
    this.use = (arg1, ...handlers) => {
      if (typeof arg1 === "string") {
        this.#path = arg1;
      } else {
        this.#path = "*";
        handlers.unshift(arg1);
      }
      handlers.forEach((handler) => {
        this.#addRoute(METHOD_NAME_ALL, this.#path, handler);
      });
      return this;
    };
    const { strict, ...optionsWithoutStrict } = options;
    Object.assign(this, optionsWithoutStrict);
    this.getPath = strict ?? true ? options.getPath ?? getPath : getPathNoStrict;
  }
  #clone() {
    const clone = new Hono({
      router: this.router,
      getPath: this.getPath
    });
    clone.errorHandler = this.errorHandler;
    clone.#notFoundHandler = this.#notFoundHandler;
    clone.routes = this.routes;
    return clone;
  }
  #notFoundHandler = notFoundHandler;
  errorHandler = errorHandler;
  route(path, app5) {
    const subApp = this.basePath(path);
    app5.routes.map((r) => {
      let handler;
      if (app5.errorHandler === errorHandler) {
        handler = r.handler;
      } else {
        handler = /* @__PURE__ */ __name(async (c, next) => (await compose([], app5.errorHandler)(c, () => r.handler(c, next))).res, "handler");
        handler[COMPOSED_HANDLER] = r.handler;
      }
      subApp.#addRoute(r.method, r.path, handler);
    });
    return this;
  }
  basePath(path) {
    const subApp = this.#clone();
    subApp._basePath = mergePath(this._basePath, path);
    return subApp;
  }
  onError = (handler) => {
    this.errorHandler = handler;
    return this;
  };
  notFound = (handler) => {
    this.#notFoundHandler = handler;
    return this;
  };
  mount(path, applicationHandler, options) {
    let replaceRequest;
    let optionHandler;
    if (options) {
      if (typeof options === "function") {
        optionHandler = options;
      } else {
        optionHandler = options.optionHandler;
        if (options.replaceRequest === false) {
          replaceRequest = /* @__PURE__ */ __name((request) => request, "replaceRequest");
        } else {
          replaceRequest = options.replaceRequest;
        }
      }
    }
    const getOptions = optionHandler ? (c) => {
      const options2 = optionHandler(c);
      return Array.isArray(options2) ? options2 : [options2];
    } : (c) => {
      let executionContext = void 0;
      try {
        executionContext = c.executionCtx;
      } catch {
      }
      return [c.env, executionContext];
    };
    replaceRequest ||= (() => {
      const mergedPath = mergePath(this._basePath, path);
      const pathPrefixLength = mergedPath === "/" ? 0 : mergedPath.length;
      return (request) => {
        const url = new URL(request.url);
        url.pathname = url.pathname.slice(pathPrefixLength) || "/";
        return new Request(url, request);
      };
    })();
    const handler = /* @__PURE__ */ __name(async (c, next) => {
      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));
      if (res) {
        return res;
      }
      await next();
    }, "handler");
    this.#addRoute(METHOD_NAME_ALL, mergePath(path, "*"), handler);
    return this;
  }
  #addRoute(method, path, handler) {
    method = method.toUpperCase();
    path = mergePath(this._basePath, path);
    const r = { basePath: this._basePath, path, method, handler };
    this.router.add(method, path, [handler, r]);
    this.routes.push(r);
  }
  #handleError(err, c) {
    if (err instanceof Error) {
      return this.errorHandler(err, c);
    }
    throw err;
  }
  #dispatch(request, executionCtx, env, method) {
    if (method === "HEAD") {
      return (async () => new Response(null, await this.#dispatch(request, executionCtx, env, "GET")))();
    }
    const path = this.getPath(request, { env });
    const matchResult = this.router.match(method, path);
    const c = new Context(request, {
      path,
      matchResult,
      env,
      executionCtx,
      notFoundHandler: this.#notFoundHandler
    });
    if (matchResult[0].length === 1) {
      let res;
      try {
        res = matchResult[0][0][0][0](c, async () => {
          c.res = await this.#notFoundHandler(c);
        });
      } catch (err) {
        return this.#handleError(err, c);
      }
      return res instanceof Promise ? res.then(
        (resolved) => resolved || (c.finalized ? c.res : this.#notFoundHandler(c))
      ).catch((err) => this.#handleError(err, c)) : res ?? this.#notFoundHandler(c);
    }
    const composed = compose(matchResult[0], this.errorHandler, this.#notFoundHandler);
    return (async () => {
      try {
        const context = await composed(c);
        if (!context.finalized) {
          throw new Error(
            "Context is not finalized. Did you forget to return a Response object or `await next()`?"
          );
        }
        return context.res;
      } catch (err) {
        return this.#handleError(err, c);
      }
    })();
  }
  fetch = (request, ...rest) => {
    return this.#dispatch(request, rest[1], rest[0], request.method);
  };
  request = (input, requestInit, Env, executionCtx) => {
    if (input instanceof Request) {
      return this.fetch(requestInit ? new Request(input, requestInit) : input, Env, executionCtx);
    }
    input = input.toString();
    return this.fetch(
      new Request(
        /^https?:\/\//.test(input) ? input : `http://localhost${mergePath("/", input)}`,
        requestInit
      ),
      Env,
      executionCtx
    );
  };
  fire = () => {
    addEventListener("fetch", (event) => {
      event.respondWith(this.#dispatch(event.request, event, void 0, event.request.method));
    });
  };
}, "Hono");

// node_modules/hono/dist/router/reg-exp-router/node.js
var LABEL_REG_EXP_STR = "[^/]+";
var ONLY_WILDCARD_REG_EXP_STR = ".*";
var TAIL_WILDCARD_REG_EXP_STR = "(?:|/.*)";
var PATH_ERROR = Symbol();
var regExpMetaChars = new Set(".\\+*[^]$()");
function compareKey(a, b) {
  if (a.length === 1) {
    return b.length === 1 ? a < b ? -1 : 1 : -1;
  }
  if (b.length === 1) {
    return 1;
  }
  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {
    return 1;
  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {
    return -1;
  }
  if (a === LABEL_REG_EXP_STR) {
    return 1;
  } else if (b === LABEL_REG_EXP_STR) {
    return -1;
  }
  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;
}
__name(compareKey, "compareKey");
var Node = /* @__PURE__ */ __name(class {
  #index;
  #varIndex;
  #children = /* @__PURE__ */ Object.create(null);
  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {
    if (tokens.length === 0) {
      if (this.#index !== void 0) {
        throw PATH_ERROR;
      }
      if (pathErrorCheckOnly) {
        return;
      }
      this.#index = index;
      return;
    }
    const [token, ...restTokens] = tokens;
    const pattern = token === "*" ? restTokens.length === 0 ? ["", "", ONLY_WILDCARD_REG_EXP_STR] : ["", "", LABEL_REG_EXP_STR] : token === "/*" ? ["", "", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\:([^\{\}]+)(?:\{(.+)\})?$/);
    let node;
    if (pattern) {
      const name = pattern[1];
      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;
      if (name && pattern[2]) {
        regexpStr = regexpStr.replace(/^\((?!\?:)(?=[^)]+\)$)/, "(?:");
        if (/\((?!\?:)/.test(regexpStr)) {
          throw PATH_ERROR;
        }
      }
      node = this.#children[regexpStr];
      if (!node) {
        if (Object.keys(this.#children).some(
          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR
        )) {
          throw PATH_ERROR;
        }
        if (pathErrorCheckOnly) {
          return;
        }
        node = this.#children[regexpStr] = new Node();
        if (name !== "") {
          node.#varIndex = context.varIndex++;
        }
      }
      if (!pathErrorCheckOnly && name !== "") {
        paramMap.push([name, node.#varIndex]);
      }
    } else {
      node = this.#children[token];
      if (!node) {
        if (Object.keys(this.#children).some(
          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR
        )) {
          throw PATH_ERROR;
        }
        if (pathErrorCheckOnly) {
          return;
        }
        node = this.#children[token] = new Node();
      }
    }
    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);
  }
  buildRegExpStr() {
    const childKeys = Object.keys(this.#children).sort(compareKey);
    const strList = childKeys.map((k) => {
      const c = this.#children[k];
      return (typeof c.#varIndex === "number" ? `(${k})@${c.#varIndex}` : regExpMetaChars.has(k) ? `\\${k}` : k) + c.buildRegExpStr();
    });
    if (typeof this.#index === "number") {
      strList.unshift(`#${this.#index}`);
    }
    if (strList.length === 0) {
      return "";
    }
    if (strList.length === 1) {
      return strList[0];
    }
    return "(?:" + strList.join("|") + ")";
  }
}, "Node");

// node_modules/hono/dist/router/reg-exp-router/trie.js
var Trie = /* @__PURE__ */ __name(class {
  #context = { varIndex: 0 };
  #root = new Node();
  insert(path, index, pathErrorCheckOnly) {
    const paramAssoc = [];
    const groups = [];
    for (let i = 0; ; ) {
      let replaced = false;
      path = path.replace(/\{[^}]+\}/g, (m) => {
        const mark = `@\\${i}`;
        groups[i] = [mark, m];
        i++;
        replaced = true;
        return mark;
      });
      if (!replaced) {
        break;
      }
    }
    const tokens = path.match(/(?::[^\/]+)|(?:\/\*$)|./g) || [];
    for (let i = groups.length - 1; i >= 0; i--) {
      const [mark] = groups[i];
      for (let j = tokens.length - 1; j >= 0; j--) {
        if (tokens[j].indexOf(mark) !== -1) {
          tokens[j] = tokens[j].replace(mark, groups[i][1]);
          break;
        }
      }
    }
    this.#root.insert(tokens, index, paramAssoc, this.#context, pathErrorCheckOnly);
    return paramAssoc;
  }
  buildRegExp() {
    let regexp = this.#root.buildRegExpStr();
    if (regexp === "") {
      return [/^$/, [], []];
    }
    let captureIndex = 0;
    const indexReplacementMap = [];
    const paramReplacementMap = [];
    regexp = regexp.replace(/#(\d+)|@(\d+)|\.\*\$/g, (_, handlerIndex, paramIndex) => {
      if (handlerIndex !== void 0) {
        indexReplacementMap[++captureIndex] = Number(handlerIndex);
        return "$()";
      }
      if (paramIndex !== void 0) {
        paramReplacementMap[Number(paramIndex)] = ++captureIndex;
        return "";
      }
      return "";
    });
    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];
  }
}, "Trie");

// node_modules/hono/dist/router/reg-exp-router/router.js
var emptyParam = [];
var nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];
var wildcardRegExpCache = /* @__PURE__ */ Object.create(null);
function buildWildcardRegExp(path) {
  return wildcardRegExpCache[path] ??= new RegExp(
    path === "*" ? "" : `^${path.replace(
      /\/\*$|([.\\+*[^\]$()])/g,
      (_, metaChar) => metaChar ? `\\${metaChar}` : "(?:|/.*)"
    )}$`
  );
}
__name(buildWildcardRegExp, "buildWildcardRegExp");
function clearWildcardRegExpCache() {
  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);
}
__name(clearWildcardRegExpCache, "clearWildcardRegExpCache");
function buildMatcherFromPreprocessedRoutes(routes) {
  const trie = new Trie();
  const handlerData = [];
  if (routes.length === 0) {
    return nullMatcher;
  }
  const routesWithStaticPathFlag = routes.map(
    (route) => [!/\*|\/:/.test(route[0]), ...route]
  ).sort(
    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length
  );
  const staticMap = /* @__PURE__ */ Object.create(null);
  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {
    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];
    if (pathErrorCheckOnly) {
      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];
    } else {
      j++;
    }
    let paramAssoc;
    try {
      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);
    } catch (e) {
      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;
    }
    if (pathErrorCheckOnly) {
      continue;
    }
    handlerData[j] = handlers.map(([h, paramCount]) => {
      const paramIndexMap = /* @__PURE__ */ Object.create(null);
      paramCount -= 1;
      for (; paramCount >= 0; paramCount--) {
        const [key, value] = paramAssoc[paramCount];
        paramIndexMap[key] = value;
      }
      return [h, paramIndexMap];
    });
  }
  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();
  for (let i = 0, len = handlerData.length; i < len; i++) {
    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {
      const map = handlerData[i][j]?.[1];
      if (!map) {
        continue;
      }
      const keys = Object.keys(map);
      for (let k = 0, len3 = keys.length; k < len3; k++) {
        map[keys[k]] = paramReplacementMap[map[keys[k]]];
      }
    }
  }
  const handlerMap = [];
  for (const i in indexReplacementMap) {
    handlerMap[i] = handlerData[indexReplacementMap[i]];
  }
  return [regexp, handlerMap, staticMap];
}
__name(buildMatcherFromPreprocessedRoutes, "buildMatcherFromPreprocessedRoutes");
function findMiddleware(middleware, path) {
  if (!middleware) {
    return void 0;
  }
  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {
    if (buildWildcardRegExp(k).test(path)) {
      return [...middleware[k]];
    }
  }
  return void 0;
}
__name(findMiddleware, "findMiddleware");
var RegExpRouter = /* @__PURE__ */ __name(class {
  name = "RegExpRouter";
  #middleware;
  #routes;
  constructor() {
    this.#middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };
    this.#routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };
  }
  add(method, path, handler) {
    const middleware = this.#middleware;
    const routes = this.#routes;
    if (!middleware || !routes) {
      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);
    }
    if (!middleware[method]) {
      ;
      [middleware, routes].forEach((handlerMap) => {
        handlerMap[method] = /* @__PURE__ */ Object.create(null);
        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {
          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];
        });
      });
    }
    if (path === "/*") {
      path = "*";
    }
    const paramCount = (path.match(/\/:/g) || []).length;
    if (/\*$/.test(path)) {
      const re = buildWildcardRegExp(path);
      if (method === METHOD_NAME_ALL) {
        Object.keys(middleware).forEach((m) => {
          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];
        });
      } else {
        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];
      }
      Object.keys(middleware).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          Object.keys(middleware[m]).forEach((p) => {
            re.test(p) && middleware[m][p].push([handler, paramCount]);
          });
        }
      });
      Object.keys(routes).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          Object.keys(routes[m]).forEach(
            (p) => re.test(p) && routes[m][p].push([handler, paramCount])
          );
        }
      });
      return;
    }
    const paths = checkOptionalParameter(path) || [path];
    for (let i = 0, len = paths.length; i < len; i++) {
      const path2 = paths[i];
      Object.keys(routes).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          routes[m][path2] ||= [
            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []
          ];
          routes[m][path2].push([handler, paramCount - len + i + 1]);
        }
      });
    }
  }
  match(method, path) {
    clearWildcardRegExpCache();
    const matchers = this.#buildAllMatchers();
    this.match = (method2, path2) => {
      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];
      const staticMatch = matcher[2][path2];
      if (staticMatch) {
        return staticMatch;
      }
      const match = path2.match(matcher[0]);
      if (!match) {
        return [[], emptyParam];
      }
      const index = match.indexOf("", 1);
      return [matcher[1][index], match];
    };
    return this.match(method, path);
  }
  #buildAllMatchers() {
    const matchers = /* @__PURE__ */ Object.create(null);
    Object.keys(this.#routes).concat(Object.keys(this.#middleware)).forEach((method) => {
      matchers[method] ||= this.#buildMatcher(method);
    });
    this.#middleware = this.#routes = void 0;
    return matchers;
  }
  #buildMatcher(method) {
    const routes = [];
    let hasOwnRoute = method === METHOD_NAME_ALL;
    [this.#middleware, this.#routes].forEach((r) => {
      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];
      if (ownRoute.length !== 0) {
        hasOwnRoute ||= true;
        routes.push(...ownRoute);
      } else if (method !== METHOD_NAME_ALL) {
        routes.push(
          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])
        );
      }
    });
    if (!hasOwnRoute) {
      return null;
    } else {
      return buildMatcherFromPreprocessedRoutes(routes);
    }
  }
}, "RegExpRouter");

// node_modules/hono/dist/router/smart-router/router.js
var SmartRouter = /* @__PURE__ */ __name(class {
  name = "SmartRouter";
  #routers = [];
  #routes = [];
  constructor(init) {
    this.#routers = init.routers;
  }
  add(method, path, handler) {
    if (!this.#routes) {
      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);
    }
    this.#routes.push([method, path, handler]);
  }
  match(method, path) {
    if (!this.#routes) {
      throw new Error("Fatal error");
    }
    const routers = this.#routers;
    const routes = this.#routes;
    const len = routers.length;
    let i = 0;
    let res;
    for (; i < len; i++) {
      const router = routers[i];
      try {
        for (let i2 = 0, len2 = routes.length; i2 < len2; i2++) {
          router.add(...routes[i2]);
        }
        res = router.match(method, path);
      } catch (e) {
        if (e instanceof UnsupportedPathError) {
          continue;
        }
        throw e;
      }
      this.match = router.match.bind(router);
      this.#routers = [router];
      this.#routes = void 0;
      break;
    }
    if (i === len) {
      throw new Error("Fatal error");
    }
    this.name = `SmartRouter + ${this.activeRouter.name}`;
    return res;
  }
  get activeRouter() {
    if (this.#routes || this.#routers.length !== 1) {
      throw new Error("No active router has been determined yet.");
    }
    return this.#routers[0];
  }
}, "SmartRouter");

// node_modules/hono/dist/router/trie-router/node.js
var emptyParams = /* @__PURE__ */ Object.create(null);
var Node2 = /* @__PURE__ */ __name(class {
  #methods;
  #children;
  #patterns;
  #order = 0;
  #params = emptyParams;
  constructor(method, handler, children) {
    this.#children = children || /* @__PURE__ */ Object.create(null);
    this.#methods = [];
    if (method && handler) {
      const m = /* @__PURE__ */ Object.create(null);
      m[method] = { handler, possibleKeys: [], score: 0 };
      this.#methods = [m];
    }
    this.#patterns = [];
  }
  insert(method, path, handler) {
    this.#order = ++this.#order;
    let curNode = this;
    const parts = splitRoutingPath(path);
    const possibleKeys = [];
    for (let i = 0, len = parts.length; i < len; i++) {
      const p = parts[i];
      const nextP = parts[i + 1];
      const pattern = getPattern(p, nextP);
      const key = Array.isArray(pattern) ? pattern[0] : p;
      if (key in curNode.#children) {
        curNode = curNode.#children[key];
        if (pattern) {
          possibleKeys.push(pattern[1]);
        }
        continue;
      }
      curNode.#children[key] = new Node2();
      if (pattern) {
        curNode.#patterns.push(pattern);
        possibleKeys.push(pattern[1]);
      }
      curNode = curNode.#children[key];
    }
    curNode.#methods.push({
      [method]: {
        handler,
        possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),
        score: this.#order
      }
    });
    return curNode;
  }
  #getHandlerSets(node, method, nodeParams, params) {
    const handlerSets = [];
    for (let i = 0, len = node.#methods.length; i < len; i++) {
      const m = node.#methods[i];
      const handlerSet = m[method] || m[METHOD_NAME_ALL];
      const processedSet = {};
      if (handlerSet !== void 0) {
        handlerSet.params = /* @__PURE__ */ Object.create(null);
        handlerSets.push(handlerSet);
        if (nodeParams !== emptyParams || params && params !== emptyParams) {
          for (let i2 = 0, len2 = handlerSet.possibleKeys.length; i2 < len2; i2++) {
            const key = handlerSet.possibleKeys[i2];
            const processed = processedSet[handlerSet.score];
            handlerSet.params[key] = params?.[key] && !processed ? params[key] : nodeParams[key] ?? params?.[key];
            processedSet[handlerSet.score] = true;
          }
        }
      }
    }
    return handlerSets;
  }
  search(method, path) {
    const handlerSets = [];
    this.#params = emptyParams;
    const curNode = this;
    let curNodes = [curNode];
    const parts = splitPath(path);
    const curNodesQueue = [];
    for (let i = 0, len = parts.length; i < len; i++) {
      const part = parts[i];
      const isLast = i === len - 1;
      const tempNodes = [];
      for (let j = 0, len2 = curNodes.length; j < len2; j++) {
        const node = curNodes[j];
        const nextNode = node.#children[part];
        if (nextNode) {
          nextNode.#params = node.#params;
          if (isLast) {
            if (nextNode.#children["*"]) {
              handlerSets.push(
                ...this.#getHandlerSets(nextNode.#children["*"], method, node.#params)
              );
            }
            handlerSets.push(...this.#getHandlerSets(nextNode, method, node.#params));
          } else {
            tempNodes.push(nextNode);
          }
        }
        for (let k = 0, len3 = node.#patterns.length; k < len3; k++) {
          const pattern = node.#patterns[k];
          const params = node.#params === emptyParams ? {} : { ...node.#params };
          if (pattern === "*") {
            const astNode = node.#children["*"];
            if (astNode) {
              handlerSets.push(...this.#getHandlerSets(astNode, method, node.#params));
              astNode.#params = params;
              tempNodes.push(astNode);
            }
            continue;
          }
          if (!part) {
            continue;
          }
          const [key, name, matcher] = pattern;
          const child = node.#children[key];
          const restPathString = parts.slice(i).join("/");
          if (matcher instanceof RegExp) {
            const m = matcher.exec(restPathString);
            if (m) {
              params[name] = m[0];
              handlerSets.push(...this.#getHandlerSets(child, method, node.#params, params));
              if (Object.keys(child.#children).length) {
                child.#params = params;
                const componentCount = m[0].match(/\//)?.length ?? 0;
                const targetCurNodes = curNodesQueue[componentCount] ||= [];
                targetCurNodes.push(child);
              }
              continue;
            }
          }
          if (matcher === true || matcher.test(part)) {
            params[name] = part;
            if (isLast) {
              handlerSets.push(...this.#getHandlerSets(child, method, params, node.#params));
              if (child.#children["*"]) {
                handlerSets.push(
                  ...this.#getHandlerSets(child.#children["*"], method, params, node.#params)
                );
              }
            } else {
              child.#params = params;
              tempNodes.push(child);
            }
          }
        }
      }
      curNodes = tempNodes.concat(curNodesQueue.shift() ?? []);
    }
    if (handlerSets.length > 1) {
      handlerSets.sort((a, b) => {
        return a.score - b.score;
      });
    }
    return [handlerSets.map(({ handler, params }) => [handler, params])];
  }
}, "Node");

// node_modules/hono/dist/router/trie-router/router.js
var TrieRouter = /* @__PURE__ */ __name(class {
  name = "TrieRouter";
  #node;
  constructor() {
    this.#node = new Node2();
  }
  add(method, path, handler) {
    const results = checkOptionalParameter(path);
    if (results) {
      for (let i = 0, len = results.length; i < len; i++) {
        this.#node.insert(method, results[i], handler);
      }
      return;
    }
    this.#node.insert(method, path, handler);
  }
  match(method, path) {
    return this.#node.search(method, path);
  }
}, "TrieRouter");

// node_modules/hono/dist/hono.js
var Hono2 = /* @__PURE__ */ __name(class extends Hono {
  constructor(options = {}) {
    super(options);
    this.router = options.router ?? new SmartRouter({
      routers: [new RegExpRouter(), new TrieRouter()]
    });
  }
}, "Hono");

// node_modules/hono/dist/middleware/cors/index.js
var cors = /* @__PURE__ */ __name((options) => {
  const defaults = {
    origin: "*",
    allowMethods: ["GET", "HEAD", "PUT", "POST", "DELETE", "PATCH"],
    allowHeaders: [],
    exposeHeaders: []
  };
  const opts = {
    ...defaults,
    ...options
  };
  const findAllowOrigin = ((optsOrigin) => {
    if (typeof optsOrigin === "string") {
      if (optsOrigin === "*") {
        return () => optsOrigin;
      } else {
        return (origin) => optsOrigin === origin ? origin : null;
      }
    } else if (typeof optsOrigin === "function") {
      return optsOrigin;
    } else {
      return (origin) => optsOrigin.includes(origin) ? origin : null;
    }
  })(opts.origin);
  const findAllowMethods = ((optsAllowMethods) => {
    if (typeof optsAllowMethods === "function") {
      return optsAllowMethods;
    } else if (Array.isArray(optsAllowMethods)) {
      return () => optsAllowMethods;
    } else {
      return () => [];
    }
  })(opts.allowMethods);
  return /* @__PURE__ */ __name(async function cors2(c, next) {
    function set(key, value) {
      c.res.headers.set(key, value);
    }
    __name(set, "set");
    const allowOrigin = findAllowOrigin(c.req.header("origin") || "", c);
    if (allowOrigin) {
      set("Access-Control-Allow-Origin", allowOrigin);
    }
    if (opts.origin !== "*") {
      const existingVary = c.req.header("Vary");
      if (existingVary) {
        set("Vary", existingVary);
      } else {
        set("Vary", "Origin");
      }
    }
    if (opts.credentials) {
      set("Access-Control-Allow-Credentials", "true");
    }
    if (opts.exposeHeaders?.length) {
      set("Access-Control-Expose-Headers", opts.exposeHeaders.join(","));
    }
    if (c.req.method === "OPTIONS") {
      if (opts.maxAge != null) {
        set("Access-Control-Max-Age", opts.maxAge.toString());
      }
      const allowMethods = findAllowMethods(c.req.header("origin") || "", c);
      if (allowMethods.length) {
        set("Access-Control-Allow-Methods", allowMethods.join(","));
      }
      let headers = opts.allowHeaders;
      if (!headers?.length) {
        const requestHeaders = c.req.header("Access-Control-Request-Headers");
        if (requestHeaders) {
          headers = requestHeaders.split(/\s*,\s*/);
        }
      }
      if (headers?.length) {
        set("Access-Control-Allow-Headers", headers.join(","));
        c.res.headers.append("Vary", "Access-Control-Request-Headers");
      }
      c.res.headers.delete("Content-Length");
      c.res.headers.delete("Content-Type");
      return new Response(null, {
        headers: c.res.headers,
        status: 204,
        statusText: "No Content"
      });
    }
    await next();
  }, "cors2");
}, "cors");

// node_modules/hono/dist/utils/color.js
function getColorEnabled() {
  const { process, Deno } = globalThis;
  const isNoColor = typeof Deno?.noColor === "boolean" ? Deno.noColor : process !== void 0 ? "NO_COLOR" in process?.env : false;
  return !isNoColor;
}
__name(getColorEnabled, "getColorEnabled");
async function getColorEnabledAsync() {
  const { navigator } = globalThis;
  const isNoColor = navigator !== void 0 && navigator.userAgent === "Cloudflare-Workers" ? "NO_COLOR" in ((await import("cloudflare:workers")).env ?? {}) : !getColorEnabled();
  return !isNoColor;
}
__name(getColorEnabledAsync, "getColorEnabledAsync");

// node_modules/hono/dist/middleware/logger/index.js
var humanize = /* @__PURE__ */ __name((times) => {
  const [delimiter, separator] = [",", "."];
  const orderTimes = times.map((v) => v.replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1" + delimiter));
  return orderTimes.join(separator);
}, "humanize");
var time = /* @__PURE__ */ __name((start) => {
  const delta = Date.now() - start;
  return humanize([delta < 1e3 ? delta + "ms" : Math.round(delta / 1e3) + "s"]);
}, "time");
var colorStatus = /* @__PURE__ */ __name(async (status) => {
  const colorEnabled = await getColorEnabledAsync();
  if (colorEnabled) {
    switch (status / 100 | 0) {
      case 5:
        return `\x1B[31m${status}\x1B[0m`;
      case 4:
        return `\x1B[33m${status}\x1B[0m`;
      case 3:
        return `\x1B[36m${status}\x1B[0m`;
      case 2:
        return `\x1B[32m${status}\x1B[0m`;
    }
  }
  return `${status}`;
}, "colorStatus");
async function log(fn, prefix, method, path, status = 0, elapsed) {
  const out = prefix === "<--" ? `${prefix} ${method} ${path}` : `${prefix} ${method} ${path} ${await colorStatus(status)} ${elapsed}`;
  fn(out);
}
__name(log, "log");
var logger = /* @__PURE__ */ __name((fn = console.log) => {
  return /* @__PURE__ */ __name(async function logger2(c, next) {
    const { method, url } = c.req;
    const path = url.slice(url.indexOf("/", 8));
    await log(fn, "<--", method, path);
    const start = Date.now();
    await next();
    await log(fn, "-->", method, path, c.res.status, time(start));
  }, "logger2");
}, "logger");

// node_modules/hono/dist/middleware/pretty-json/index.js
var prettyJSON = /* @__PURE__ */ __name((options) => {
  const targetQuery = options?.query ?? "pretty";
  return /* @__PURE__ */ __name(async function prettyJSON2(c, next) {
    const pretty = c.req.query(targetQuery) || c.req.query(targetQuery) === "";
    await next();
    if (pretty && c.res.headers.get("Content-Type")?.startsWith("application/json")) {
      const obj = await c.res.json();
      c.res = new Response(JSON.stringify(obj, null, options?.space ?? 2), c.res);
    }
  }, "prettyJSON2");
}, "prettyJSON");

// node_modules/hono/dist/middleware/secure-headers/secure-headers.js
var HEADERS_MAP = {
  crossOriginEmbedderPolicy: ["Cross-Origin-Embedder-Policy", "require-corp"],
  crossOriginResourcePolicy: ["Cross-Origin-Resource-Policy", "same-origin"],
  crossOriginOpenerPolicy: ["Cross-Origin-Opener-Policy", "same-origin"],
  originAgentCluster: ["Origin-Agent-Cluster", "?1"],
  referrerPolicy: ["Referrer-Policy", "no-referrer"],
  strictTransportSecurity: ["Strict-Transport-Security", "max-age=15552000; includeSubDomains"],
  xContentTypeOptions: ["X-Content-Type-Options", "nosniff"],
  xDnsPrefetchControl: ["X-DNS-Prefetch-Control", "off"],
  xDownloadOptions: ["X-Download-Options", "noopen"],
  xFrameOptions: ["X-Frame-Options", "SAMEORIGIN"],
  xPermittedCrossDomainPolicies: ["X-Permitted-Cross-Domain-Policies", "none"],
  xXssProtection: ["X-XSS-Protection", "0"]
};
var DEFAULT_OPTIONS = {
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: true,
  crossOriginOpenerPolicy: true,
  originAgentCluster: true,
  referrerPolicy: true,
  strictTransportSecurity: true,
  xContentTypeOptions: true,
  xDnsPrefetchControl: true,
  xDownloadOptions: true,
  xFrameOptions: true,
  xPermittedCrossDomainPolicies: true,
  xXssProtection: true,
  removePoweredBy: true,
  permissionsPolicy: {}
};
var secureHeaders = /* @__PURE__ */ __name((customOptions) => {
  const options = { ...DEFAULT_OPTIONS, ...customOptions };
  const headersToSet = getFilteredHeaders(options);
  const callbacks = [];
  if (options.contentSecurityPolicy) {
    const [callback, value] = getCSPDirectives(options.contentSecurityPolicy);
    if (callback) {
      callbacks.push(callback);
    }
    headersToSet.push(["Content-Security-Policy", value]);
  }
  if (options.contentSecurityPolicyReportOnly) {
    const [callback, value] = getCSPDirectives(options.contentSecurityPolicyReportOnly);
    if (callback) {
      callbacks.push(callback);
    }
    headersToSet.push(["Content-Security-Policy-Report-Only", value]);
  }
  if (options.permissionsPolicy && Object.keys(options.permissionsPolicy).length > 0) {
    headersToSet.push([
      "Permissions-Policy",
      getPermissionsPolicyDirectives(options.permissionsPolicy)
    ]);
  }
  if (options.reportingEndpoints) {
    headersToSet.push(["Reporting-Endpoints", getReportingEndpoints(options.reportingEndpoints)]);
  }
  if (options.reportTo) {
    headersToSet.push(["Report-To", getReportToOptions(options.reportTo)]);
  }
  return /* @__PURE__ */ __name(async function secureHeaders2(ctx, next) {
    const headersToSetForReq = callbacks.length === 0 ? headersToSet : callbacks.reduce((acc, cb) => cb(ctx, acc), headersToSet);
    await next();
    setHeaders(ctx, headersToSetForReq);
    if (options?.removePoweredBy) {
      ctx.res.headers.delete("X-Powered-By");
    }
  }, "secureHeaders2");
}, "secureHeaders");
function getFilteredHeaders(options) {
  return Object.entries(HEADERS_MAP).filter(([key]) => options[key]).map(([key, defaultValue]) => {
    const overrideValue = options[key];
    return typeof overrideValue === "string" ? [defaultValue[0], overrideValue] : defaultValue;
  });
}
__name(getFilteredHeaders, "getFilteredHeaders");
function getCSPDirectives(contentSecurityPolicy) {
  const callbacks = [];
  const resultValues = [];
  for (const [directive, value] of Object.entries(contentSecurityPolicy)) {
    const valueArray = Array.isArray(value) ? value : [value];
    valueArray.forEach((value2, i) => {
      if (typeof value2 === "function") {
        const index = i * 2 + 2 + resultValues.length;
        callbacks.push((ctx, values) => {
          values[index] = value2(ctx, directive);
        });
      }
    });
    resultValues.push(
      directive.replace(
        /[A-Z]+(?![a-z])|[A-Z]/g,
        (match, offset) => offset ? "-" + match.toLowerCase() : match.toLowerCase()
      ),
      ...valueArray.flatMap((value2) => [" ", value2]),
      "; "
    );
  }
  resultValues.pop();
  return callbacks.length === 0 ? [void 0, resultValues.join("")] : [
    (ctx, headersToSet) => headersToSet.map((values) => {
      if (values[0] === "Content-Security-Policy" || values[0] === "Content-Security-Policy-Report-Only") {
        const clone = values[1].slice();
        callbacks.forEach((cb) => {
          cb(ctx, clone);
        });
        return [values[0], clone.join("")];
      } else {
        return values;
      }
    }),
    resultValues
  ];
}
__name(getCSPDirectives, "getCSPDirectives");
function getPermissionsPolicyDirectives(policy) {
  return Object.entries(policy).map(([directive, value]) => {
    const kebabDirective = camelToKebab(directive);
    if (typeof value === "boolean") {
      return `${kebabDirective}=${value ? "*" : "none"}`;
    }
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return `${kebabDirective}=()`;
      }
      if (value.length === 1 && (value[0] === "*" || value[0] === "none")) {
        return `${kebabDirective}=${value[0]}`;
      }
      const allowlist = value.map((item) => ["self", "src"].includes(item) ? item : `"${item}"`);
      return `${kebabDirective}=(${allowlist.join(" ")})`;
    }
    return "";
  }).filter(Boolean).join(", ");
}
__name(getPermissionsPolicyDirectives, "getPermissionsPolicyDirectives");
function camelToKebab(str) {
  return str.replace(/([a-z\d])([A-Z])/g, "$1-$2").toLowerCase();
}
__name(camelToKebab, "camelToKebab");
function getReportingEndpoints(reportingEndpoints = []) {
  return reportingEndpoints.map((endpoint) => `${endpoint.name}="${endpoint.url}"`).join(", ");
}
__name(getReportingEndpoints, "getReportingEndpoints");
function getReportToOptions(reportTo = []) {
  return reportTo.map((option) => JSON.stringify(option)).join(", ");
}
__name(getReportToOptions, "getReportToOptions");
function setHeaders(ctx, headersToSet) {
  headersToSet.forEach(([header, value]) => {
    ctx.res.headers.set(header, value);
  });
}
__name(setHeaders, "setHeaders");

// src/services/kv.ts
var KVService = class {
  kv;
  VERSION = "2.0.0";
  constructor(kv) {
    this.kv = kv;
  }
  // 通用 KV 操作
  async get(key) {
    try {
      const data = await this.kv.get(key, "json");
      return data;
    } catch (error) {
      console.error(`KV get error for key ${key}:`, error);
      return null;
    }
  }
  async set(key, value, ttl) {
    try {
      const kvData = {
        data: value,
        lastUpdated: Date.now(),
        version: this.VERSION
      };
      await this.kv.put(key, JSON.stringify(kvData), {
        expirationTtl: ttl
      });
      return true;
    } catch (error) {
      console.error(`KV set error for key ${key}:`, error);
      return false;
    }
  }
  async getWithMetadata(key) {
    try {
      const result = await this.kv.get(key, "json");
      return result;
    } catch (error) {
      console.error(`KV getWithMetadata error for key ${key}:`, error);
      return null;
    }
  }
  // 书签操作
  async getBookmarks() {
    const result = await this.getWithMetadata("cloudnav:bookmarks");
    return result?.data || [];
  }
  async setBookmarks(bookmarks) {
    return await this.set("cloudnav:bookmarks", bookmarks);
  }
  async addBookmark(bookmark) {
    const bookmarks = await this.getBookmarks();
    bookmarks.push(bookmark);
    return await this.setBookmarks(bookmarks);
  }
  async updateBookmark(id, updates) {
    const bookmarks = await this.getBookmarks();
    const index = bookmarks.findIndex((b) => b.id === id);
    if (index === -1)
      return false;
    bookmarks[index] = { ...bookmarks[index], ...updates, updatedAt: Date.now() };
    return await this.setBookmarks(bookmarks);
  }
  async deleteBookmark(id) {
    const bookmarks = await this.getBookmarks();
    const filtered = bookmarks.filter((b) => b.id !== id);
    if (filtered.length === bookmarks.length)
      return false;
    return await this.setBookmarks(filtered);
  }
  // 分类操作
  async getCategories() {
    const result = await this.getWithMetadata("cloudnav:categories");
    return result?.data || [];
  }
  async setCategories(categories) {
    return await this.set("cloudnav:categories", categories);
  }
  async addCategory(category) {
    const categories = await this.getCategories();
    categories.push(category);
    return await this.setCategories(categories);
  }
  async updateCategory(id, updates) {
    const categories = await this.getCategories();
    const index = categories.findIndex((c) => c.id === id);
    if (index === -1)
      return false;
    categories[index] = { ...categories[index], ...updates };
    return await this.setCategories(categories);
  }
  async deleteCategory(id) {
    const categories = await this.getCategories();
    const filtered = categories.filter((c) => c.id !== id);
    if (filtered.length === categories.length)
      return false;
    return await this.setCategories(filtered);
  }
  // 统计操作
  async getStats() {
    const result = await this.getWithMetadata("cloudnav:stats");
    return result?.data || {
      totalClicks: 0,
      totalViews: 0,
      bookmarkStats: {},
      categoryStats: {},
      searchStats: {},
      dailyStats: {},
      deviceStats: { mobile: 0, desktop: 0, tablet: 0 },
      lastUpdated: Date.now()
    };
  }
  async updateStats(updates) {
    const stats = await this.getStats();
    const updatedStats = { ...stats, ...updates, lastUpdated: Date.now() };
    return await this.set("cloudnav:stats", updatedStats);
  }
  async incrementBookmarkClick(bookmarkId) {
    const stats = await this.getStats();
    stats.totalClicks++;
    stats.bookmarkStats[bookmarkId] = (stats.bookmarkStats[bookmarkId] || 0) + 1;
    const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
    stats.dailyStats[today] = (stats.dailyStats[today] || 0) + 1;
    return await this.updateStats(stats);
  }
  // 配置操作
  async getConfig() {
    const result = await this.getWithMetadata("cloudnav:config");
    return result?.data || {
      siteName: "CloudNav",
      siteDescription: "\u667A\u80FD\u4E66\u7B7E\u5BFC\u822A\u7AD9",
      aiConfig: { enabled: false },
      features: { stats: true, ai: false, import: true, export: true },
      theme: { primaryColor: "#7C3AED", darkMode: false }
    };
  }
  async updateConfig(updates) {
    const config = await this.getConfig();
    const updatedConfig = { ...config, ...updates };
    return await this.set("cloudnav:config", updatedConfig);
  }
  // 数据备份和恢复
  async backup() {
    const [bookmarks, categories, stats, config] = await Promise.all([
      this.getBookmarks(),
      this.getCategories(),
      this.getStats(),
      this.getConfig()
    ]);
    return { bookmarks, categories, stats, config };
  }
  async restore(data) {
    try {
      const promises = [];
      if (data.bookmarks)
        promises.push(this.setBookmarks(data.bookmarks));
      if (data.categories)
        promises.push(this.setCategories(data.categories));
      if (data.stats)
        promises.push(this.updateStats(data.stats));
      if (data.config)
        promises.push(this.updateConfig(data.config));
      await Promise.all(promises);
      return true;
    } catch (error) {
      console.error("Restore error:", error);
      return false;
    }
  }
  // 清理和维护
  async cleanup() {
    try {
      const stats = await this.getStats();
      const thirtyDaysAgo = /* @__PURE__ */ new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      Object.keys(stats.dailyStats).forEach((date) => {
        if (new Date(date) < thirtyDaysAgo) {
          delete stats.dailyStats[date];
        }
      });
      await this.updateStats(stats);
      return true;
    } catch (error) {
      console.error("Cleanup error:", error);
      return false;
    }
  }
};
__name(KVService, "KVService");

// node_modules/nanoid/url-alphabet/index.js
var urlAlphabet = "useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";

// node_modules/nanoid/index.browser.js
var nanoid = /* @__PURE__ */ __name((size = 21) => {
  let id = "";
  let bytes = crypto.getRandomValues(new Uint8Array(size |= 0));
  while (size--) {
    id += urlAlphabet[bytes[size] & 63];
  }
  return id;
}, "nanoid");

// src/services/bookmarks.ts
var BookmarkService = class {
  kvService;
  constructor(kvService) {
    this.kvService = kvService;
  }
  // 获取所有书签
  async getAllBookmarks() {
    return await this.kvService.getBookmarks();
  }
  // 根据ID获取书签
  async getBookmarkById(id) {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.find((bookmark) => bookmark.id === id) || null;
  }
  // 根据分类获取书签
  async getBookmarksByCategory(categoryId) {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.filter((bookmark) => bookmark.category === categoryId);
  }
  // 搜索书签
  async searchBookmarks(query) {
    const bookmarks = await this.getAllBookmarks();
    const searchTerm = query.toLowerCase().trim();
    if (!searchTerm)
      return bookmarks;
    return bookmarks.filter(
      (bookmark) => bookmark.title.toLowerCase().includes(searchTerm) || bookmark.description?.toLowerCase().includes(searchTerm) || bookmark.shortDesc?.toLowerCase().includes(searchTerm) || bookmark.url.toLowerCase().includes(searchTerm) || bookmark.tags?.some((tag) => tag.toLowerCase().includes(searchTerm))
    );
  }
  // 创建新书签
  async createBookmark(bookmarkData) {
    const now = Date.now();
    const newBookmark = {
      id: nanoid(),
      ...bookmarkData,
      createdAt: now,
      updatedAt: now,
      clickCount: 0
    };
    const success = await this.kvService.addBookmark(newBookmark);
    if (!success) {
      throw new Error("Failed to create bookmark");
    }
    return newBookmark;
  }
  // 更新书签
  async updateBookmark(id, updates) {
    const bookmark = await this.getBookmarkById(id);
    if (!bookmark) {
      return null;
    }
    const updatedData = {
      ...updates,
      updatedAt: Date.now()
    };
    const success = await this.kvService.updateBookmark(id, updatedData);
    if (!success) {
      throw new Error("Failed to update bookmark");
    }
    return { ...bookmark, ...updatedData };
  }
  // 删除书签
  async deleteBookmark(id) {
    const bookmark = await this.getBookmarkById(id);
    if (!bookmark) {
      return false;
    }
    return await this.kvService.deleteBookmark(id);
  }
  // 批量删除书签
  async deleteBookmarks(ids) {
    const results = { success: [], failed: [] };
    for (const id of ids) {
      const deleted = await this.deleteBookmark(id);
      if (deleted) {
        results.success.push(id);
      } else {
        results.failed.push(id);
      }
    }
    return results;
  }
  // 移动书签到不同分类
  async moveBookmarkToCategory(bookmarkId, newCategoryId) {
    return await this.updateBookmark(bookmarkId, { category: newCategoryId }) !== null;
  }
  // 批量移动书签
  async moveBookmarksToCategory(bookmarkIds, newCategoryId) {
    const results = { success: [], failed: [] };
    for (const id of bookmarkIds) {
      const moved = await this.moveBookmarkToCategory(id, newCategoryId);
      if (moved) {
        results.success.push(id);
      } else {
        results.failed.push(id);
      }
    }
    return results;
  }
  // 增加点击计数
  async incrementClickCount(id) {
    const bookmark = await this.getBookmarkById(id);
    if (!bookmark) {
      return false;
    }
    const success = await this.kvService.updateBookmark(id, {
      clickCount: bookmark.clickCount + 1,
      updatedAt: Date.now()
    });
    if (success) {
      await this.kvService.incrementBookmarkClick(id);
    }
    return success;
  }
  // 获取热门书签
  async getPopularBookmarks(limit = 10) {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.sort((a, b) => b.clickCount - a.clickCount).slice(0, limit);
  }
  // 获取最近添加的书签
  async getRecentBookmarks(limit = 10) {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.sort((a, b) => b.createdAt - a.createdAt).slice(0, limit);
  }
  // 获取最近更新的书签
  async getRecentlyUpdatedBookmarks(limit = 10) {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.sort((a, b) => b.updatedAt - a.updatedAt).slice(0, limit);
  }
  // 验证书签数据
  validateBookmarkData(data) {
    const errors = [];
    if (!data.title || typeof data.title !== "string" || data.title.trim().length === 0) {
      errors.push("\u6807\u9898\u662F\u5FC5\u9700\u7684");
    }
    if (!data.url || typeof data.url !== "string") {
      errors.push("URL\u662F\u5FC5\u9700\u7684");
    } else {
      try {
        new URL(data.url);
      } catch {
        errors.push("URL\u683C\u5F0F\u65E0\u6548");
      }
    }
    if (!data.category || typeof data.category !== "string") {
      errors.push("\u5206\u7C7B\u662F\u5FC5\u9700\u7684");
    }
    if (data.tags && !Array.isArray(data.tags)) {
      errors.push("\u6807\u7B7E\u5FC5\u987B\u662F\u6570\u7EC4");
    }
    return {
      valid: errors.length === 0,
      errors
    };
  }
  // 检查重复书签
  async checkDuplicateBookmark(url, excludeId) {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.find(
      (bookmark) => bookmark.url === url && bookmark.id !== excludeId
    ) || null;
  }
  // 获取书签统计信息
  async getBookmarkStats() {
    const bookmarks = await this.getAllBookmarks();
    const total = bookmarks.length;
    const totalClicks = bookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);
    const byCategory = {};
    bookmarks.forEach((bookmark) => {
      byCategory[bookmark.category] = (byCategory[bookmark.category] || 0) + 1;
    });
    return {
      total,
      byCategory,
      totalClicks,
      averageClicksPerBookmark: total > 0 ? totalClicks / total : 0
    };
  }
  // 导出书签数据
  async exportBookmarks() {
    return await this.getAllBookmarks();
  }
  // 导入书签数据
  async importBookmarks(bookmarks, options = {}) {
    const result = { imported: 0, skipped: 0, errors: [] };
    for (const bookmark of bookmarks) {
      try {
        const validation = this.validateBookmarkData(bookmark);
        if (!validation.valid) {
          result.errors.push(`\u4E66\u7B7E "${bookmark.title}" \u9A8C\u8BC1\u5931\u8D25: ${validation.errors.join(", ")}`);
          continue;
        }
        if (options.skipDuplicates) {
          const duplicate = await this.checkDuplicateBookmark(bookmark.url);
          if (duplicate) {
            result.skipped++;
            continue;
          }
        }
        await this.createBookmark({
          title: bookmark.title,
          url: bookmark.url,
          description: bookmark.description,
          shortDesc: bookmark.shortDesc,
          category: bookmark.category,
          icon: bookmark.icon,
          tags: bookmark.tags
        });
        result.imported++;
      } catch (error) {
        result.errors.push(`\u5BFC\u5165\u4E66\u7B7E "${bookmark.title}" \u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }
    return result;
  }
};
__name(BookmarkService, "BookmarkService");

// src/services/categories.ts
var CategoryService = class {
  kvService;
  constructor(kvService) {
    this.kvService = kvService;
  }
  // 获取所有分类
  async getAllCategories() {
    const categories = await this.kvService.getCategories();
    return categories.sort((a, b) => a.order - b.order);
  }
  // 根据ID获取分类
  async getCategoryById(id) {
    const categories = await this.getAllCategories();
    return categories.find((category) => category.id === id) || null;
  }
  // 创建新分类
  async createCategory(categoryData) {
    const categories = await this.getAllCategories();
    const maxOrder = categories.length > 0 ? Math.max(...categories.map((c) => c.order)) : 0;
    const newCategory = {
      id: nanoid(),
      ...categoryData,
      order: categoryData.order ?? maxOrder + 1,
      createdAt: Date.now()
    };
    const success = await this.kvService.addCategory(newCategory);
    if (!success) {
      throw new Error("Failed to create category");
    }
    return newCategory;
  }
  // 更新分类
  async updateCategory(id, updates) {
    const category = await this.getCategoryById(id);
    if (!category) {
      return null;
    }
    const success = await this.kvService.updateCategory(id, updates);
    if (!success) {
      throw new Error("Failed to update category");
    }
    return { ...category, ...updates };
  }
  // 删除分类
  async deleteCategory(id, options = {}) {
    const category = await this.getCategoryById(id);
    if (!category) {
      return { success: false };
    }
    const bookmarks = await this.kvService.getBookmarks();
    const categoryBookmarks = bookmarks.filter((bookmark) => bookmark.category === id);
    let movedBookmarks = 0;
    let deletedBookmarks = 0;
    if (categoryBookmarks.length > 0) {
      if (options.moveBookmarksTo) {
        for (const bookmark of categoryBookmarks) {
          const success2 = await this.kvService.updateBookmark(bookmark.id, {
            category: options.moveBookmarksTo,
            updatedAt: Date.now()
          });
          if (success2)
            movedBookmarks++;
        }
      } else if (options.deleteBookmarks) {
        for (const bookmark of categoryBookmarks) {
          const success2 = await this.kvService.deleteBookmark(bookmark.id);
          if (success2)
            deletedBookmarks++;
        }
      } else {
        throw new Error("Cannot delete category with bookmarks. Please specify how to handle existing bookmarks.");
      }
    }
    const success = await this.kvService.deleteCategory(id);
    return {
      success,
      movedBookmarks: movedBookmarks > 0 ? movedBookmarks : void 0,
      deletedBookmarks: deletedBookmarks > 0 ? deletedBookmarks : void 0
    };
  }
  // 重新排序分类
  async reorderCategories(categoryOrders) {
    try {
      for (const { id, order } of categoryOrders) {
        await this.kvService.updateCategory(id, { order });
      }
      return true;
    } catch (error) {
      console.error("Failed to reorder categories:", error);
      return false;
    }
  }
  // 获取分类统计信息
  async getCategoryStats() {
    const [categories, bookmarks, stats] = await Promise.all([
      this.getAllCategories(),
      this.kvService.getBookmarks(),
      this.kvService.getStats()
    ]);
    return categories.map((category) => {
      const categoryBookmarks = bookmarks.filter((bookmark) => bookmark.category === category.id);
      const totalClicks = categoryBookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);
      const lastUpdated = categoryBookmarks.length > 0 ? Math.max(...categoryBookmarks.map((bookmark) => bookmark.updatedAt)) : category.createdAt;
      return {
        category,
        bookmarkCount: categoryBookmarks.length,
        totalClicks,
        lastUpdated
      };
    });
  }
  // 验证分类数据
  validateCategoryData(data) {
    const errors = [];
    if (!data.name || typeof data.name !== "string" || data.name.trim().length === 0) {
      errors.push("\u5206\u7C7B\u540D\u79F0\u662F\u5FC5\u9700\u7684");
    }
    if (data.order !== void 0 && (typeof data.order !== "number" || data.order < 0)) {
      errors.push("\u6392\u5E8F\u5FC5\u987B\u662F\u975E\u8D1F\u6570");
    }
    return {
      valid: errors.length === 0,
      errors
    };
  }
  // 检查分类名称是否重复
  async checkDuplicateCategoryName(name, excludeId) {
    const categories = await this.getAllCategories();
    return categories.find(
      (category) => category.name.toLowerCase() === name.toLowerCase() && category.id !== excludeId
    ) || null;
  }
  // 获取空分类（没有书签的分类）
  async getEmptyCategories() {
    const [categories, bookmarks] = await Promise.all([
      this.getAllCategories(),
      this.kvService.getBookmarks()
    ]);
    return categories.filter(
      (category) => !bookmarks.some((bookmark) => bookmark.category === category.id)
    );
  }
  // 获取热门分类
  async getPopularCategories(limit = 10) {
    const stats = await this.getCategoryStats();
    return stats.sort((a, b) => b.totalClicks - a.totalClicks).slice(0, limit).map(({ category, bookmarkCount, totalClicks }) => ({
      category,
      bookmarkCount,
      totalClicks
    }));
  }
  // 创建默认分类
  async createDefaultCategories() {
    const defaultCategories = [
      { name: "\u5F00\u53D1\u5DE5\u5177", icon: "\u{1F6E0}\uFE0F", description: "\u5F00\u53D1\u76F8\u5173\u7684\u5DE5\u5177\u548C\u8D44\u6E90", order: 1 },
      { name: "\u8BBE\u8BA1\u8D44\u6E90", icon: "\u{1F3A8}", description: "\u8BBE\u8BA1\u5DE5\u5177\u548C\u7D20\u6750", order: 2 },
      { name: "\u5B66\u4E60\u8D44\u6599", icon: "\u{1F4DA}", description: "\u5B66\u4E60\u548C\u6559\u80B2\u76F8\u5173\u8D44\u6E90", order: 3 },
      { name: "\u5A31\u4E50\u4F11\u95F2", icon: "\u{1F3AE}", description: "\u5A31\u4E50\u548C\u4F11\u95F2\u7F51\u7AD9", order: 4 },
      { name: "\u65B0\u95FB\u8D44\u8BAF", icon: "\u{1F4F0}", description: "\u65B0\u95FB\u548C\u8D44\u8BAF\u7F51\u7AD9", order: 5 },
      { name: "\u793E\u4EA4\u5A92\u4F53", icon: "\u{1F4AC}", description: "\u793E\u4EA4\u7F51\u7EDC\u548C\u901A\u8BAF\u5DE5\u5177", order: 6 },
      { name: "\u8D2D\u7269\u7F51\u7AD9", icon: "\u{1F6D2}", description: "\u7535\u5546\u548C\u8D2D\u7269\u5E73\u53F0", order: 7 },
      { name: "\u5176\u4ED6", icon: "\u{1F4C1}", description: "\u5176\u4ED6\u672A\u5206\u7C7B\u7684\u7F51\u7AD9", order: 8 }
    ];
    const createdCategories = [];
    for (const categoryData of defaultCategories) {
      try {
        const existing = await this.checkDuplicateCategoryName(categoryData.name);
        if (!existing) {
          const category = await this.createCategory(categoryData);
          createdCategories.push(category);
        }
      } catch (error) {
        console.error(`Failed to create default category ${categoryData.name}:`, error);
      }
    }
    return createdCategories;
  }
  // 导出分类数据
  async exportCategories() {
    return await this.getAllCategories();
  }
  // 导入分类数据
  async importCategories(categories, options = {}) {
    const result = { imported: 0, skipped: 0, errors: [] };
    for (const category of categories) {
      try {
        const validation = this.validateCategoryData(category);
        if (!validation.valid) {
          result.errors.push(`\u5206\u7C7B "${category.name}" \u9A8C\u8BC1\u5931\u8D25: ${validation.errors.join(", ")}`);
          continue;
        }
        if (options.skipDuplicates) {
          const duplicate = await this.checkDuplicateCategoryName(category.name);
          if (duplicate) {
            result.skipped++;
            continue;
          }
        }
        await this.createCategory({
          name: category.name,
          icon: category.icon,
          description: category.description,
          order: category.order
        });
        result.imported++;
      } catch (error) {
        result.errors.push(`\u5BFC\u5165\u5206\u7C7B "${category.name}" \u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }
    return result;
  }
  // 合并分类
  async mergeCategories(sourceId, targetId) {
    const [sourceCategory, targetCategory] = await Promise.all([
      this.getCategoryById(sourceId),
      this.getCategoryById(targetId)
    ]);
    if (!sourceCategory || !targetCategory) {
      throw new Error("Source or target category not found");
    }
    const bookmarks = await this.kvService.getBookmarks();
    const sourceBookmarks = bookmarks.filter((bookmark) => bookmark.category === sourceId);
    let movedBookmarks = 0;
    for (const bookmark of sourceBookmarks) {
      const success = await this.kvService.updateBookmark(bookmark.id, {
        category: targetId,
        updatedAt: Date.now()
      });
      if (success)
        movedBookmarks++;
    }
    const deleteSuccess = await this.kvService.deleteCategory(sourceId);
    return {
      success: deleteSuccess,
      movedBookmarks
    };
  }
};
__name(CategoryService, "CategoryService");

// src/services/migration.ts
var MigrationService = class {
  kvService;
  bookmarkService;
  categoryService;
  constructor(kvService) {
    this.kvService = kvService;
    this.bookmarkService = new BookmarkService(kvService);
    this.categoryService = new CategoryService(kvService);
  }
  // 从 navLinks.js 迁移数据
  async migrateFromNavLinks(legacyData) {
    const result = {
      success: false,
      categoriesImported: 0,
      bookmarksImported: 0,
      errors: []
    };
    try {
      console.log("\u5F00\u59CB\u6570\u636E\u8FC1\u79FB...");
      console.log("\u8FC1\u79FB\u5206\u7C7B\u6570\u636E...");
      for (let i = 0; i < legacyData.categories.length; i++) {
        const legacyCategory = legacyData.categories[i];
        try {
          const category = {
            name: legacyCategory.name,
            icon: legacyCategory.icon,
            description: `\u4ECE\u539F\u7CFB\u7EDF\u8FC1\u79FB\u7684\u5206\u7C7B: ${legacyCategory.name}`,
            order: i + 1
          };
          const existing = await this.categoryService.checkDuplicateCategoryName(category.name);
          if (!existing) {
            await this.categoryService.createCategory(category);
            result.categoriesImported++;
            console.log(`\u2713 \u5206\u7C7B\u8FC1\u79FB\u6210\u529F: ${category.name}`);
          } else {
            console.log(`- \u5206\u7C7B\u5DF2\u5B58\u5728\uFF0C\u8DF3\u8FC7: ${category.name}`);
          }
        } catch (error) {
          const errorMsg = `\u5206\u7C7B\u8FC1\u79FB\u5931\u8D25 ${legacyCategory.name}: ${error instanceof Error ? error.message : "Unknown error"}`;
          result.errors.push(errorMsg);
          console.error(errorMsg);
        }
      }
      console.log("\u8FC1\u79FB\u4E66\u7B7E\u6570\u636E...");
      for (const legacySite of legacyData.sites) {
        try {
          const bookmark = {
            title: legacySite.title,
            url: legacySite.url,
            description: legacySite.description,
            shortDesc: legacySite.shortDesc,
            category: legacySite.category,
            icon: legacySite.icon,
            tags: []
          };
          const validation = this.bookmarkService.validateBookmarkData(bookmark);
          if (!validation.valid) {
            result.errors.push(`\u4E66\u7B7E\u9A8C\u8BC1\u5931\u8D25 ${bookmark.title}: ${validation.errors.join(", ")}`);
            continue;
          }
          const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmark.url);
          if (!duplicate) {
            await this.bookmarkService.createBookmark(bookmark);
            result.bookmarksImported++;
            console.log(`\u2713 \u4E66\u7B7E\u8FC1\u79FB\u6210\u529F: ${bookmark.title}`);
          } else {
            console.log(`- \u4E66\u7B7E\u5DF2\u5B58\u5728\uFF0C\u8DF3\u8FC7: ${bookmark.title}`);
          }
        } catch (error) {
          const errorMsg = `\u4E66\u7B7E\u8FC1\u79FB\u5931\u8D25 ${legacySite.title}: ${error instanceof Error ? error.message : "Unknown error"}`;
          result.errors.push(errorMsg);
          console.error(errorMsg);
        }
      }
      result.success = true;
      console.log(`\u8FC1\u79FB\u5B8C\u6210: ${result.categoriesImported} \u4E2A\u5206\u7C7B, ${result.bookmarksImported} \u4E2A\u4E66\u7B7E`);
    } catch (error) {
      result.errors.push(`\u8FC1\u79FB\u8FC7\u7A0B\u51FA\u9519: ${error instanceof Error ? error.message : "Unknown error"}`);
      console.error("\u8FC1\u79FB\u5931\u8D25:", error);
    }
    return result;
  }
  // 从当前项目的 navLinks.js 读取数据
  async loadNavLinksData() {
    try {
      const sampleData = {
        categories: [
          { id: "opensource", name: "\u5F00\u6E90\u9879\u76EE", icon: "\u{1F513}" },
          { id: "ai", name: "AI\u5DE5\u5177", icon: "\u{1F916}" },
          { id: "design", name: "\u8BBE\u8BA1\u5DE5\u5177", icon: "\u{1F3A8}" },
          { id: "dev", name: "\u5F00\u53D1\u5DE5\u5177", icon: "\u26A1" },
          { id: "learn", name: "\u5B66\u4E60\u8D44\u6E90", icon: "\u{1F4DA}" },
          { id: "life", name: "\u751F\u6D3B\u670D\u52A1", icon: "\u{1F3E0}" }
        ],
        sites: [
          {
            id: "github",
            title: "GitHub",
            description: "\u5168\u7403\u6700\u5927\u7684\u4EE3\u7801\u6258\u7BA1\u5E73\u53F0",
            shortDesc: "\u4EE3\u7801\u6258\u7BA1",
            url: "https://github.com",
            category: "opensource",
            icon: "/icons/github.webp"
          },
          {
            id: "chatgpt",
            title: "ChatGPT",
            description: "OpenAI\u5F00\u53D1\u7684AI\u804A\u5929\u673A\u5668\u4EBA",
            shortDesc: "AI\u804A\u5929",
            url: "https://chat.openai.com",
            category: "ai",
            icon: "/icons/chatgpt.webp"
          }
        ]
      };
      return sampleData;
    } catch (error) {
      console.error("\u8BFB\u53D6 navLinks \u6570\u636E\u5931\u8D25:", error);
      return null;
    }
  }
  // 执行完整迁移流程
  async performMigration() {
    try {
      const [existingBookmarks, existingCategories] = await Promise.all([
        this.bookmarkService.getAllBookmarks(),
        this.categoryService.getAllCategories()
      ]);
      if (existingBookmarks.length > 0 || existingCategories.length > 0) {
        return {
          success: false,
          message: "\u7CFB\u7EDF\u4E2D\u5DF2\u5B58\u5728\u6570\u636E\uFF0C\u8BF7\u5148\u6E05\u7A7A\u6570\u636E\u6216\u9009\u62E9\u5F3A\u5236\u8FC1\u79FB",
          details: { categoriesImported: 0, bookmarksImported: 0, errors: [] }
        };
      }
      const legacyData = await this.loadNavLinksData();
      if (!legacyData) {
        return {
          success: false,
          message: "\u65E0\u6CD5\u8BFB\u53D6\u539F\u59CB\u6570\u636E\u6587\u4EF6",
          details: { categoriesImported: 0, bookmarksImported: 0, errors: ["\u6570\u636E\u6587\u4EF6\u8BFB\u53D6\u5931\u8D25"] }
        };
      }
      const migrationResult = await this.migrateFromNavLinks(legacyData);
      return {
        success: migrationResult.success,
        message: migrationResult.success ? `\u8FC1\u79FB\u6210\u529F\u5B8C\u6210\uFF01\u5BFC\u5165\u4E86 ${migrationResult.categoriesImported} \u4E2A\u5206\u7C7B\u548C ${migrationResult.bookmarksImported} \u4E2A\u4E66\u7B7E` : "\u8FC1\u79FB\u8FC7\u7A0B\u4E2D\u51FA\u73B0\u9519\u8BEF",
        details: {
          categoriesImported: migrationResult.categoriesImported,
          bookmarksImported: migrationResult.bookmarksImported,
          errors: migrationResult.errors
        }
      };
    } catch (error) {
      return {
        success: false,
        message: "\u8FC1\u79FB\u8FC7\u7A0B\u4E2D\u53D1\u751F\u672A\u77E5\u9519\u8BEF",
        details: {
          categoriesImported: 0,
          bookmarksImported: 0,
          errors: [error instanceof Error ? error.message : "Unknown error"]
        }
      };
    }
  }
  // 清空所有数据
  async clearAllData() {
    try {
      const [bookmarks, categories] = await Promise.all([
        this.bookmarkService.getAllBookmarks(),
        this.categoryService.getAllCategories()
      ]);
      for (const bookmark of bookmarks) {
        await this.bookmarkService.deleteBookmark(bookmark.id);
      }
      for (const category of categories) {
        await this.categoryService.deleteCategory(category.id, { deleteBookmarks: true });
      }
      await this.kvService.updateStats({
        totalClicks: 0,
        totalViews: 0,
        bookmarkStats: {},
        categoryStats: {},
        searchStats: {},
        dailyStats: {},
        deviceStats: { mobile: 0, desktop: 0, tablet: 0 },
        lastUpdated: Date.now()
      });
      return true;
    } catch (error) {
      console.error("\u6E05\u7A7A\u6570\u636E\u5931\u8D25:", error);
      return false;
    }
  }
  // 创建示例数据
  async createSampleData() {
    const result = {
      success: false,
      categoriesCreated: 0,
      bookmarksCreated: 0,
      errors: []
    };
    try {
      const defaultCategories = await this.categoryService.createDefaultCategories();
      result.categoriesCreated = defaultCategories.length;
      const sampleBookmarks = [
        {
          title: "GitHub",
          url: "https://github.com",
          description: "\u5168\u7403\u6700\u5927\u7684\u4EE3\u7801\u6258\u7BA1\u5E73\u53F0\uFF0C\u5F00\u53D1\u8005\u7684\u5FC5\u5907\u5DE5\u5177",
          shortDesc: "\u4EE3\u7801\u6258\u7BA1\u5E73\u53F0",
          category: defaultCategories.find((c) => c.name === "\u5F00\u53D1\u5DE5\u5177")?.id || "dev",
          icon: "https://github.com/favicon.ico",
          tags: ["\u4EE3\u7801", "\u5F00\u6E90", "Git"]
        },
        {
          title: "Figma",
          url: "https://figma.com",
          description: "\u73B0\u4EE3\u5316\u7684\u754C\u9762\u8BBE\u8BA1\u5DE5\u5177\uFF0C\u652F\u6301\u56E2\u961F\u534F\u4F5C",
          shortDesc: "\u754C\u9762\u8BBE\u8BA1\u5DE5\u5177",
          category: defaultCategories.find((c) => c.name === "\u8BBE\u8BA1\u8D44\u6E90")?.id || "design",
          icon: "https://figma.com/favicon.ico",
          tags: ["\u8BBE\u8BA1", "UI", "\u534F\u4F5C"]
        },
        {
          title: "MDN Web Docs",
          url: "https://developer.mozilla.org",
          description: "Web\u5F00\u53D1\u8005\u7684\u6743\u5A01\u53C2\u8003\u6587\u6863",
          shortDesc: "Web\u5F00\u53D1\u6587\u6863",
          category: defaultCategories.find((c) => c.name === "\u5B66\u4E60\u8D44\u6599")?.id || "learn",
          icon: "https://developer.mozilla.org/favicon.ico",
          tags: ["\u6587\u6863", "Web", "\u5B66\u4E60"]
        }
      ];
      for (const bookmarkData of sampleBookmarks) {
        try {
          await this.bookmarkService.createBookmark(bookmarkData);
          result.bookmarksCreated++;
        } catch (error) {
          result.errors.push(`\u521B\u5EFA\u793A\u4F8B\u4E66\u7B7E\u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
        }
      }
      result.success = true;
    } catch (error) {
      result.errors.push(`\u521B\u5EFA\u793A\u4F8B\u6570\u636E\u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
    return result;
  }
  // 获取迁移状态
  async getMigrationStatus() {
    const [bookmarks, categories, config] = await Promise.all([
      this.bookmarkService.getAllBookmarks(),
      this.categoryService.getAllCategories(),
      this.kvService.getConfig()
    ]);
    return {
      hasData: bookmarks.length > 0 || categories.length > 0,
      bookmarkCount: bookmarks.length,
      categoryCount: categories.length,
      lastMigration: void 0
      // 可以从配置中读取上次迁移时间
    };
  }
};
__name(MigrationService, "MigrationService");

// src/services/chrome-bookmarks.ts
var ChromeBookmarkService = class {
  bookmarkService;
  categoryService;
  constructor(bookmarkService, categoryService) {
    this.bookmarkService = bookmarkService;
    this.categoryService = categoryService;
  }
  // 解析 Chrome 书签 JSON 文件
  parseChromeBoomarks(jsonContent) {
    try {
      const data = JSON.parse(jsonContent);
      if (!data.roots || !data.roots.bookmark_bar || !data.roots.other) {
        throw new Error("Invalid Chrome bookmarks format");
      }
      return data;
    } catch (error) {
      console.error("Failed to parse Chrome bookmarks:", error);
      return null;
    }
  }
  // 解析 HTML 书签文件（Netscape 格式）
  parseHtmlBookmarks(htmlContent) {
    try {
      const bookmarks = [];
      const folders = [];
      const lines = htmlContent.split("\n");
      let currentFolder = "";
      let folderStack = [];
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.includes("<DT><H3")) {
          const folderMatch = trimmedLine.match(/>([^<]+)</);
          if (folderMatch) {
            currentFolder = folderMatch[1];
            folderStack.push(currentFolder);
            folders.push(currentFolder);
          }
        } else if (trimmedLine.includes("</DL>")) {
          folderStack.pop();
          currentFolder = folderStack[folderStack.length - 1] || "";
        } else if (trimmedLine.includes("<DT><A HREF=")) {
          const urlMatch = trimmedLine.match(/HREF="([^"]+)"/);
          const titleMatch = trimmedLine.match(/>([^<]+)</);
          const addDateMatch = trimmedLine.match(/ADD_DATE="([^"]+)"/);
          const iconMatch = trimmedLine.match(/ICON="([^"]+)"/);
          if (urlMatch && titleMatch) {
            const url = urlMatch[1];
            const title = titleMatch[1];
            const addDate = addDateMatch ? parseInt(addDateMatch[1]) * 1e3 : Date.now();
            const icon = iconMatch ? iconMatch[1] : void 0;
            bookmarks.push({
              title,
              url,
              description: `\u4ECE HTML \u4E66\u7B7E\u5BFC\u5165: ${title}`,
              shortDesc: title,
              category: currentFolder || "imported",
              tags: currentFolder ? [currentFolder] : ["imported"],
              icon,
              dateAdded: addDate,
              dateModified: addDate
            });
          }
        }
      }
      return { bookmarks, folders };
    } catch (error) {
      console.error("Failed to parse HTML bookmarks:", error);
      return null;
    }
  }
  // 从 Chrome 书签节点提取书签和文件夹
  extractBookmarksFromNode(node, parentPath = "") {
    const bookmarks = [];
    const folders = [];
    if (node.type === "url" && node.url) {
      bookmarks.push({
        title: node.name,
        url: node.url,
        description: `\u4ECE Chrome \u5BFC\u5165\u7684\u4E66\u7B7E`,
        shortDesc: node.name,
        category: parentPath || "imported",
        tags: parentPath ? [parentPath] : ["imported"],
        dateAdded: node.date_added ? parseInt(node.date_added) : Date.now(),
        dateModified: node.date_modified ? parseInt(node.date_modified) : Date.now()
      });
    } else if (node.type === "folder" && node.children) {
      const folderName = node.name;
      const folderPath = parentPath ? `${parentPath}/${folderName}` : folderName;
      folders.push(folderName);
      for (const child of node.children) {
        const childResult = this.extractBookmarksFromNode(child, folderPath);
        bookmarks.push(...childResult.bookmarks);
        folders.push(...childResult.folders);
      }
    }
    return { bookmarks, folders };
  }
  // 通用书签导入（自动检测格式）
  async importBookmarks(content, options = {}) {
    const format = options.format || this.detectFileFormat(content);
    if (format === "json") {
      return this.importChromeBookmarks(content, options);
    } else if (format === "html") {
      return this.importHtmlBookmarks(content, options);
    } else {
      return {
        success: false,
        imported: 0,
        skipped: 0,
        errors: ["\u65E0\u6CD5\u8BC6\u522B\u7684\u6587\u4EF6\u683C\u5F0F\uFF0C\u8BF7\u786E\u4FDD\u662F Chrome JSON \u6216 HTML \u4E66\u7B7E\u6587\u4EF6"]
      };
    }
  }
  // 检测文件格式
  detectFileFormat(content) {
    const trimmedContent = content.trim();
    if (trimmedContent.startsWith("{") && trimmedContent.includes('"roots"')) {
      return "json";
    }
    if (trimmedContent.includes("<!DOCTYPE NETSCAPE-Bookmark-file-1>") || trimmedContent.includes("<DT><A HREF=") || trimmedContent.includes("<H3>")) {
      return "html";
    }
    return "unknown";
  }
  // 导入 Chrome JSON 书签
  async importChromeBookmarks(jsonContent, options = {}) {
    const result = {
      success: false,
      imported: 0,
      skipped: 0,
      errors: []
    };
    try {
      const chromeData = this.parseChromeBoomarks(jsonContent);
      if (!chromeData) {
        result.errors.push("\u65E0\u6CD5\u89E3\u6790 Chrome \u4E66\u7B7E\u6587\u4EF6");
        return result;
      }
      const allBookmarks = [];
      const allFolders = [];
      if (chromeData.roots.bookmark_bar) {
        const barResult = this.extractBookmarksFromNode(chromeData.roots.bookmark_bar, "\u4E66\u7B7E\u680F");
        allBookmarks.push(...barResult.bookmarks);
        allFolders.push(...barResult.folders);
      }
      if (chromeData.roots.other) {
        const otherResult = this.extractBookmarksFromNode(chromeData.roots.other, "\u5176\u4ED6\u4E66\u7B7E");
        allBookmarks.push(...otherResult.bookmarks);
        allFolders.push(...otherResult.folders);
      }
      if (chromeData.roots.synced) {
        const syncedResult = this.extractBookmarksFromNode(chromeData.roots.synced, "\u540C\u6B65\u4E66\u7B7E");
        allBookmarks.push(...syncedResult.bookmarks);
        allFolders.push(...syncedResult.folders);
      }
      const categoryMap = {};
      if (options.createCategories) {
        const uniqueFolders = [...new Set(allFolders)];
        for (const folderName of uniqueFolders) {
          try {
            const existingCategory = await this.categoryService.checkDuplicateCategoryName(folderName);
            if (!existingCategory) {
              const category = await this.categoryService.createCategory({
                name: folderName,
                description: `\u4ECE Chrome \u5BFC\u5165\u7684\u5206\u7C7B: ${folderName}`,
                order: 999
                // 放在最后
              });
              categoryMap[folderName] = category.id;
            } else {
              categoryMap[folderName] = existingCategory.id;
            }
          } catch (error) {
            result.errors.push(`\u521B\u5EFA\u5206\u7C7B\u5931\u8D25: ${folderName}`);
          }
        }
      }
      const existingCategories = await this.categoryService.getAllCategories();
      const defaultCategoryId = options.defaultCategory || existingCategories.find((c) => c.name === "\u5BFC\u5165\u4E66\u7B7E")?.id || existingCategories[0]?.id;
      for (const bookmarkData of allBookmarks) {
        try {
          let categoryId = defaultCategoryId;
          if (options.createCategories && bookmarkData.category) {
            categoryId = categoryMap[bookmarkData.category] || defaultCategoryId;
          }
          const validation = this.bookmarkService.validateBookmarkData({
            ...bookmarkData,
            category: categoryId
          });
          if (!validation.valid) {
            result.errors.push(`\u4E66\u7B7E\u9A8C\u8BC1\u5931\u8D25 "${bookmarkData.title}": ${validation.errors.join(", ")}`);
            continue;
          }
          if (options.skipDuplicates) {
            const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmarkData.url);
            if (duplicate) {
              result.skipped++;
              continue;
            }
          }
          await this.bookmarkService.createBookmark({
            title: bookmarkData.title,
            url: bookmarkData.url,
            description: bookmarkData.description,
            shortDesc: bookmarkData.shortDesc,
            category: categoryId,
            tags: bookmarkData.tags
          });
          result.imported++;
        } catch (error) {
          result.errors.push(`\u5BFC\u5165\u4E66\u7B7E\u5931\u8D25 "${bookmarkData.title}": ${error instanceof Error ? error.message : "Unknown error"}`);
        }
      }
      result.success = true;
      console.log(`Chrome \u4E66\u7B7E\u5BFC\u5165\u5B8C\u6210: ${result.imported} \u4E2A\u4E66\u7B7E, ${result.skipped} \u4E2A\u8DF3\u8FC7, ${result.errors.length} \u4E2A\u9519\u8BEF`);
    } catch (error) {
      result.errors.push(`\u5BFC\u5165\u8FC7\u7A0B\u51FA\u9519: ${error instanceof Error ? error.message : "Unknown error"}`);
      console.error("Chrome bookmarks import failed:", error);
    }
    return result;
  }
  // 导入 HTML 书签
  async importHtmlBookmarks(htmlContent, options = {}) {
    const result = {
      success: false,
      imported: 0,
      skipped: 0,
      errors: []
    };
    try {
      const htmlData = this.parseHtmlBookmarks(htmlContent);
      if (!htmlData) {
        result.errors.push("\u65E0\u6CD5\u89E3\u6790 HTML \u4E66\u7B7E\u6587\u4EF6");
        return result;
      }
      const { bookmarks: allBookmarks, folders: allFolders } = htmlData;
      const categoryMap = {};
      if (options.createCategories) {
        const uniqueFolders = [...new Set(allFolders)];
        for (const folderName of uniqueFolders) {
          try {
            const existingCategory = await this.categoryService.checkDuplicateCategoryName(folderName);
            if (!existingCategory) {
              const category = await this.categoryService.createCategory({
                name: folderName,
                description: `\u4ECE HTML \u4E66\u7B7E\u5BFC\u5165\u7684\u5206\u7C7B: ${folderName}`,
                order: 999
                // 放在最后
              });
              categoryMap[folderName] = category.id;
            } else {
              categoryMap[folderName] = existingCategory.id;
            }
          } catch (error) {
            result.errors.push(`\u521B\u5EFA\u5206\u7C7B\u5931\u8D25: ${folderName}`);
          }
        }
      }
      const existingCategories = await this.categoryService.getAllCategories();
      const defaultCategoryId = options.defaultCategory || existingCategories.find((c) => c.name === "\u5BFC\u5165\u4E66\u7B7E")?.id || existingCategories[0]?.id;
      for (const bookmarkData of allBookmarks) {
        try {
          let categoryId = defaultCategoryId;
          if (options.createCategories && bookmarkData.category) {
            categoryId = categoryMap[bookmarkData.category] || defaultCategoryId;
          }
          const validation = this.bookmarkService.validateBookmarkData({
            ...bookmarkData,
            category: categoryId
          });
          if (!validation.valid) {
            result.errors.push(`\u4E66\u7B7E\u9A8C\u8BC1\u5931\u8D25 "${bookmarkData.title}": ${validation.errors.join(", ")}`);
            continue;
          }
          if (options.skipDuplicates) {
            const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmarkData.url);
            if (duplicate) {
              result.skipped++;
              continue;
            }
          }
          await this.bookmarkService.createBookmark({
            title: bookmarkData.title,
            url: bookmarkData.url,
            description: bookmarkData.description,
            shortDesc: bookmarkData.shortDesc,
            category: categoryId,
            icon: bookmarkData.icon,
            tags: bookmarkData.tags
          });
          result.imported++;
        } catch (error) {
          result.errors.push(`\u5BFC\u5165\u4E66\u7B7E\u5931\u8D25 "${bookmarkData.title}": ${error instanceof Error ? error.message : "Unknown error"}`);
        }
      }
      result.success = true;
      console.log(`HTML \u4E66\u7B7E\u5BFC\u5165\u5B8C\u6210: ${result.imported} \u4E2A\u4E66\u7B7E, ${result.skipped} \u4E2A\u8DF3\u8FC7, ${result.errors.length} \u4E2A\u9519\u8BEF`);
    } catch (error) {
      result.errors.push(`\u5BFC\u5165\u8FC7\u7A0B\u51FA\u9519: ${error instanceof Error ? error.message : "Unknown error"}`);
      console.error("HTML bookmarks import failed:", error);
    }
    return result;
  }
  // 导出为 Chrome 书签格式
  async exportToChromeFormat() {
    try {
      const [bookmarks, categories] = await Promise.all([
        this.bookmarkService.getAllBookmarks(),
        this.categoryService.getAllCategories()
      ]);
      const bookmarksByCategory = {};
      bookmarks.forEach((bookmark) => {
        if (!bookmarksByCategory[bookmark.category]) {
          bookmarksByCategory[bookmark.category] = [];
        }
        bookmarksByCategory[bookmark.category].push(bookmark);
      });
      const chromeBookmarks = {
        checksum: this.generateChecksum(),
        roots: {
          bookmark_bar: {
            date_added: Date.now().toString(),
            date_modified: Date.now().toString(),
            guid: this.generateGuid(),
            id: "1",
            name: "CloudNav \u4E66\u7B7E",
            type: "folder",
            children: []
          },
          other: {
            date_added: Date.now().toString(),
            date_modified: Date.now().toString(),
            guid: this.generateGuid(),
            id: "2",
            name: "\u5176\u4ED6\u4E66\u7B7E",
            type: "folder",
            children: []
          },
          synced: {
            date_added: Date.now().toString(),
            date_modified: Date.now().toString(),
            guid: this.generateGuid(),
            id: "3",
            name: "\u79FB\u52A8\u8BBE\u5907\u4E66\u7B7E",
            type: "folder",
            children: []
          }
        },
        version: 1
      };
      let idCounter = 4;
      categories.forEach((category) => {
        const categoryBookmarks = bookmarksByCategory[category.id] || [];
        if (categoryBookmarks.length > 0) {
          const folderNode = {
            date_added: category.createdAt.toString(),
            date_modified: Date.now().toString(),
            guid: this.generateGuid(),
            id: (idCounter++).toString(),
            name: category.name,
            type: "folder",
            children: categoryBookmarks.map((bookmark) => ({
              date_added: bookmark.createdAt.toString(),
              date_modified: bookmark.updatedAt.toString(),
              guid: this.generateGuid(),
              id: (idCounter++).toString(),
              name: bookmark.title,
              type: "url",
              url: bookmark.url
            }))
          };
          chromeBookmarks.roots.bookmark_bar.children.push(folderNode);
        }
      });
      return JSON.stringify(chromeBookmarks, null, 2);
    } catch (error) {
      console.error("Export to Chrome format failed:", error);
      throw new Error("\u5BFC\u51FA Chrome \u4E66\u7B7E\u683C\u5F0F\u5931\u8D25");
    }
  }
  // 导出为 Netscape 书签格式（通用格式）
  async exportToNetscapeFormat() {
    try {
      const [bookmarks, categories] = await Promise.all([
        this.bookmarkService.getAllBookmarks(),
        this.categoryService.getAllCategories()
      ]);
      let html = `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<!-- This is an automatically generated file.
     It will be read and overwritten.
     DO NOT EDIT! -->
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>CloudNav \u4E66\u7B7E</TITLE>
<H1>CloudNav \u4E66\u7B7E</H1>
<DL><p>
`;
      const bookmarksByCategory = {};
      bookmarks.forEach((bookmark) => {
        if (!bookmarksByCategory[bookmark.category]) {
          bookmarksByCategory[bookmark.category] = [];
        }
        bookmarksByCategory[bookmark.category].push(bookmark);
      });
      categories.forEach((category) => {
        const categoryBookmarks = bookmarksByCategory[category.id] || [];
        if (categoryBookmarks.length > 0) {
          html += `    <DT><H3 ADD_DATE="${Math.floor(category.createdAt / 1e3)}" LAST_MODIFIED="${Math.floor(Date.now() / 1e3)}">${this.escapeHtml(category.name)}</H3>
`;
          html += `    <DL><p>
`;
          categoryBookmarks.forEach((bookmark) => {
            const addDate = Math.floor(bookmark.createdAt / 1e3);
            const lastModified = Math.floor(bookmark.updatedAt / 1e3);
            html += `        <DT><A HREF="${this.escapeHtml(bookmark.url)}" ADD_DATE="${addDate}" LAST_MODIFIED="${lastModified}"`;
            if (bookmark.icon) {
              html += ` ICON="${this.escapeHtml(bookmark.icon)}"`;
            }
            html += `>${this.escapeHtml(bookmark.title)}</A>
`;
            if (bookmark.description) {
              html += `        <DD>${this.escapeHtml(bookmark.description)}
`;
            }
          });
          html += `    </DL><p>
`;
        }
      });
      html += `</DL><p>
`;
      return html;
    } catch (error) {
      console.error("Export to Netscape format failed:", error);
      throw new Error("\u5BFC\u51FA Netscape \u4E66\u7B7E\u683C\u5F0F\u5931\u8D25");
    }
  }
  // 生成校验和
  generateChecksum() {
    return Math.random().toString(36).substring(2, 15);
  }
  // 生成 GUID
  generateGuid() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === "x" ? r : r & 3 | 8;
      return v.toString(16);
    });
  }
  // HTML 转义（Workers 环境兼容）
  escapeHtml(text) {
    return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#39;");
  }
  // 获取导入统计信息
  async getImportStats() {
    try {
      const bookmarks = await this.bookmarkService.getAllBookmarks();
      const importedBookmarks = bookmarks.filter(
        (b) => b.tags?.includes("imported") || b.description?.includes("\u4ECE Chrome \u5BFC\u5165")
      );
      return {
        totalImported: importedBookmarks.length,
        lastImportDate: importedBookmarks.length > 0 ? Math.max(...importedBookmarks.map((b) => b.createdAt)) : void 0,
        importSources: ["Chrome", "Netscape", "Manual"]
      };
    } catch (error) {
      console.error("Get import stats failed:", error);
      return {
        totalImported: 0,
        importSources: []
      };
    }
  }
};
__name(ChromeBookmarkService, "ChromeBookmarkService");

// src/services/ai.ts
var AIService = class {
  bookmarkService;
  categoryService;
  aiConfig;
  constructor(bookmarkService, categoryService, aiConfig) {
    this.bookmarkService = bookmarkService;
    this.categoryService = categoryService;
    this.aiConfig = aiConfig;
  }
  // 智能分析书签
  async analyzeBookmarks(options) {
    const [bookmarks, categories] = await Promise.all([
      this.bookmarkService.getAllBookmarks(),
      this.categoryService.getAllCategories()
    ]);
    const result = {
      suggestions: {
        categoryRecommendations: [],
        duplicateDetection: [],
        descriptionSuggestions: [],
        tagSuggestions: [],
        newCategorySuggestions: []
      },
      statistics: {
        totalAnalyzed: bookmarks.length,
        categorySuggestions: 0,
        duplicatesFound: 0,
        descriptionSuggestions: 0,
        tagSuggestions: 0
      }
    };
    if (options.enableCategoryRecommendations) {
      result.suggestions.categoryRecommendations = await this.analyzeCategoryRecommendations(
        bookmarks,
        categories,
        options.confidenceThreshold
      );
      result.statistics.categorySuggestions = result.suggestions.categoryRecommendations.length;
    }
    if (options.enableDuplicateDetection) {
      result.suggestions.duplicateDetection = await this.detectDuplicates(
        bookmarks,
        options.confidenceThreshold
      );
      result.statistics.duplicatesFound = result.suggestions.duplicateDetection.length;
    }
    if (options.enableDescriptionGeneration) {
      result.suggestions.descriptionSuggestions = await this.generateDescriptions(
        bookmarks,
        options.confidenceThreshold
      );
      result.statistics.descriptionSuggestions = result.suggestions.descriptionSuggestions.length;
    }
    if (options.enableTagSuggestions) {
      result.suggestions.tagSuggestions = await this.suggestTags(
        bookmarks,
        options.confidenceThreshold
      );
      result.statistics.tagSuggestions = result.suggestions.tagSuggestions.length;
    }
    if (options.enableNewCategoryCreation) {
      result.suggestions.newCategorySuggestions = await this.suggestNewCategories(
        bookmarks,
        categories,
        options.confidenceThreshold
      );
    }
    return result;
  }
  // 分析分类推荐
  async analyzeCategoryRecommendations(bookmarks, categories, threshold) {
    const recommendations = [];
    for (const bookmark of bookmarks) {
      const suggestion = this.suggestCategoryForBookmark(bookmark, categories);
      if (suggestion && suggestion.confidence >= threshold && suggestion.suggestedCategory !== bookmark.category) {
        recommendations.push({
          bookmarkId: bookmark.id,
          currentCategory: bookmark.category,
          suggestedCategory: suggestion.suggestedCategory,
          confidence: suggestion.confidence,
          reason: suggestion.reason
        });
      }
    }
    return recommendations;
  }
  // 为单个书签建议分类
  suggestCategoryForBookmark(bookmark, categories) {
    const url = bookmark.url.toLowerCase();
    const title = bookmark.title.toLowerCase();
    const description = (bookmark.description || "").toLowerCase();
    const domainRules = [
      { domains: ["github.com", "gitlab.com", "bitbucket.org"], category: "\u5F00\u53D1\u5DE5\u5177", confidence: 0.9 },
      { domains: ["stackoverflow.com", "stackexchange.com"], category: "\u6280\u672F\u95EE\u7B54", confidence: 0.9 },
      { domains: ["youtube.com", "youtu.be"], category: "\u89C6\u9891", confidence: 0.9 },
      { domains: ["twitter.com", "facebook.com", "instagram.com"], category: "\u793E\u4EA4\u5A92\u4F53", confidence: 0.8 },
      { domains: ["amazon.com", "taobao.com", "jd.com"], category: "\u8D2D\u7269", confidence: 0.8 },
      { domains: ["news.", "bbc.com", "cnn.com"], category: "\u65B0\u95FB", confidence: 0.8 },
      { domains: ["wikipedia.org"], category: "\u767E\u79D1", confidence: 0.9 },
      { domains: ["docs.", "documentation"], category: "\u6587\u6863", confidence: 0.8 }
    ];
    const keywordRules = [
      { keywords: ["api", "documentation", "docs"], category: "\u6587\u6863", confidence: 0.7 },
      { keywords: ["tutorial", "guide", "how to"], category: "\u6559\u7A0B", confidence: 0.7 },
      { keywords: ["tool", "utility", "generator"], category: "\u5DE5\u5177", confidence: 0.7 },
      { keywords: ["blog", "article"], category: "\u535A\u5BA2", confidence: 0.6 },
      { keywords: ["design", "ui", "ux"], category: "\u8BBE\u8BA1", confidence: 0.7 },
      { keywords: ["game", "gaming"], category: "\u6E38\u620F", confidence: 0.8 }
    ];
    let bestMatch = null;
    let maxConfidence = 0;
    for (const rule of domainRules) {
      for (const domain of rule.domains) {
        if (url.includes(domain)) {
          const category = categories.find((c) => c.name.includes(rule.category));
          if (category && rule.confidence > maxConfidence) {
            bestMatch = {
              suggestedCategory: category.id,
              confidence: rule.confidence,
              reason: `\u57FA\u4E8E\u57DF\u540D ${domain} \u7684\u667A\u80FD\u5206\u7C7B`
            };
            maxConfidence = rule.confidence;
          }
        }
      }
    }
    for (const rule of keywordRules) {
      for (const keyword of rule.keywords) {
        if (title.includes(keyword) || description.includes(keyword)) {
          const category = categories.find((c) => c.name.includes(rule.category));
          if (category && rule.confidence > maxConfidence) {
            bestMatch = {
              suggestedCategory: category.id,
              confidence: rule.confidence,
              reason: `\u57FA\u4E8E\u5173\u952E\u8BCD "${keyword}" \u7684\u667A\u80FD\u5206\u7C7B`
            };
            maxConfidence = rule.confidence;
          }
        }
      }
    }
    return bestMatch;
  }
  // 检测重复书签
  async detectDuplicates(bookmarks, threshold) {
    const duplicates = [];
    const processed = /* @__PURE__ */ new Set();
    for (let i = 0; i < bookmarks.length; i++) {
      if (processed.has(bookmarks[i].id))
        continue;
      const similarBookmarks = [bookmarks[i]];
      for (let j = i + 1; j < bookmarks.length; j++) {
        if (processed.has(bookmarks[j].id))
          continue;
        const similarity = this.calculateSimilarity(bookmarks[i], bookmarks[j]);
        if (similarity >= threshold) {
          similarBookmarks.push(bookmarks[j]);
          processed.add(bookmarks[j].id);
        }
      }
      if (similarBookmarks.length > 1) {
        duplicates.push({
          bookmarks: similarBookmarks,
          similarity: this.calculateGroupSimilarity(similarBookmarks),
          reason: this.getDuplicateReason(similarBookmarks)
        });
        similarBookmarks.forEach((b) => processed.add(b.id));
      }
    }
    return duplicates;
  }
  // 计算两个书签的相似度
  calculateSimilarity(bookmark1, bookmark2) {
    if (bookmark1.url === bookmark2.url) {
      return 1;
    }
    const titleSimilarity = this.calculateTextSimilarity(bookmark1.title, bookmark2.title);
    const url1Clean = this.cleanUrl(bookmark1.url);
    const url2Clean = this.cleanUrl(bookmark2.url);
    const urlSimilarity = this.calculateTextSimilarity(url1Clean, url2Clean);
    return titleSimilarity * 0.4 + urlSimilarity * 0.6;
  }
  // 计算文本相似度（简单的 Jaccard 相似度）
  calculateTextSimilarity(text1, text2) {
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    const intersection = new Set([...words1].filter((x) => words2.has(x)));
    const union = /* @__PURE__ */ new Set([...words1, ...words2]);
    return intersection.size / union.size;
  }
  // 清理 URL
  cleanUrl(url) {
    try {
      const urlObj = new URL(url);
      return `${urlObj.hostname}${urlObj.pathname}`;
    } catch {
      return url;
    }
  }
  // 计算组相似度
  calculateGroupSimilarity(bookmarks) {
    if (bookmarks.length < 2)
      return 0;
    let totalSimilarity = 0;
    let comparisons = 0;
    for (let i = 0; i < bookmarks.length; i++) {
      for (let j = i + 1; j < bookmarks.length; j++) {
        totalSimilarity += this.calculateSimilarity(bookmarks[i], bookmarks[j]);
        comparisons++;
      }
    }
    return comparisons > 0 ? totalSimilarity / comparisons : 0;
  }
  // 获取重复原因
  getDuplicateReason(bookmarks) {
    if (bookmarks.length < 2)
      return "";
    const urls2 = bookmarks.map((b) => b.url);
    const uniqueUrls = new Set(urls2);
    if (uniqueUrls.size < urls2.length) {
      return "\u53D1\u73B0\u5B8C\u5168\u76F8\u540C\u7684 URL";
    }
    const titles = bookmarks.map((b) => b.title);
    const titleSimilarity = this.calculateTextSimilarity(titles[0], titles[1]);
    if (titleSimilarity > 0.8) {
      return "\u6807\u9898\u9AD8\u5EA6\u76F8\u4F3C";
    }
    return "URL \u548C\u5185\u5BB9\u76F8\u4F3C";
  }
  // 生成描述建议
  async generateDescriptions(bookmarks, threshold) {
    const suggestions = [];
    for (const bookmark of bookmarks) {
      if (!bookmark.description || bookmark.description.length < 10) {
        const suggestion = this.generateDescriptionForBookmark(bookmark);
        if (suggestion && suggestion.confidence >= threshold) {
          suggestions.push({
            bookmarkId: bookmark.id,
            currentDescription: bookmark.description || "",
            suggestedDescription: suggestion.description,
            confidence: suggestion.confidence
          });
        }
      }
    }
    return suggestions;
  }
  // 为单个书签生成描述
  generateDescriptionForBookmark(bookmark) {
    const url = bookmark.url.toLowerCase();
    const title = bookmark.title;
    const domainDescriptions = {
      "github.com": "\u5F00\u6E90\u4EE3\u7801\u4ED3\u5E93\u548C\u534F\u4F5C\u5E73\u53F0",
      "stackoverflow.com": "\u7A0B\u5E8F\u5458\u95EE\u7B54\u793E\u533A",
      "youtube.com": "\u89C6\u9891\u5206\u4EAB\u5E73\u53F0",
      "wikipedia.org": "\u5728\u7EBF\u767E\u79D1\u5168\u4E66",
      "twitter.com": "\u793E\u4EA4\u5A92\u4F53\u5E73\u53F0",
      "linkedin.com": "\u804C\u4E1A\u793E\u4EA4\u7F51\u7EDC",
      "medium.com": "\u5728\u7EBF\u53D1\u5E03\u5E73\u53F0",
      "dev.to": "\u5F00\u53D1\u8005\u793E\u533A"
    };
    for (const [domain, description] of Object.entries(domainDescriptions)) {
      if (url.includes(domain)) {
        return {
          description: `${title} - ${description}`,
          confidence: 0.8
        };
      }
    }
    if (url.includes("/docs/") || url.includes("/documentation/")) {
      return {
        description: `${title} - \u6280\u672F\u6587\u6863\u548C\u4F7F\u7528\u6307\u5357`,
        confidence: 0.7
      };
    }
    if (url.includes("/blog/") || url.includes("/article/")) {
      return {
        description: `${title} - \u535A\u5BA2\u6587\u7AE0`,
        confidence: 0.7
      };
    }
    if (url.includes("/tutorial/") || url.includes("/guide/")) {
      return {
        description: `${title} - \u6559\u7A0B\u6307\u5357`,
        confidence: 0.7
      };
    }
    return {
      description: `${title} - \u7F51\u9875\u4E66\u7B7E`,
      confidence: 0.5
    };
  }
  // 建议标签
  async suggestTags(bookmarks, threshold) {
    const suggestions = [];
    for (const bookmark of bookmarks) {
      const tags = this.generateTagsForBookmark(bookmark);
      if (tags && tags.confidence >= threshold) {
        suggestions.push({
          bookmarkId: bookmark.id,
          suggestedTags: tags.tags,
          confidence: tags.confidence
        });
      }
    }
    return suggestions;
  }
  // 为单个书签生成标签
  generateTagsForBookmark(bookmark) {
    const url = bookmark.url.toLowerCase();
    const title = bookmark.title.toLowerCase();
    const tags = [];
    let confidence = 0;
    const domainTags = {
      "github.com": ["\u5F00\u6E90", "\u4EE3\u7801", "Git"],
      "stackoverflow.com": ["\u7F16\u7A0B", "\u95EE\u7B54", "\u6280\u672F"],
      "youtube.com": ["\u89C6\u9891", "\u5A31\u4E50"],
      "twitter.com": ["\u793E\u4EA4", "\u65B0\u95FB"],
      "linkedin.com": ["\u804C\u4E1A", "\u793E\u4EA4", "\u6C42\u804C"],
      "medium.com": ["\u535A\u5BA2", "\u6587\u7AE0"],
      "wikipedia.org": ["\u767E\u79D1", "\u77E5\u8BC6"]
    };
    for (const [domain, domainTagList] of Object.entries(domainTags)) {
      if (url.includes(domain)) {
        tags.push(...domainTagList);
        confidence = Math.max(confidence, 0.8);
      }
    }
    const keywordTags = {
      "api": ["API", "\u63A5\u53E3", "\u5F00\u53D1"],
      "tutorial": ["\u6559\u7A0B", "\u5B66\u4E60"],
      "documentation": ["\u6587\u6863", "\u53C2\u8003"],
      "tool": ["\u5DE5\u5177", "\u5B9E\u7528"],
      "framework": ["\u6846\u67B6", "\u5F00\u53D1"],
      "library": ["\u5E93", "\u5F00\u53D1"],
      "design": ["\u8BBE\u8BA1", "UI"],
      "css": ["CSS", "\u6837\u5F0F", "\u524D\u7AEF"],
      "javascript": ["JavaScript", "JS", "\u524D\u7AEF"],
      "python": ["Python", "\u7F16\u7A0B"],
      "react": ["React", "\u524D\u7AEF", "\u6846\u67B6"]
    };
    for (const [keyword, keywordTagList] of Object.entries(keywordTags)) {
      if (title.includes(keyword) || url.includes(keyword)) {
        tags.push(...keywordTagList);
        confidence = Math.max(confidence, 0.7);
      }
    }
    const uniqueTags = [...new Set(tags)];
    return uniqueTags.length > 0 ? {
      tags: uniqueTags,
      confidence
    } : null;
  }
  // 建议新分类
  async suggestNewCategories(bookmarks, categories, threshold) {
    const suggestions = [];
    const uncategorizedBookmarks = bookmarks.filter((b) => {
      const category = categories.find((c) => c.id === b.category);
      return !category || category.name === "\u672A\u5206\u7C7B" || category.name === "\u5176\u4ED6";
    });
    const domainGroups = this.groupBookmarksByDomain(uncategorizedBookmarks);
    for (const [domain, domainBookmarks] of Object.entries(domainGroups)) {
      if (domainBookmarks.length >= 3) {
        const categoryName = this.suggestCategoryNameForDomain(domain);
        if (categoryName) {
          suggestions.push({
            name: categoryName,
            description: `\u57FA\u4E8E ${domain} \u57DF\u540D\u7684\u81EA\u52A8\u5206\u7C7B`,
            bookmarkIds: domainBookmarks.map((b) => b.id),
            confidence: 0.8
          });
        }
      }
    }
    return suggestions;
  }
  // 按域名分组书签
  groupBookmarksByDomain(bookmarks) {
    const groups = {};
    for (const bookmark of bookmarks) {
      try {
        const domain = new URL(bookmark.url).hostname;
        if (!groups[domain]) {
          groups[domain] = [];
        }
        groups[domain].push(bookmark);
      } catch {
      }
    }
    return groups;
  }
  // 为域名建议分类名称
  suggestCategoryNameForDomain(domain) {
    const domainCategories = {
      "github.com": "\u5F00\u6E90\u9879\u76EE",
      "stackoverflow.com": "\u6280\u672F\u95EE\u7B54",
      "youtube.com": "\u89C6\u9891\u8D44\u6E90",
      "medium.com": "\u6280\u672F\u535A\u5BA2",
      "dev.to": "\u5F00\u53D1\u793E\u533A",
      "twitter.com": "\u793E\u4EA4\u5A92\u4F53",
      "linkedin.com": "\u804C\u4E1A\u7F51\u7EDC"
    };
    return domainCategories[domain] || null;
  }
  // 应用 AI 建议
  async applyAISuggestions(analysisResult, selectedSuggestions) {
    let applied = 0;
    const errors = [];
    try {
      if (selectedSuggestions.categoryRecommendations) {
        for (const suggestionId of selectedSuggestions.categoryRecommendations) {
          const suggestion = analysisResult.suggestions.categoryRecommendations.find(
            (s) => `${s.bookmarkId}-${s.suggestedCategory}` === suggestionId
          );
          if (suggestion) {
            try {
              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {
                category: suggestion.suggestedCategory
              });
              applied++;
            } catch (error) {
              errors.push(`\u66F4\u65B0\u4E66\u7B7E\u5206\u7C7B\u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
            }
          }
        }
      }
      if (selectedSuggestions.descriptionUpdates) {
        for (const suggestionId of selectedSuggestions.descriptionUpdates) {
          const suggestion = analysisResult.suggestions.descriptionSuggestions.find(
            (s) => s.bookmarkId === suggestionId
          );
          if (suggestion) {
            try {
              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {
                description: suggestion.suggestedDescription
              });
              applied++;
            } catch (error) {
              errors.push(`\u66F4\u65B0\u4E66\u7B7E\u63CF\u8FF0\u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
            }
          }
        }
      }
      if (selectedSuggestions.tagUpdates) {
        for (const suggestionId of selectedSuggestions.tagUpdates) {
          const suggestion = analysisResult.suggestions.tagSuggestions.find(
            (s) => s.bookmarkId === suggestionId
          );
          if (suggestion) {
            try {
              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {
                tags: suggestion.suggestedTags
              });
              applied++;
            } catch (error) {
              errors.push(`\u66F4\u65B0\u4E66\u7B7E\u6807\u7B7E\u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
            }
          }
        }
      }
      if (selectedSuggestions.newCategories) {
        for (const suggestionId of selectedSuggestions.newCategories) {
          const suggestion = analysisResult.suggestions.newCategorySuggestions.find(
            (s) => s.name === suggestionId
          );
          if (suggestion) {
            try {
              const newCategory = await this.categoryService.createCategory({
                name: suggestion.name,
                description: suggestion.description
              });
              for (const bookmarkId of suggestion.bookmarkIds) {
                await this.bookmarkService.updateBookmark(bookmarkId, {
                  category: newCategory.id
                });
              }
              applied++;
            } catch (error) {
              errors.push(`\u521B\u5EFA\u65B0\u5206\u7C7B\u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
            }
          }
        }
      }
      if (selectedSuggestions.duplicateRemovals) {
        for (const suggestionId of selectedSuggestions.duplicateRemovals) {
          try {
            await this.bookmarkService.deleteBookmark(suggestionId);
            applied++;
          } catch (error) {
            errors.push(`\u5220\u9664\u91CD\u590D\u4E66\u7B7E\u5931\u8D25: ${error instanceof Error ? error.message : "Unknown error"}`);
          }
        }
      }
    } catch (error) {
      errors.push(`\u5E94\u7528 AI \u5EFA\u8BAE\u65F6\u51FA\u9519: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
    return { applied, errors };
  }
  // 获取 AI 配置
  getAIConfig() {
    return this.aiConfig;
  }
  // 更新 AI 配置
  updateAIConfig(config) {
    this.aiConfig = { ...this.aiConfig, ...config };
  }
};
__name(AIService, "AIService");

// src/services/statistics.ts
var StatisticsService = class {
  kvService;
  bookmarkService;
  categoryService;
  constructor(kvService) {
    this.kvService = kvService;
    this.bookmarkService = new BookmarkService(kvService);
    this.categoryService = new CategoryService(kvService);
  }
  // 获取详细统计数据
  async getDetailedStats() {
    const [stats, bookmarks, categories] = await Promise.all([
      this.kvService.getStats(),
      this.bookmarkService.getAllBookmarks(),
      this.categoryService.getAllCategories()
    ]);
    return {
      overview: this.calculateOverviewStats(stats, bookmarks, categories),
      bookmarkStats: this.calculateBookmarkStats(stats, bookmarks, categories),
      categoryStats: this.calculateCategoryStats(stats, bookmarks, categories),
      searchStats: this.calculateSearchStats(stats),
      timeStats: this.calculateTimeStats(stats),
      deviceStats: this.calculateDeviceStats(stats),
      performanceStats: this.calculatePerformanceStats()
    };
  }
  // 计算概览统计
  calculateOverviewStats(stats, bookmarks, categories) {
    const totalClicks = bookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);
    return {
      totalBookmarks: bookmarks.length,
      totalCategories: categories.length,
      totalClicks,
      totalViews: stats.totalViews,
      averageClicksPerBookmark: bookmarks.length > 0 ? totalClicks / bookmarks.length : 0
    };
  }
  // 计算书签统计
  calculateBookmarkStats(stats, bookmarks, categories) {
    const topBookmarks = bookmarks.sort((a, b) => b.clickCount - a.clickCount).slice(0, 10).map((bookmark) => ({
      bookmark,
      clicks: bookmark.clickCount,
      clickRate: stats.totalClicks > 0 ? bookmark.clickCount / stats.totalClicks * 100 : 0
    }));
    const recentBookmarks = bookmarks.sort((a, b) => b.createdAt - a.createdAt).slice(0, 10);
    const bookmarksByCategory = {};
    bookmarks.forEach((bookmark) => {
      bookmarksByCategory[bookmark.category] = (bookmarksByCategory[bookmark.category] || 0) + 1;
    });
    return {
      topBookmarks,
      recentBookmarks,
      bookmarksByCategory
    };
  }
  // 计算分类统计
  calculateCategoryStats(stats, bookmarks, categories) {
    const categoryClickStats = {};
    bookmarks.forEach((bookmark) => {
      categoryClickStats[bookmark.category] = (categoryClickStats[bookmark.category] || 0) + bookmark.clickCount;
    });
    const topCategories = categories.map((category) => {
      const bookmarkCount = bookmarks.filter((b) => b.category === category.id).length;
      const totalClicks = categoryClickStats[category.id] || 0;
      return {
        category,
        bookmarkCount,
        totalClicks,
        averageClicks: bookmarkCount > 0 ? totalClicks / bookmarkCount : 0
      };
    }).sort((a, b) => b.totalClicks - a.totalClicks).slice(0, 10);
    const totalBookmarks = bookmarks.length;
    const categoryDistribution = categories.map((category) => {
      const count = bookmarks.filter((b) => b.category === category.id).length;
      return {
        categoryName: category.name,
        count,
        percentage: totalBookmarks > 0 ? count / totalBookmarks * 100 : 0
      };
    }).sort((a, b) => b.count - a.count);
    return {
      topCategories,
      categoryDistribution
    };
  }
  // 计算搜索统计
  calculateSearchStats(stats) {
    const searchEntries = Object.entries(stats.searchStats);
    const totalSearches = searchEntries.reduce((sum, [, count]) => sum + count, 0);
    const topSearches = searchEntries.sort(([, a], [, b]) => b - a).slice(0, 20).map(([query, count]) => ({
      query,
      count,
      percentage: totalSearches > 0 ? count / totalSearches * 100 : 0
    }));
    return {
      topSearches,
      totalSearches,
      uniqueSearches: searchEntries.length
    };
  }
  // 计算时间统计
  calculateTimeStats(stats) {
    const dailyStats = stats.dailyStats;
    const weeklyTrend = this.aggregateByWeek(dailyStats);
    const monthlyTrend = this.aggregateByMonth(dailyStats);
    return {
      dailyStats,
      weeklyTrend,
      monthlyTrend
    };
  }
  // 计算设备统计
  calculateDeviceStats(stats) {
    const { mobile, desktop, tablet } = stats.deviceStats;
    const total = mobile + desktop + tablet;
    return {
      mobile,
      desktop,
      tablet,
      mobilePercentage: total > 0 ? mobile / total * 100 : 0,
      desktopPercentage: total > 0 ? desktop / total * 100 : 0,
      tabletPercentage: total > 0 ? tablet / total * 100 : 0
    };
  }
  // 计算性能统计
  calculatePerformanceStats() {
    return {
      averageLoadTime: void 0,
      errorRate: void 0,
      uptime: void 0
    };
  }
  // 按周聚合数据
  aggregateByWeek(dailyStats) {
    const weeklyData = {};
    Object.entries(dailyStats).forEach(([date, clicks]) => {
      const weekStart = this.getWeekStart(new Date(date));
      const weekKey = weekStart.toISOString().split("T")[0];
      if (!weeklyData[weekKey]) {
        weeklyData[weekKey] = { clicks: 0, views: 0 };
      }
      weeklyData[weekKey].clicks += clicks;
    });
    return Object.entries(weeklyData).map(([week, data]) => ({ week, ...data })).sort((a, b) => a.week.localeCompare(b.week));
  }
  // 按月聚合数据
  aggregateByMonth(dailyStats) {
    const monthlyData = {};
    Object.entries(dailyStats).forEach(([date, clicks]) => {
      const monthKey = date.substring(0, 7);
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { clicks: 0, views: 0 };
      }
      monthlyData[monthKey].clicks += clicks;
    });
    return Object.entries(monthlyData).map(([month, data]) => ({ month, ...data })).sort((a, b) => a.month.localeCompare(b.month));
  }
  // 获取周的开始日期
  getWeekStart(date) {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1);
    return new Date(d.setDate(diff));
  }
  // 记录页面访问
  async recordPageView(userAgent) {
    try {
      const stats = await this.kvService.getStats();
      stats.totalViews++;
      if (userAgent) {
        const deviceType = this.detectDeviceType(userAgent);
        stats.deviceStats[deviceType]++;
      }
      const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
      stats.dailyStats[today] = (stats.dailyStats[today] || 0) + 1;
      await this.kvService.updateStats(stats);
    } catch (error) {
      console.error("Failed to record page view:", error);
    }
  }
  // 检测设备类型
  detectDeviceType(userAgent) {
    const ua = userAgent.toLowerCase();
    if (/tablet|ipad|playbook|silk/.test(ua)) {
      return "tablet";
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/.test(ua)) {
      return "mobile";
    }
    return "desktop";
  }
  // 记录搜索
  async recordSearch(query) {
    try {
      if (!query.trim())
        return;
      const stats = await this.kvService.getStats();
      stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
      await this.kvService.updateStats(stats);
    } catch (error) {
      console.error("Failed to record search:", error);
    }
  }
  // 清理过期统计数据
  async cleanupOldStats(daysToKeep = 90) {
    try {
      const stats = await this.kvService.getStats();
      const cutoffDate = /* @__PURE__ */ new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const cutoffString = cutoffDate.toISOString().split("T")[0];
      Object.keys(stats.dailyStats).forEach((date) => {
        if (date < cutoffString) {
          delete stats.dailyStats[date];
        }
      });
      await this.kvService.updateStats(stats);
      console.log(`Cleaned up stats older than ${daysToKeep} days`);
    } catch (error) {
      console.error("Failed to cleanup old stats:", error);
    }
  }
  // 导出统计数据
  async exportStats() {
    return await this.getDetailedStats();
  }
  // 重置统计数据
  async resetStats() {
    try {
      await this.kvService.updateStats({
        totalClicks: 0,
        totalViews: 0,
        bookmarkStats: {},
        categoryStats: {},
        searchStats: {},
        dailyStats: {},
        deviceStats: { mobile: 0, desktop: 0, tablet: 0 },
        lastUpdated: Date.now()
      });
      console.log("Statistics reset successfully");
    } catch (error) {
      console.error("Failed to reset stats:", error);
      throw error;
    }
  }
};
__name(StatisticsService, "StatisticsService");

// src/routes/api.ts
var app = new Hono2();
app.use("*", async (c, next) => {
  const kvService = c.get("kvService");
  const bookmarkService = new BookmarkService(kvService);
  const categoryService = new CategoryService(kvService);
  const config = await kvService.getConfig();
  c.set("bookmarkService", bookmarkService);
  c.set("categoryService", categoryService);
  c.set("migrationService", new MigrationService(kvService));
  c.set("chromeBookmarkService", new ChromeBookmarkService(bookmarkService, categoryService));
  c.set("aiService", new AIService(bookmarkService, categoryService, config.aiConfig));
  c.set("statisticsService", new StatisticsService(kvService));
  await next();
});
app.get("/bookmarks", async (c) => {
  try {
    const bookmarkService = c.get("bookmarkService");
    const bookmarks = await bookmarkService.getAllBookmarks();
    return c.json({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error("Get bookmarks error:", error);
    return c.json({
      success: false,
      error: "Failed to get bookmarks"
    }, 500);
  }
});
app.get("/bookmarks/:id", async (c) => {
  try {
    const id = c.req.param("id");
    const bookmarkService = c.get("bookmarkService");
    const bookmark = await bookmarkService.getBookmarkById(id);
    if (!bookmark) {
      return c.json({
        success: false,
        error: "Bookmark not found"
      }, 404);
    }
    return c.json({
      success: true,
      data: bookmark
    });
  } catch (error) {
    console.error("Get bookmark error:", error);
    return c.json({
      success: false,
      error: "Failed to get bookmark"
    }, 500);
  }
});
app.post("/bookmarks", async (c) => {
  try {
    const bookmarkData = await c.req.json();
    const bookmarkService = c.get("bookmarkService");
    const validation = bookmarkService.validateBookmarkData(bookmarkData);
    if (!validation.valid) {
      return c.json({
        success: false,
        error: "Validation failed",
        data: { errors: validation.errors }
      }, 400);
    }
    const duplicate = await bookmarkService.checkDuplicateBookmark(bookmarkData.url);
    if (duplicate) {
      return c.json({
        success: false,
        error: "Bookmark with this URL already exists"
      }, 409);
    }
    const bookmark = await bookmarkService.createBookmark(bookmarkData);
    return c.json({
      success: true,
      data: bookmark,
      message: "Bookmark created successfully"
    }, 201);
  } catch (error) {
    console.error("Create bookmark error:", error);
    return c.json({
      success: false,
      error: "Failed to create bookmark"
    }, 500);
  }
});
app.put("/bookmarks/:id", async (c) => {
  try {
    const id = c.req.param("id");
    const updates = await c.req.json();
    const bookmarkService = c.get("bookmarkService");
    const bookmark = await bookmarkService.updateBookmark(id, updates);
    if (!bookmark) {
      return c.json({
        success: false,
        error: "Bookmark not found"
      }, 404);
    }
    return c.json({
      success: true,
      data: bookmark,
      message: "Bookmark updated successfully"
    });
  } catch (error) {
    console.error("Update bookmark error:", error);
    return c.json({
      success: false,
      error: "Failed to update bookmark"
    }, 500);
  }
});
app.delete("/bookmarks/:id", async (c) => {
  try {
    const id = c.req.param("id");
    const bookmarkService = c.get("bookmarkService");
    const success = await bookmarkService.deleteBookmark(id);
    if (!success) {
      return c.json({
        success: false,
        error: "Bookmark not found"
      }, 404);
    }
    return c.json({
      success: true,
      message: "Bookmark deleted successfully"
    });
  } catch (error) {
    console.error("Delete bookmark error:", error);
    return c.json({
      success: false,
      error: "Failed to delete bookmark"
    }, 500);
  }
});
app.post("/bookmarks/batch-delete", async (c) => {
  try {
    const { ids } = await c.req.json();
    if (!Array.isArray(ids) || ids.length === 0) {
      return c.json({
        success: false,
        error: "Invalid bookmark IDs"
      }, 400);
    }
    const bookmarkService = c.get("bookmarkService");
    const result = await bookmarkService.deleteBookmarks(ids);
    return c.json({
      success: true,
      data: result,
      message: `Deleted ${result.success.length} bookmarks`
    });
  } catch (error) {
    console.error("Batch delete bookmarks error:", error);
    return c.json({
      success: false,
      error: "Failed to delete bookmarks"
    }, 500);
  }
});
app.get("/categories", async (c) => {
  try {
    const categoryService = c.get("categoryService");
    const categories = await categoryService.getAllCategories();
    return c.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error("Get categories error:", error);
    return c.json({
      success: false,
      error: "Failed to get categories"
    }, 500);
  }
});
app.get("/categories/:id", async (c) => {
  try {
    const id = c.req.param("id");
    const categoryService = c.get("categoryService");
    const category = await categoryService.getCategoryById(id);
    if (!category) {
      return c.json({
        success: false,
        error: "Category not found"
      }, 404);
    }
    return c.json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error("Get category error:", error);
    return c.json({
      success: false,
      error: "Failed to get category"
    }, 500);
  }
});
app.post("/categories", async (c) => {
  try {
    const categoryData = await c.req.json();
    const categoryService = c.get("categoryService");
    const validation = categoryService.validateCategoryData(categoryData);
    if (!validation.valid) {
      return c.json({
        success: false,
        error: "Validation failed",
        data: { errors: validation.errors }
      }, 400);
    }
    const duplicate = await categoryService.checkDuplicateCategoryName(categoryData.name);
    if (duplicate) {
      return c.json({
        success: false,
        error: "Category with this name already exists"
      }, 409);
    }
    const category = await categoryService.createCategory(categoryData);
    return c.json({
      success: true,
      data: category,
      message: "Category created successfully"
    }, 201);
  } catch (error) {
    console.error("Create category error:", error);
    return c.json({
      success: false,
      error: "Failed to create category"
    }, 500);
  }
});
app.put("/categories/:id", async (c) => {
  try {
    const id = c.req.param("id");
    const updates = await c.req.json();
    const categoryService = c.get("categoryService");
    const category = await categoryService.updateCategory(id, updates);
    if (!category) {
      return c.json({
        success: false,
        error: "Category not found"
      }, 404);
    }
    return c.json({
      success: true,
      data: category,
      message: "Category updated successfully"
    });
  } catch (error) {
    console.error("Update category error:", error);
    return c.json({
      success: false,
      error: "Failed to update category"
    }, 500);
  }
});
app.delete("/categories/:id", async (c) => {
  try {
    const id = c.req.param("id");
    const { moveBookmarksTo, deleteBookmarks } = c.req.query();
    const categoryService = c.get("categoryService");
    const options = {};
    if (moveBookmarksTo)
      options.moveBookmarksTo = moveBookmarksTo;
    if (deleteBookmarks === "true")
      options.deleteBookmarks = true;
    const result = await categoryService.deleteCategory(id, options);
    if (!result.success) {
      return c.json({
        success: false,
        error: "Category not found"
      }, 404);
    }
    return c.json({
      success: true,
      data: result,
      message: "Category deleted successfully"
    });
  } catch (error) {
    console.error("Delete category error:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete category"
    }, 500);
  }
});
app.get("/categories/stats", async (c) => {
  try {
    const categoryService = c.get("categoryService");
    const stats = await categoryService.getCategoryStats();
    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error("Get category stats error:", error);
    return c.json({
      success: false,
      error: "Failed to get category stats"
    }, 500);
  }
});
app.get("/stats", async (c) => {
  try {
    const kvService = c.get("kvService");
    const stats = await kvService.getStats();
    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error("Get stats error:", error);
    return c.json({
      success: false,
      error: "Failed to get stats"
    }, 500);
  }
});
app.post("/stats/click", async (c) => {
  try {
    const { bookmarkId } = await c.req.json();
    if (!bookmarkId) {
      return c.json({
        success: false,
        error: "Bookmark ID is required"
      }, 400);
    }
    const bookmarkService = c.get("bookmarkService");
    const success = await bookmarkService.incrementClickCount(bookmarkId);
    if (success) {
      return c.json({
        success: true,
        message: "Click recorded"
      });
    } else {
      return c.json({
        success: false,
        error: "Bookmark not found"
      }, 404);
    }
  } catch (error) {
    console.error("Record click error:", error);
    return c.json({
      success: false,
      error: "Failed to record click"
    }, 500);
  }
});
app.get("/bookmarks/popular", async (c) => {
  try {
    const limit = parseInt(c.req.query("limit") || "10");
    const bookmarkService = c.get("bookmarkService");
    const bookmarks = await bookmarkService.getPopularBookmarks(limit);
    return c.json({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error("Get popular bookmarks error:", error);
    return c.json({
      success: false,
      error: "Failed to get popular bookmarks"
    }, 500);
  }
});
app.get("/bookmarks/recent", async (c) => {
  try {
    const limit = parseInt(c.req.query("limit") || "10");
    const bookmarkService = c.get("bookmarkService");
    const bookmarks = await bookmarkService.getRecentBookmarks(limit);
    return c.json({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error("Get recent bookmarks error:", error);
    return c.json({
      success: false,
      error: "Failed to get recent bookmarks"
    }, 500);
  }
});
app.get("/migration/status", async (c) => {
  try {
    const migrationService = c.get("migrationService");
    const status = await migrationService.getMigrationStatus();
    return c.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error("Get migration status error:", error);
    return c.json({
      success: false,
      error: "Failed to get migration status"
    }, 500);
  }
});
app.post("/migration/migrate", async (c) => {
  try {
    const migrationService = c.get("migrationService");
    const result = await migrationService.performMigration();
    return c.json({
      success: result.success,
      data: result.details,
      message: result.message
    });
  } catch (error) {
    console.error("Migration error:", error);
    return c.json({
      success: false,
      error: "Migration failed"
    }, 500);
  }
});
app.post("/migration/sample-data", async (c) => {
  try {
    const migrationService = c.get("migrationService");
    const result = await migrationService.createSampleData();
    return c.json({
      success: result.success,
      data: {
        categoriesCreated: result.categoriesCreated,
        bookmarksCreated: result.bookmarksCreated,
        errors: result.errors
      },
      message: `Created ${result.categoriesCreated} categories and ${result.bookmarksCreated} bookmarks`
    });
  } catch (error) {
    console.error("Create sample data error:", error);
    return c.json({
      success: false,
      error: "Failed to create sample data"
    }, 500);
  }
});
app.post("/migration/clear", async (c) => {
  try {
    const migrationService = c.get("migrationService");
    const success = await migrationService.clearAllData();
    return c.json({
      success,
      message: success ? "All data cleared successfully" : "Failed to clear data"
    });
  } catch (error) {
    console.error("Clear data error:", error);
    return c.json({
      success: false,
      error: "Failed to clear data"
    }, 500);
  }
});
app.post("/import/bookmarks", async (c) => {
  try {
    const body = await c.req.json();
    const { content, options = {} } = body;
    if (!content) {
      return c.json({
        success: false,
        error: "Bookmark file content is required"
      }, 400);
    }
    const chromeBookmarkService = c.get("chromeBookmarkService");
    const result = await chromeBookmarkService.importBookmarks(content, {
      createCategories: options.createCategories !== false,
      skipDuplicates: options.skipDuplicates !== false,
      defaultCategory: options.defaultCategory,
      format: options.format || "auto"
    });
    return c.json({
      success: result.success,
      data: {
        imported: result.imported,
        skipped: result.skipped,
        errors: result.errors
      },
      message: result.success ? `\u6210\u529F\u5BFC\u5165 ${result.imported} \u4E2A\u4E66\u7B7E\uFF0C\u8DF3\u8FC7 ${result.skipped} \u4E2A\u91CD\u590D\u9879` : "\u5BFC\u5165\u5931\u8D25"
    });
  } catch (error) {
    console.error("Bookmark import error:", error);
    return c.json({
      success: false,
      error: "Failed to import bookmarks"
    }, 500);
  }
});
app.post("/import/chrome", async (c) => {
  try {
    const body = await c.req.json();
    const { jsonContent, options = {} } = body;
    if (!jsonContent) {
      return c.json({
        success: false,
        error: "Chrome bookmarks JSON content is required"
      }, 400);
    }
    const chromeBookmarkService = c.get("chromeBookmarkService");
    const result = await chromeBookmarkService.importChromeBookmarks(jsonContent, {
      createCategories: options.createCategories !== false,
      skipDuplicates: options.skipDuplicates !== false,
      defaultCategory: options.defaultCategory
    });
    return c.json({
      success: result.success,
      data: {
        imported: result.imported,
        skipped: result.skipped,
        errors: result.errors
      },
      message: result.success ? `\u6210\u529F\u5BFC\u5165 ${result.imported} \u4E2A\u4E66\u7B7E\uFF0C\u8DF3\u8FC7 ${result.skipped} \u4E2A\u91CD\u590D\u9879` : "\u5BFC\u5165\u5931\u8D25"
    });
  } catch (error) {
    console.error("Chrome import error:", error);
    return c.json({
      success: false,
      error: "Failed to import Chrome bookmarks"
    }, 500);
  }
});
app.get("/export/chrome", async (c) => {
  try {
    const chromeBookmarkService = c.get("chromeBookmarkService");
    const chromeJson = await chromeBookmarkService.exportToChromeFormat();
    c.header("Content-Type", "application/json");
    c.header("Content-Disposition", 'attachment; filename="cloudnav-bookmarks.json"');
    return c.text(chromeJson);
  } catch (error) {
    console.error("Chrome export error:", error);
    return c.json({
      success: false,
      error: "Failed to export Chrome bookmarks"
    }, 500);
  }
});
app.get("/export/netscape", async (c) => {
  try {
    const chromeBookmarkService = c.get("chromeBookmarkService");
    const netscapeHtml = await chromeBookmarkService.exportToNetscapeFormat();
    c.header("Content-Type", "text/html");
    c.header("Content-Disposition", 'attachment; filename="cloudnav-bookmarks.html"');
    return c.text(netscapeHtml);
  } catch (error) {
    console.error("Netscape export error:", error);
    return c.json({
      success: false,
      error: "Failed to export Netscape bookmarks"
    }, 500);
  }
});
app.get("/import/stats", async (c) => {
  try {
    const chromeBookmarkService = c.get("chromeBookmarkService");
    const stats = await chromeBookmarkService.getImportStats();
    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error("Import stats error:", error);
    return c.json({
      success: false,
      error: "Failed to get import stats"
    }, 500);
  }
});
app.post("/ai/analyze", async (c) => {
  try {
    const body = await c.req.json();
    const options = {
      enableCategoryRecommendations: body.enableCategoryRecommendations !== false,
      enableDuplicateDetection: body.enableDuplicateDetection !== false,
      enableDescriptionGeneration: body.enableDescriptionGeneration !== false,
      enableTagSuggestions: body.enableTagSuggestions !== false,
      enableNewCategoryCreation: body.enableNewCategoryCreation !== false,
      confidenceThreshold: body.confidenceThreshold || 0.7,
      autoApply: body.autoApply || false
    };
    const aiService = c.get("aiService");
    const result = await aiService.analyzeBookmarks(options);
    return c.json({
      success: true,
      data: result,
      message: `AI \u5206\u6790\u5B8C\u6210\uFF0C\u53D1\u73B0 ${result.statistics.categorySuggestions} \u4E2A\u5206\u7C7B\u5EFA\u8BAE\uFF0C${result.statistics.duplicatesFound} \u4E2A\u91CD\u590D\u9879`
    });
  } catch (error) {
    console.error("AI analyze error:", error);
    return c.json({
      success: false,
      error: "Failed to analyze bookmarks with AI"
    }, 500);
  }
});
app.post("/ai/apply", async (c) => {
  try {
    const body = await c.req.json();
    const { analysisResult, selectedSuggestions } = body;
    if (!analysisResult || !selectedSuggestions) {
      return c.json({
        success: false,
        error: "Analysis result and selected suggestions are required"
      }, 400);
    }
    const aiService = c.get("aiService");
    const result = await aiService.applyAISuggestions(analysisResult, selectedSuggestions);
    return c.json({
      success: true,
      data: result,
      message: `\u6210\u529F\u5E94\u7528 ${result.applied} \u4E2A AI \u5EFA\u8BAE${result.errors.length > 0 ? `\uFF0C${result.errors.length} \u4E2A\u9519\u8BEF` : ""}`
    });
  } catch (error) {
    console.error("AI apply error:", error);
    return c.json({
      success: false,
      error: "Failed to apply AI suggestions"
    }, 500);
  }
});
app.get("/ai/config", async (c) => {
  try {
    const aiService = c.get("aiService");
    const config = aiService.getAIConfig();
    return c.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error("Get AI config error:", error);
    return c.json({
      success: false,
      error: "Failed to get AI config"
    }, 500);
  }
});
app.put("/ai/config", async (c) => {
  try {
    const body = await c.req.json();
    const aiService = c.get("aiService");
    aiService.updateAIConfig(body);
    return c.json({
      success: true,
      message: "AI configuration updated successfully"
    });
  } catch (error) {
    console.error("Update AI config error:", error);
    return c.json({
      success: false,
      error: "Failed to update AI config"
    }, 500);
  }
});
app.get("/statistics/detailed", async (c) => {
  try {
    const statisticsService = c.get("statisticsService");
    const stats = await statisticsService.getDetailedStats();
    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error("Get detailed statistics error:", error);
    return c.json({
      success: false,
      error: "Failed to get detailed statistics"
    }, 500);
  }
});
app.post("/statistics/page-view", async (c) => {
  try {
    const userAgent = c.req.header("User-Agent");
    const statisticsService = c.get("statisticsService");
    await statisticsService.recordPageView(userAgent);
    return c.json({
      success: true,
      message: "Page view recorded"
    });
  } catch (error) {
    console.error("Record page view error:", error);
    return c.json({
      success: false,
      error: "Failed to record page view"
    }, 500);
  }
});
app.post("/statistics/search", async (c) => {
  try {
    const body = await c.req.json();
    const { query } = body;
    if (!query) {
      return c.json({
        success: false,
        error: "Search query is required"
      }, 400);
    }
    const statisticsService = c.get("statisticsService");
    await statisticsService.recordSearch(query);
    return c.json({
      success: true,
      message: "Search recorded"
    });
  } catch (error) {
    console.error("Record search error:", error);
    return c.json({
      success: false,
      error: "Failed to record search"
    }, 500);
  }
});
app.get("/statistics/export", async (c) => {
  try {
    const statisticsService = c.get("statisticsService");
    const stats = await statisticsService.exportStats();
    c.header("Content-Type", "application/json");
    c.header("Content-Disposition", 'attachment; filename="cloudnav-statistics.json"');
    return c.text(JSON.stringify(stats, null, 2));
  } catch (error) {
    console.error("Export statistics error:", error);
    return c.json({
      success: false,
      error: "Failed to export statistics"
    }, 500);
  }
});
app.post("/statistics/reset", async (c) => {
  try {
    const statisticsService = c.get("statisticsService");
    await statisticsService.resetStats();
    return c.json({
      success: true,
      message: "Statistics reset successfully"
    });
  } catch (error) {
    console.error("Reset statistics error:", error);
    return c.json({
      success: false,
      error: "Failed to reset statistics"
    }, 500);
  }
});
app.post("/statistics/cleanup", async (c) => {
  try {
    const body = await c.req.json();
    const daysToKeep = body.daysToKeep || 90;
    const statisticsService = c.get("statisticsService");
    await statisticsService.cleanupOldStats(daysToKeep);
    return c.json({
      success: true,
      message: `Cleaned up statistics older than ${daysToKeep} days`
    });
  } catch (error) {
    console.error("Cleanup statistics error:", error);
    return c.json({
      success: false,
      error: "Failed to cleanup statistics"
    }, 500);
  }
});
app.get("/config", async (c) => {
  try {
    const kvService = c.get("kvService");
    const config = await kvService.getConfig();
    const publicConfig = {
      siteName: config.siteName,
      siteDescription: config.siteDescription,
      features: config.features,
      theme: config.theme
    };
    return c.json({
      success: true,
      data: publicConfig
    });
  } catch (error) {
    console.error("Get config error:", error);
    return c.json({
      success: false,
      error: "Failed to get config"
    }, 500);
  }
});
app.get("/search", async (c) => {
  try {
    const query = c.req.query("q") || "";
    if (!query.trim()) {
      return c.json({
        success: false,
        error: "Search query is required"
      }, 400);
    }
    const kvService = c.get("kvService");
    const bookmarks = await kvService.getBookmarks();
    const filteredBookmarks = bookmarks.filter(
      (bookmark) => bookmark.title.toLowerCase().includes(query.toLowerCase()) || bookmark.description?.toLowerCase().includes(query.toLowerCase()) || bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) || bookmark.tags?.some((tag) => tag.toLowerCase().includes(query.toLowerCase()))
    );
    const stats = await kvService.getStats();
    stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
    await kvService.updateStats(stats);
    return c.json({
      success: true,
      data: {
        query,
        results: filteredBookmarks,
        total: filteredBookmarks.length
      }
    });
  } catch (error) {
    console.error("Search error:", error);
    return c.json({
      success: false,
      error: "Search failed"
    }, 500);
  }
});
app.get("/backup", async (c) => {
  try {
    const kvService = c.get("kvService");
    const backup = await kvService.backup();
    return c.json({
      success: true,
      data: backup
    });
  } catch (error) {
    console.error("Backup error:", error);
    return c.json({
      success: false,
      error: "Backup failed"
    }, 500);
  }
});
app.get("/health", (c) => {
  return c.json({
    success: true,
    data: {
      status: "healthy",
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      version: "2.0.0"
    }
  });
});
app.get("/test", (c) => {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CloudNav 2.0 - API \u6D4B\u8BD5</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 py-4">
              <h1 class="text-2xl font-bold text-gray-900">\u{1F9EA} API \u6D4B\u8BD5\u9875\u9762</h1>
              <p class="text-gray-600">\u6D4B\u8BD5 CloudNav 2.0 \u7684\u6838\u5FC3 API \u529F\u80FD</p>
            </div>
          </header>

          <div class="max-w-7xl mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">\u{1F4CA} \u8FC1\u79FB\u72B6\u6001</h2>
                <button onclick="testMigrationStatus()" class="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mb-4">
                  \u83B7\u53D6\u8FC1\u79FB\u72B6\u6001
                </button>
                <pre id="migrationStatus" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">\u{1F3AF} \u521B\u5EFA\u793A\u4F8B\u6570\u636E</h2>
                <button onclick="createSampleData()" class="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mb-4">
                  \u521B\u5EFA\u793A\u4F8B\u6570\u636E
                </button>
                <pre id="sampleDataResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">\u{1F4DA} \u4E66\u7B7E\u5217\u8868</h2>
                <button onclick="getBookmarks()" class="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 mb-4">
                  \u83B7\u53D6\u4E66\u7B7E\u5217\u8868
                </button>
                <pre id="bookmarksList" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">\u{1F4C1} \u5206\u7C7B\u5217\u8868</h2>
                <button onclick="getCategories()" class="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 mb-4">
                  \u83B7\u53D6\u5206\u7C7B\u5217\u8868
                </button>
                <pre id="categoriesList" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">\u2795 \u521B\u5EFA\u65B0\u4E66\u7B7E</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <input type="text" id="bookmarkTitle" placeholder="\u4E66\u7B7E\u6807\u9898" class="px-3 py-2 border border-gray-300 rounded">
                <input type="url" id="bookmarkUrl" placeholder="\u4E66\u7B7EURL" class="px-3 py-2 border border-gray-300 rounded">
                <input type="text" id="bookmarkCategory" placeholder="\u5206\u7C7BID (\u5148\u521B\u5EFA\u793A\u4F8B\u6570\u636E\u83B7\u53D6)" class="px-3 py-2 border border-gray-300 rounded">
                <input type="text" id="bookmarkDesc" placeholder="\u63CF\u8FF0" class="px-3 py-2 border border-gray-300 rounded">
              </div>
              <button onclick="createBookmark()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4">
                \u521B\u5EFA\u4E66\u7B7E
              </button>
              <pre id="createBookmarkResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">\u{1F4E5} \u4E66\u7B7E\u5BFC\u5165\uFF08\u652F\u6301\u591A\u79CD\u683C\u5F0F\uFF09</h2>
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  \u4E0A\u4F20\u4E66\u7B7E\u6587\u4EF6 (\u652F\u6301 .json \u548C .html \u683C\u5F0F)
                </label>
                <input type="file" id="bookmarkFile" accept=".json,.html,.htm" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                <div class="mt-2 text-sm text-gray-600">
                  <p>\u652F\u6301\u7684\u683C\u5F0F\uFF1A</p>
                  <ul class="list-disc list-inside ml-4">
                    <li>Chrome JSON \u683C\u5F0F (bookmarks.json)</li>
                    <li>HTML \u4E66\u7B7E\u683C\u5F0F (bookmarks.html)</li>
                    <li>Netscape \u4E66\u7B7E\u683C\u5F0F</li>
                  </ul>
                </div>
              </div>
              <div class="mb-4 space-y-2">
                <label class="flex items-center">
                  <input type="checkbox" id="createCategories" checked class="mr-2">
                  <span class="text-sm">\u81EA\u52A8\u521B\u5EFA\u5206\u7C7B</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="skipDuplicates" checked class="mr-2">
                  <span class="text-sm">\u8DF3\u8FC7\u91CD\u590D\u4E66\u7B7E</span>
                </label>
              </div>
              <button onclick="importBookmarks()" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mb-4">
                \u5BFC\u5165\u4E66\u7B7E\u6587\u4EF6
              </button>
              <pre id="importResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">\u{1F4E4} \u4E66\u7B7E\u5BFC\u51FA</h2>
              <div class="space-x-4 mb-4">
                <button onclick="exportChromeBookmarks()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                  \u5BFC\u51FA\u4E3A Chrome \u683C\u5F0F
                </button>
                <button onclick="exportNetscapeBookmarks()" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                  \u5BFC\u51FA\u4E3A HTML \u683C\u5F0F
                </button>
                <button onclick="getImportStats()" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                  \u5BFC\u5165\u7EDF\u8BA1
                </button>
              </div>
              <pre id="exportResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">\u{1F916} AI \u667A\u80FD\u6574\u7406</h2>
              <div class="mb-4 space-y-2">
                <label class="flex items-center">
                  <input type="checkbox" id="enableCategoryRecommendations" checked class="mr-2">
                  <span class="text-sm">\u542F\u7528\u5206\u7C7B\u63A8\u8350</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="enableDuplicateDetection" checked class="mr-2">
                  <span class="text-sm">\u542F\u7528\u91CD\u590D\u68C0\u6D4B</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="enableDescriptionGeneration" checked class="mr-2">
                  <span class="text-sm">\u542F\u7528\u63CF\u8FF0\u751F\u6210</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="enableTagSuggestions" checked class="mr-2">
                  <span class="text-sm">\u542F\u7528\u6807\u7B7E\u5EFA\u8BAE</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="enableNewCategoryCreation" class="mr-2">
                  <span class="text-sm">\u542F\u7528\u65B0\u5206\u7C7B\u521B\u5EFA</span>
                </label>
                <div class="flex items-center space-x-2">
                  <label class="text-sm">\u7F6E\u4FE1\u5EA6\u9608\u503C:</label>
                  <input type="range" id="confidenceThreshold" min="0.1" max="1.0" step="0.1" value="0.7" class="flex-1">
                  <span id="confidenceValue" class="text-sm font-mono">0.7</span>
                </div>
              </div>
              <div class="space-x-4 mb-4">
                <button onclick="analyzeWithAI()" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                  AI \u5206\u6790\u4E66\u7B7E
                </button>
                <button onclick="getAIConfig()" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                  \u83B7\u53D6 AI \u914D\u7F6E
                </button>
              </div>
              <pre id="aiResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">\u{1F4CA} \u7EDF\u8BA1\u529F\u80FD</h2>
              <div class="space-x-4 mb-4">
                <button onclick="getDetailedStats()" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
                  \u83B7\u53D6\u8BE6\u7EC6\u7EDF\u8BA1
                </button>
                <button onclick="recordPageView()" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                  \u8BB0\u5F55\u9875\u9762\u8BBF\u95EE
                </button>
                <button onclick="recordSearch()" class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700">
                  \u8BB0\u5F55\u641C\u7D22
                </button>
                <button onclick="exportStats()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                  \u5BFC\u51FA\u7EDF\u8BA1
                </button>
                <button onclick="resetStats()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                  \u91CD\u7F6E\u7EDF\u8BA1
                </button>
              </div>
              <pre id="statsResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">\u{1F9F9} \u6E05\u7406\u6570\u636E</h2>
              <button onclick="clearAllData()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4">
                \u6E05\u7A7A\u6240\u6709\u6570\u636E
              </button>
              <pre id="clearDataResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">\u{1F517} \u5FEB\u901F\u94FE\u63A5</h2>
              <div class="space-x-4">
                <a href="/" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">\u8FD4\u56DE\u4E3B\u9875</a>
                <a href="/admin" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">\u7BA1\u7406\u9762\u677F</a>
                <a href="/about" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">\u5173\u4E8E\u9875\u9762</a>
              </div>
            </div>
          </div>
        </div>

        <script>
          // \u66F4\u65B0\u7F6E\u4FE1\u5EA6\u663E\u793A
          document.getElementById('confidenceThreshold').addEventListener('input', function() {
            document.getElementById('confidenceValue').textContent = this.value;
          });

          async function testMigrationStatus() {
            try {
              const response = await fetch('/api/migration/status');
              const data = await response.json();
              document.getElementById('migrationStatus').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('migrationStatus').textContent = 'Error: ' + error.message;
            }
          }

          async function createSampleData() {
            try {
              const response = await fetch('/api/migration/sample-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('sampleDataResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('sampleDataResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getBookmarks() {
            try {
              const response = await fetch('/api/bookmarks');
              const data = await response.json();
              document.getElementById('bookmarksList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('bookmarksList').textContent = 'Error: ' + error.message;
            }
          }

          async function getCategories() {
            try {
              const response = await fetch('/api/categories');
              const data = await response.json();
              document.getElementById('categoriesList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('categoriesList').textContent = 'Error: ' + error.message;
            }
          }

          async function createBookmark() {
            try {
              const title = document.getElementById('bookmarkTitle').value;
              const url = document.getElementById('bookmarkUrl').value;
              const category = document.getElementById('bookmarkCategory').value;
              const description = document.getElementById('bookmarkDesc').value;

              if (!title || !url || !category) {
                alert('\u8BF7\u586B\u5199\u6807\u9898\u3001URL\u548C\u5206\u7C7BID');
                return;
              }

              const response = await fetch('/api/bookmarks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  title,
                  url,
                  category,
                  description,
                  shortDesc: description
                })
              });
              const data = await response.json();
              document.getElementById('createBookmarkResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('createBookmarkResult').textContent = 'Error: ' + error.message;
            }
          }

          async function importBookmarks() {
            try {
              const fileInput = document.getElementById('bookmarkFile');
              const file = fileInput.files[0];

              if (!file) {
                alert('\u8BF7\u9009\u62E9\u4E66\u7B7E\u6587\u4EF6');
                return;
              }

              const content = await file.text();
              const createCategories = document.getElementById('createCategories').checked;
              const skipDuplicates = document.getElementById('skipDuplicates').checked;

              // \u68C0\u6D4B\u6587\u4EF6\u7C7B\u578B
              const fileName = file.name.toLowerCase();
              let format = 'auto';
              if (fileName.endsWith('.json')) {
                format = 'json';
              } else if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
                format = 'html';
              }

              const response = await fetch('/api/import/bookmarks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  content,
                  options: {
                    createCategories,
                    skipDuplicates,
                    format
                  }
                })
              });
              const data = await response.json();
              document.getElementById('importResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('importResult').textContent = 'Error: ' + error.message;
            }
          }

          async function exportChromeBookmarks() {
            try {
              const response = await fetch('/api/export/chrome');
              if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'cloudnav-bookmarks.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                document.getElementById('exportResult').textContent = '\u2705 Chrome \u683C\u5F0F\u4E66\u7B7E\u5BFC\u51FA\u6210\u529F';
              } else {
                const error = await response.json();
                document.getElementById('exportResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
              }
            } catch (error) {
              document.getElementById('exportResult').textContent = 'Error: ' + error.message;
            }
          }

          async function exportNetscapeBookmarks() {
            try {
              const response = await fetch('/api/export/netscape');
              if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'cloudnav-bookmarks.html';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                document.getElementById('exportResult').textContent = '\u2705 HTML \u683C\u5F0F\u4E66\u7B7E\u5BFC\u51FA\u6210\u529F';
              } else {
                const error = await response.json();
                document.getElementById('exportResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
              }
            } catch (error) {
              document.getElementById('exportResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getImportStats() {
            try {
              const response = await fetch('/api/import/stats');
              const data = await response.json();
              document.getElementById('exportResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('exportResult').textContent = 'Error: ' + error.message;
            }
          }

          async function analyzeWithAI() {
            try {
              const options = {
                enableCategoryRecommendations: document.getElementById('enableCategoryRecommendations').checked,
                enableDuplicateDetection: document.getElementById('enableDuplicateDetection').checked,
                enableDescriptionGeneration: document.getElementById('enableDescriptionGeneration').checked,
                enableTagSuggestions: document.getElementById('enableTagSuggestions').checked,
                enableNewCategoryCreation: document.getElementById('enableNewCategoryCreation').checked,
                confidenceThreshold: parseFloat(document.getElementById('confidenceThreshold').value)
              };

              const response = await fetch('/api/ai/analyze', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(options)
              });
              const data = await response.json();

              // \u683C\u5F0F\u5316\u663E\u793A\u7ED3\u679C
              if (data.success && data.data) {
                const result = data.data;
                const summary = {
                  message: data.message,
                  statistics: result.statistics,
                  suggestions: {
                    categoryRecommendations: result.suggestions.categoryRecommendations.length,
                    duplicateDetection: result.suggestions.duplicateDetection.length,
                    descriptionSuggestions: result.suggestions.descriptionSuggestions.length,
                    tagSuggestions: result.suggestions.tagSuggestions.length,
                    newCategorySuggestions: result.suggestions.newCategorySuggestions.length
                  },
                  sampleSuggestions: {
                    categoryRecommendations: result.suggestions.categoryRecommendations.slice(0, 3),
                    duplicates: result.suggestions.duplicateDetection.slice(0, 2),
                    descriptions: result.suggestions.descriptionSuggestions.slice(0, 3),
                    tags: result.suggestions.tagSuggestions.slice(0, 3),
                    newCategories: result.suggestions.newCategorySuggestions.slice(0, 2)
                  }
                };
                document.getElementById('aiResult').textContent = JSON.stringify(summary, null, 2);
              } else {
                document.getElementById('aiResult').textContent = JSON.stringify(data, null, 2);
              }
            } catch (error) {
              document.getElementById('aiResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getAIConfig() {
            try {
              const response = await fetch('/api/ai/config');
              const data = await response.json();
              document.getElementById('aiResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('aiResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getDetailedStats() {
            try {
              const response = await fetch('/api/statistics/detailed');
              const data = await response.json();

              // \u683C\u5F0F\u5316\u663E\u793A\u7EDF\u8BA1\u6570\u636E
              if (data.success && data.data) {
                const stats = data.data;
                const summary = {
                  overview: stats.overview,
                  topBookmarks: stats.bookmarkStats.topBookmarks.slice(0, 5),
                  topCategories: stats.categoryStats.topCategories.slice(0, 5),
                  topSearches: stats.searchStats.topSearches.slice(0, 10),
                  deviceStats: stats.deviceStats,
                  recentTrends: {
                    dailyStats: Object.entries(stats.timeStats.dailyStats).slice(-7),
                    weeklyTrend: stats.timeStats.weeklyTrend.slice(-4),
                    monthlyTrend: stats.timeStats.monthlyTrend.slice(-6)
                  }
                };
                document.getElementById('statsResult').textContent = JSON.stringify(summary, null, 2);
              } else {
                document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);
              }
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function recordPageView() {
            try {
              const response = await fetch('/api/statistics/page-view', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function recordSearch() {
            const query = prompt('\u8BF7\u8F93\u5165\u641C\u7D22\u5173\u952E\u8BCD:');
            if (!query) return;

            try {
              const response = await fetch('/api/statistics/search', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
              });
              const data = await response.json();
              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function exportStats() {
            try {
              const response = await fetch('/api/statistics/export');
              if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'cloudnav-statistics.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                document.getElementById('statsResult').textContent = '\u2705 \u7EDF\u8BA1\u6570\u636E\u5BFC\u51FA\u6210\u529F';
              } else {
                const error = await response.json();
                document.getElementById('statsResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
              }
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function resetStats() {
            if (!confirm('\u786E\u5B9A\u8981\u91CD\u7F6E\u6240\u6709\u7EDF\u8BA1\u6570\u636E\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\uFF01')) {
              return;
            }

            try {
              const response = await fetch('/api/statistics/reset', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function clearAllData() {
            if (!confirm('\u786E\u5B9A\u8981\u6E05\u7A7A\u6240\u6709\u6570\u636E\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\uFF01')) {
              return;
            }

            try {
              const response = await fetch('/api/migration/clear', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('clearDataResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('clearDataResult').textContent = 'Error: ' + error.message;
            }
          }
        <\/script>
      </body>
    </html>
  `;
  return c.html(html);
});

// src/components/HomePage.ts
var HomePage = /* @__PURE__ */ __name(({
  bookmarks,
  categories,
  config,
  searchQuery,
  currentCategory
}) => {
  const categoryNav = categories.map(
    (category) => `<a href="/category/${category.id}" class="px-4 py-2 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors">
      ${category.name}
    </a>`
  ).join("");
  const bookmarkCards = bookmarks.map(
    (bookmark) => `<div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <div class="flex items-center mb-3">
        ${bookmark.icon ? `<img src="${bookmark.icon}" alt="${bookmark.title}" class="w-8 h-8 mr-3 rounded">` : ""}
        <h3 class="text-lg font-semibold text-gray-800">${bookmark.title}</h3>
      </div>
      <p class="text-gray-600 text-sm mb-3">${bookmark.shortDesc || bookmark.description || ""}</p>
      <a href="${bookmark.url}" target="_blank" rel="noopener noreferrer" 
         onclick="trackClick('${bookmark.id}')"
         class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
        \u8BBF\u95EE
      </a>
      ${bookmark.clickCount > 0 ? `<span class="ml-3 text-sm text-gray-500">${bookmark.clickCount} \u6B21\u70B9\u51FB</span>` : ""}
    </div>`
  ).join("");
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- \u5934\u90E8 -->
      <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-4">
          <div class="flex items-center justify-between">
            <h1 class="text-2xl font-bold text-gray-900">${config.siteName}</h1>
            <div class="flex items-center space-x-4">
              <input type="text" 
                     placeholder="\u641C\u7D22\u4E66\u7B7E..." 
                     value="${searchQuery || ""}"
                     class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                     onkeypress="if(event.key==='Enter') window.location.href='/search?q='+encodeURIComponent(this.value)">
              <a href="/admin" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">\u7BA1\u7406</a>
            </div>
          </div>
        </div>
      </header>

      <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- \u5206\u7C7B\u5BFC\u822A -->
        <div class="mb-8">
          <div class="flex flex-wrap gap-3">
            <a href="/" class="px-4 py-2 ${!currentCategory ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-700"} rounded-lg hover:bg-blue-500 hover:text-white transition-colors">
              \u5168\u90E8
            </a>
            ${categoryNav}
          </div>
        </div>

        <!-- \u4E66\u7B7E\u5C55\u793A -->
        ${bookmarks.length > 0 ? `
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            ${bookmarkCards}
          </div>
        ` : `
          <div class="text-center py-12">
            <div class="text-gray-400 text-6xl mb-4">\u{1F4DA}</div>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">\u6682\u65E0\u4E66\u7B7E</h3>
            <p class="text-gray-500 mb-6">\u8FD8\u6CA1\u6709\u6DFB\u52A0\u4EFB\u4F55\u4E66\u7B7E\uFF0C\u53BB\u7BA1\u7406\u9875\u9762\u6DFB\u52A0\u4E00\u4E9B\u5427\uFF01</p>
            <a href="/admin" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">\u6DFB\u52A0\u4E66\u7B7E</a>
          </div>
        `}
      </div>
    </div>
  `;
}, "HomePage");

// src/components/Layout.tsx
var Layout = /* @__PURE__ */ __name(({ title, description, children }) => {
  return `
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>${title}</title>
        <meta name="description" content="${description}" />

        <!-- SEO Meta Tags -->
        <meta property="og:title" content="${title}" />
        <meta property="og:description" content="${description}" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:title" content="${title}" />
        <meta name="twitter:description" content="${description}" />

        <!-- Favicon -->
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />

        <!-- \u57FA\u7840CSS\u6846\u67B6 -->
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        
        {/* \u57FA\u7840\u6837\u5F0F */}
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
          }
          
          .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
          }
          
          .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 10px 0;
            transition: all 0.3s ease;
          }
          
          .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
          }
          
          .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #7C3AED;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          
          .btn:hover {
            background: #6D28D9;
            transform: translateY(-1px);
          }
          
          .grid {
            display: grid;
            gap: 20px;
          }
          
          .grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
          .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
          .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
          .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
          
          @media (max-width: 768px) {
            .grid-cols-2, .grid-cols-3, .grid-cols-4 {
              grid-template-columns: repeat(2, 1fr);
            }
          }
          
          @media (max-width: 480px) {
            .grid-cols-2, .grid-cols-3, .grid-cols-4 {
              grid-template-columns: 1fr;
            }
          }
          
          .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px 0;
            margin-bottom: 30px;
          }
          
          .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          
          .logo {
            font-size: 24px;
            font-weight: bold;
            color: #7C3AED;
            text-decoration: none;
          }
          
          .search-box {
            flex: 1;
            max-width: 400px;
            margin: 0 20px;
          }
          
          .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
          }
          
          .search-input:focus {
            outline: none;
            border-color: #7C3AED;
          }
          
          .category-nav {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          
          .category-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
          }
          
          .category-item {
            padding: 8px 16px;
            background: #f1f5f9;
            border-radius: 20px;
            text-decoration: none;
            color: #64748b;
            transition: all 0.3s ease;
          }
          
          .category-item:hover,
          .category-item.active {
            background: #7C3AED;
            color: white;
          }
          
          .bookmark-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
          }
          
          .bookmark-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
          }
          
          .bookmark-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
          }
          
          .bookmark-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            margin-bottom: 12px;
            object-fit: cover;
          }
          
          .bookmark-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1e293b;
          }
          
          .bookmark-desc {
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
          }
          
          .error {
            background: #fee2e2;
            color: #dc2626;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
          }
          
          .loading {
            text-align: center;
            padding: 40px;
            color: #64748b;
          }
          
          /* \u6697\u8272\u6A21\u5F0F */
          @media (prefers-color-scheme: dark) {
            body {
              background: #0f172a;
              color: #e2e8f0;
            }
            
            .card, .header, .category-nav, .bookmark-card {
              background: #1e293b;
              color: #e2e8f0;
            }
            
            .search-input {
              background: #334155;
              border-color: #475569;
              color: #e2e8f0;
            }
            
            .category-item {
              background: #334155;
              color: #94a3b8;
            }
            
            .bookmark-title {
              color: #f1f5f9;
            }
          }
        </style>
      </head>
      <body>
        ${children}

        <!-- \u57FA\u7840 JavaScript -->
        <script>
          // \u641C\u7D22\u529F\u80FD
          function initSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
              searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                  const query = this.value.trim();
                  if (query) {
                    window.location.href = '/search?q=' + encodeURIComponent(query);
                  }
                }
              });
            }
          }
          
          // \u70B9\u51FB\u7EDF\u8BA1
          function trackClick(bookmarkId) {
            fetch('/api/stats/click', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ bookmarkId })
            }).catch(console.error);
          }
          
          // \u521D\u59CB\u5316
          document.addEventListener('DOMContentLoaded', function() {
            initSearch();
          });
        <\/script>
      </body>
    </html>
  `;
}, "Layout");

// src/routes/pages.ts
var app2 = new Hono2();
app2.get("/", async (c) => {
  try {
    const kvService = c.get("kvService");
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);
    const stats = await kvService.getStats();
    await kvService.updateStats({
      ...stats,
      totalViews: stats.totalViews + 1
    });
    const content = HomePage({ bookmarks, categories, config });
    const html = Layout({
      title: config.siteName,
      description: config.siteDescription,
      children: content
    });
    return c.html(html);
  } catch (error) {
    console.error("Homepage error:", error);
    return c.html(Layout({
      title: "CloudNav - Error",
      description: "An error occurred",
      children: '<div class="error">\u670D\u52A1\u6682\u65F6\u4E0D\u53EF\u7528\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002</div>'
    }));
  }
});
app2.get("/search", async (c) => {
  const query = c.req.query("q") || "";
  const kvService = c.get("kvService");
  try {
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);
    const filteredBookmarks = bookmarks.filter(
      (bookmark) => bookmark.title.toLowerCase().includes(query.toLowerCase()) || bookmark.description?.toLowerCase().includes(query.toLowerCase()) || bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) || bookmark.tags?.some((tag) => tag.toLowerCase().includes(query.toLowerCase()))
    );
    if (query) {
      const stats = await kvService.getStats();
      stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
      await kvService.updateStats(stats);
    }
    const content = HomePage({
      bookmarks: filteredBookmarks,
      categories,
      config,
      searchQuery: query
    });
    const html = Layout({
      title: `\u641C\u7D22: ${query} - ${config.siteName}`,
      description: `\u641C\u7D22\u7ED3\u679C: ${query}`,
      children: content
    });
    return c.html(html);
  } catch (error) {
    console.error("Search error:", error);
    return c.html(Layout({
      title: "CloudNav - Search Error",
      description: "Search error",
      children: '<div class="error">\u641C\u7D22\u670D\u52A1\u6682\u65F6\u4E0D\u53EF\u7528\u3002</div>'
    }));
  }
});
app2.get("/category/:id", async (c) => {
  const categoryId = c.req.param("id");
  const kvService = c.get("kvService");
  try {
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);
    const category = categories.find((cat) => cat.id === categoryId);
    if (!category) {
      return c.html(Layout({
        title: "Category Not Found",
        description: "Category not found",
        children: '<div class="error">\u5206\u7C7B\u4E0D\u5B58\u5728\u3002</div>'
      }), 404);
    }
    const categoryBookmarks = bookmarks.filter((bookmark) => bookmark.category === categoryId);
    const stats = await kvService.getStats();
    stats.categoryStats[categoryId] = (stats.categoryStats[categoryId] || 0) + 1;
    await kvService.updateStats(stats);
    const content = HomePage({
      bookmarks: categoryBookmarks,
      categories,
      config,
      currentCategory: category
    });
    const html = Layout({
      title: `${category.name} - ${config.siteName}`,
      description: category.description || `${category.name} \u5206\u7C7B\u4E0B\u7684\u4E66\u7B7E`,
      children: content
    });
    return c.html(html);
  } catch (error) {
    console.error("Category page error:", error);
    return c.html(Layout({
      title: "CloudNav - Category Error",
      description: "Category error",
      children: '<div class="error">\u5206\u7C7B\u9875\u9762\u52A0\u8F7D\u5931\u8D25\u3002</div>'
    }));
  }
});
app2.get("/about", async (c) => {
  const kvService = c.get("kvService");
  const config = await kvService.getConfig();
  const aboutContent = `
    <div class="about-page">
      <h1>\u5173\u4E8E ${config.siteName}</h1>
      <p>${config.siteDescription}</p>
      <div class="features">
        <h2>\u529F\u80FD\u7279\u6027</h2>
        <ul>
          <li>\u{1F680} \u57FA\u4E8E Cloudflare Workers \u7684\u8FB9\u7F18\u8BA1\u7B97</li>
          <li>\u{1F4CA} \u667A\u80FD\u7EDF\u8BA1\u5206\u6790</li>
          <li>\u{1F916} AI \u667A\u80FD\u6574\u7406</li>
          <li>\u{1F4F1} \u54CD\u5E94\u5F0F\u8BBE\u8BA1</li>
          <li>\u{1F50D} \u5F3A\u5927\u7684\u641C\u7D22\u529F\u80FD</li>
          <li>\u{1F4E5} Chrome \u4E66\u7B7E\u5BFC\u5165\u5BFC\u51FA</li>
        </ul>
      </div>
      <div class="tech-stack">
        <h2>\u6280\u672F\u6808</h2>
        <p>Hono.js + TypeScript + Cloudflare Workers + KV Storage</p>
      </div>
    </div>
  `;
  const html = Layout({
    title: `\u5173\u4E8E - ${config.siteName}`,
    description: `\u5173\u4E8E ${config.siteName}`,
    children: aboutContent
  });
  return c.html(html);
});

// src/components/AdminPanel.tsx
var AdminPanel = /* @__PURE__ */ __name(({ bookmarks, categories, stats, config }) => {
  return `
    <div class="admin-panel">
      {/* \u7BA1\u7406\u9762\u677F\u5934\u90E8 */}
      <div class="admin-header">
        <div class="container">
          <div class="admin-nav">
            <h1>\u7BA1\u7406\u9762\u677F</h1>
            <div class="admin-nav-links">
              <a href="/" class="btn btn-outline">\u8FD4\u56DE\u9996\u9875</a>
              <a href="/admin/stats" class="btn btn-outline">\u7EDF\u8BA1\u6570\u636E</a>
              <a href="/admin/settings" class="btn btn-outline">\u7CFB\u7EDF\u8BBE\u7F6E</a>
            </div>
          </div>
        </div>
      </div>

      <div class="container">
        {/* \u6982\u89C8\u5361\u7247 */}
        <div class="overview-grid">
          <div class="overview-card">
            <div class="overview-icon">\u{1F4DA}</div>
            <div class="overview-content">
              <h3>\u4E66\u7B7E\u603B\u6570</h3>
              <div class="overview-number">{bookmarks.length}</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="overview-icon">\u{1F4C1}</div>
            <div class="overview-content">
              <h3>\u5206\u7C7B\u603B\u6570</h3>
              <div class="overview-number">{categories.length}</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="overview-icon">\u{1F446}</div>
            <div class="overview-content">
              <h3>\u603B\u70B9\u51FB\u91CF</h3>
              <div class="overview-number">{stats.totalClicks}</div>
            </div>
          </div>
          
          <div class="overview-card">
            <div class="overview-icon">\u{1F440}</div>
            <div class="overview-content">
              <h3>\u603B\u8BBF\u95EE\u91CF</h3>
              <div class="overview-number">{stats.totalViews}</div>
            </div>
          </div>
        </div>

        {/* \u5FEB\u901F\u64CD\u4F5C */}
        <div class="quick-actions">
          <h2>\u5FEB\u901F\u64CD\u4F5C</h2>
          <div class="action-grid">
            <button onclick="showAddBookmarkModal()" class="action-card">
              <div class="action-icon">\u2795</div>
              <div class="action-title">\u6DFB\u52A0\u4E66\u7B7E</div>
              <div class="action-desc">\u6DFB\u52A0\u65B0\u7684\u4E66\u7B7E\u5230\u6536\u85CF</div>
            </button>
            
            <button onclick="showAddCategoryModal()" class="action-card">
              <div class="action-icon">\u{1F4C1}</div>
              <div class="action-title">\u6DFB\u52A0\u5206\u7C7B</div>
              <div class="action-desc">\u521B\u5EFA\u65B0\u7684\u4E66\u7B7E\u5206\u7C7B</div>
            </button>
            
            <button onclick="showImportModal()" class="action-card">
              <div class="action-icon">\u{1F4E5}</div>
              <div class="action-title">\u5BFC\u5165\u4E66\u7B7E</div>
              <div class="action-desc">\u4ECE Chrome \u5BFC\u5165\u4E66\u7B7E</div>
            </button>
            
            <button onclick="exportBookmarks()" class="action-card">
              <div class="action-icon">\u{1F4E4}</div>
              <div class="action-title">\u5BFC\u51FA\u4E66\u7B7E</div>
              <div class="action-desc">\u5BFC\u51FA\u4E3A Chrome \u683C\u5F0F</div>
            </button>
            
            ${config.aiConfig.enabled ? `
              <button onclick="organizeWithAI()" class="action-card">
                <div class="action-icon">\u{1F916}</div>
                <div class="action-title">AI \u6574\u7406</div>
                <div class="action-desc">\u4F7F\u7528 AI \u667A\u80FD\u6574\u7406\u4E66\u7B7E</div>
              </button>
            ` : ""}
            
            <button onclick="backupData()" class="action-card">
              <div class="action-icon">\u{1F4BE}</div>
              <div class="action-title">\u5907\u4EFD\u6570\u636E</div>
              <div class="action-desc">\u5907\u4EFD\u6240\u6709\u6570\u636E</div>
            </button>
          </div>
        </div>

        {/* \u4E66\u7B7E\u7BA1\u7406 */}
        <div class="bookmarks-section">
          <div class="section-header">
            <h2>\u4E66\u7B7E\u7BA1\u7406</h2>
            <div class="section-actions">
              <input type="text" id="bookmarkSearch" placeholder="\u641C\u7D22\u4E66\u7B7E..." class="search-input" />
              <select id="categoryFilter" class="filter-select">
                <option value="">\u6240\u6709\u5206\u7C7B</option>
                ${categories.map((category) => `
                  <option value="${category.id}">${category.name}</option>
                `).join("")}
              </select>
            </div>
          </div>
          
          <div class="bookmarks-table">
            <table>
              <thead>
                <tr>
                  <th>\u6807\u9898</th>
                  <th>URL</th>
                  <th>\u5206\u7C7B</th>
                  <th>\u70B9\u51FB\u91CF</th>
                  <th>\u64CD\u4F5C</th>
                </tr>
              </thead>
              <tbody id="bookmarksTableBody">
                ${bookmarks.map((bookmark) => {
    const category = categories.find((cat) => cat.id === bookmark.category);
    return `
                    <tr data-bookmark-id="${bookmark.id}" data-category="${bookmark.category}">
                      <td>
                        <div class="bookmark-cell">
                          ${bookmark.icon ? `<img src="${bookmark.icon}" alt="" class="bookmark-icon-small" />` : ""}
                          <div>
                            <div class="bookmark-title">${bookmark.title}</div>
                            <div class="bookmark-desc">${bookmark.shortDesc || ""}</div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <a href="${bookmark.url}" target="_blank" class="bookmark-url">
                          ${bookmark.url}
                        </a>
                      </td>
                      <td>
                        <span class="category-badge">${category?.name || "\u672A\u5206\u7C7B"}</span>
                      </td>
                      <td>${bookmark.clickCount}</td>
                      <td>
                        <div class="table-actions">
                          <button onclick="editBookmark('${bookmark.id}')" class="btn-small btn-edit">\u7F16\u8F91</button>
                          <button onclick="deleteBookmark('${bookmark.id}')" class="btn-small btn-delete">\u5220\u9664</button>
                        </div>
                      </td>
                    </tr>
                  `;
  }).join("")}
              </tbody>
            </table>
          </div>
        </div>

        {/* \u5206\u7C7B\u7BA1\u7406 */}
        <div class="categories-section">
          <div class="section-header">
            <h2>\u5206\u7C7B\u7BA1\u7406</h2>
          </div>
          
          <div class="categories-grid">
            ${categories.map((category) => `
              <div class="category-card" data-category-id="${category.id}">
                <div class="category-header">
                  <div class="category-info">
                    ${category.icon ? `<span class="category-icon">${category.icon}</span>` : ""}
                    <h3>${category.name}</h3>
                  </div>
                  <div class="category-actions">
                    <button onclick="editCategory('${category.id}')" class="btn-small btn-edit">\u7F16\u8F91</button>
                    <button onclick="deleteCategory('${category.id}')" class="btn-small btn-delete">\u5220\u9664</button>
                  </div>
                </div>

                ${category.description ? `<p class="category-description">${category.description}</p>` : ""}

                <div class="category-stats">
                  <span class="category-count">
                    ${bookmarks.filter((b) => b.category === category.id).length} \u4E2A\u4E66\u7B7E
                  </span>
                  <span class="category-clicks">
                    ${stats.categoryStats[category.id] || 0} \u6B21\u8BBF\u95EE
                  </span>
                </div>
              </div>
            `).join("")}
          </div>
        </div>
      </div>

      {/* \u6A21\u6001\u6846\u5360\u4F4D\u7B26 */}
      <div id="modalContainer"></div>

      {/* \u6837\u5F0F */}
      <style>
        .admin-panel {
          min-height: 100vh;
          background: #f8f9fa;
        }
        
        .admin-header {
          background: white;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin-bottom: 30px;
        }
        
        .admin-nav {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 0;
        }
        
        .admin-nav h1 {
          color: #1e293b;
          margin: 0;
        }
        
        .admin-nav-links {
          display: flex;
          gap: 10px;
        }
        
        .btn-outline {
          background: transparent;
          border: 2px solid #7C3AED;
          color: #7C3AED;
        }
        
        .btn-outline:hover {
          background: #7C3AED;
          color: white;
        }
        
        .overview-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-bottom: 40px;
        }
        
        .overview-card {
          background: white;
          padding: 25px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          display: flex;
          align-items: center;
        }
        
        .overview-icon {
          font-size: 2.5em;
          margin-right: 20px;
        }
        
        .overview-number {
          font-size: 2em;
          font-weight: bold;
          color: #7C3AED;
        }
        
        .quick-actions {
          margin-bottom: 40px;
        }
        
        .action-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-top: 20px;
        }
        
        .action-card {
          background: white;
          border: none;
          padding: 25px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
        }
        
        .action-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .action-icon {
          font-size: 2.5em;
          margin-bottom: 15px;
        }
        
        .action-title {
          font-size: 1.2em;
          font-weight: 600;
          margin-bottom: 8px;
          color: #1e293b;
        }
        
        .action-desc {
          color: #64748b;
          font-size: 0.9em;
        }
        
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }
        
        .section-actions {
          display: flex;
          gap: 10px;
        }
        
        .search-input, .filter-select {
          padding: 8px 12px;
          border: 2px solid #e2e8f0;
          border-radius: 6px;
          font-size: 14px;
        }
        
        .bookmarks-table {
          background: white;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .bookmarks-table table {
          width: 100%;
          border-collapse: collapse;
        }
        
        .bookmarks-table th,
        .bookmarks-table td {
          padding: 15px;
          text-align: left;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .bookmarks-table th {
          background: #f8f9fa;
          font-weight: 600;
          color: #374151;
        }
        
        .bookmark-cell {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        
        .bookmark-icon-small {
          width: 24px;
          height: 24px;
          border-radius: 4px;
        }
        
        .bookmark-title {
          font-weight: 500;
          color: #1e293b;
        }
        
        .bookmark-desc {
          font-size: 0.85em;
          color: #64748b;
        }
        
        .bookmark-url {
          color: #7C3AED;
          text-decoration: none;
          font-size: 0.9em;
        }
        
        .category-badge {
          background: #e0e7ff;
          color: #3730a3;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.8em;
        }
        
        .table-actions {
          display: flex;
          gap: 5px;
        }
        
        .btn-small {
          padding: 4px 8px;
          font-size: 0.8em;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
        
        .btn-edit {
          background: #3b82f6;
          color: white;
        }
        
        .btn-delete {
          background: #ef4444;
          color: white;
        }
        
        .categories-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
        }
        
        .category-card {
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .category-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }
        
        .category-info {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .category-info h3 {
          margin: 0;
          color: #1e293b;
        }
        
        .category-description {
          color: #64748b;
          margin: 10px 0;
        }
        
        .category-stats {
          display: flex;
          justify-content: space-between;
          font-size: 0.9em;
          color: #64748b;
        }
      </style>

      {/* JavaScript */}
      <script>
        // \u641C\u7D22\u548C\u8FC7\u6EE4\u529F\u80FD
        document.getElementById('bookmarkSearch').addEventListener('input', filterBookmarks);
        document.getElementById('categoryFilter').addEventListener('change', filterBookmarks);
        
        function filterBookmarks() {
          const searchTerm = document.getElementById('bookmarkSearch').value.toLowerCase();
          const categoryFilter = document.getElementById('categoryFilter').value;
          const rows = document.querySelectorAll('#bookmarksTableBody tr');
          
          rows.forEach(row => {
            const title = row.querySelector('.bookmark-title').textContent.toLowerCase();
            const url = row.querySelector('.bookmark-url').textContent.toLowerCase();
            const category = row.dataset.category;
            
            const matchesSearch = title.includes(searchTerm) || url.includes(searchTerm);
            const matchesCategory = !categoryFilter || category === categoryFilter;
            
            row.style.display = matchesSearch && matchesCategory ? '' : 'none';
          });
        }
        
        // \u5360\u4F4D\u7B26\u51FD\u6570\uFF0C\u5C06\u5728\u540E\u7EED\u5B9E\u73B0
        function showAddBookmarkModal() { alert('\u6DFB\u52A0\u4E66\u7B7E\u529F\u80FD\u5373\u5C06\u5B9E\u73B0'); }
        function showAddCategoryModal() { alert('\u6DFB\u52A0\u5206\u7C7B\u529F\u80FD\u5373\u5C06\u5B9E\u73B0'); }
        function showImportModal() { alert('\u5BFC\u5165\u529F\u80FD\u5373\u5C06\u5B9E\u73B0'); }
        function exportBookmarks() { alert('\u5BFC\u51FA\u529F\u80FD\u5373\u5C06\u5B9E\u73B0'); }
        function organizeWithAI() { alert('AI\u6574\u7406\u529F\u80FD\u5373\u5C06\u5B9E\u73B0'); }
        function backupData() { alert('\u5907\u4EFD\u529F\u80FD\u5373\u5C06\u5B9E\u73B0'); }
        function editBookmark(id) { alert('\u7F16\u8F91\u4E66\u7B7E: ' + id); }
        function deleteBookmark(id) { alert('\u5220\u9664\u4E66\u7B7E: ' + id); }
        function editCategory(id) { alert('\u7F16\u8F91\u5206\u7C7B: ' + id); }
        function deleteCategory(id) { alert('\u5220\u9664\u5206\u7C7B: ' + id); }
      <\/script>
    </div>
  `;
}, "AdminPanel");

// src/routes/admin.ts
var app3 = new Hono2();
app3.get("/", async (c) => {
  try {
    const kvService = c.get("kvService");
    const [bookmarks, categories, stats, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getStats(),
      kvService.getConfig()
    ]);
    const content = AdminPanel({ bookmarks, categories, stats, config });
    const html = Layout({
      title: `\u7BA1\u7406\u9762\u677F - ${config.siteName}`,
      description: "\u7BA1\u7406\u60A8\u7684\u4E66\u7B7E\u548C\u5206\u7C7B",
      children: content
    });
    return c.html(html);
  } catch (error) {
    console.error("Admin panel error:", error);
    return c.html(Layout({
      title: "CloudNav - Admin Error",
      description: "Admin error",
      children: '<div class="error">\u7BA1\u7406\u9762\u677F\u52A0\u8F7D\u5931\u8D25\u3002</div>'
    }));
  }
});
app3.get("/stats", async (c) => {
  try {
    const kvService = c.get("kvService");
    const [stats, config] = await Promise.all([
      kvService.getStats(),
      kvService.getConfig()
    ]);
    const statsContent = `
      <div class="admin-stats">
        <h1>\u7EDF\u8BA1\u6570\u636E</h1>
        
        <div class="stats-grid">
          <div class="stat-card">
            <h3>\u603B\u70B9\u51FB\u91CF</h3>
            <div class="stat-number">${stats.totalClicks}</div>
          </div>
          
          <div class="stat-card">
            <h3>\u603B\u8BBF\u95EE\u91CF</h3>
            <div class="stat-number">${stats.totalViews}</div>
          </div>
          
          <div class="stat-card">
            <h3>\u79FB\u52A8\u7AEF\u8BBF\u95EE</h3>
            <div class="stat-number">${stats.deviceStats.mobile}</div>
          </div>
          
          <div class="stat-card">
            <h3>\u684C\u9762\u7AEF\u8BBF\u95EE</h3>
            <div class="stat-number">${stats.deviceStats.desktop}</div>
          </div>
        </div>
        
        <div class="stats-section">
          <h2>\u70ED\u95E8\u4E66\u7B7E</h2>
          <div class="stats-list">
            ${Object.entries(stats.bookmarkStats).sort(([, a], [, b]) => b - a).slice(0, 10).map(([id, count]) => `
                <div class="stats-item">
                  <span class="stats-label">${id}</span>
                  <span class="stats-value">${count} \u6B21\u70B9\u51FB</span>
                </div>
              `).join("")}
          </div>
        </div>
        
        <div class="stats-section">
          <h2>\u70ED\u95E8\u641C\u7D22</h2>
          <div class="stats-list">
            ${Object.entries(stats.searchStats).sort(([, a], [, b]) => b - a).slice(0, 10).map(([query, count]) => `
                <div class="stats-item">
                  <span class="stats-label">${query}</span>
                  <span class="stats-value">${count} \u6B21\u641C\u7D22</span>
                </div>
              `).join("")}
          </div>
        </div>
        
        <div class="admin-actions">
          <a href="/admin" class="btn">\u8FD4\u56DE\u7BA1\u7406\u9762\u677F</a>
          <button onclick="exportStats()" class="btn">\u5BFC\u51FA\u7EDF\u8BA1\u6570\u636E</button>
        </div>
      </div>
      
      <style>
        .admin-stats {
          max-width: 1000px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin: 30px 0;
        }
        
        .stat-card {
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          text-align: center;
        }
        
        .stat-number {
          font-size: 2.5em;
          font-weight: bold;
          color: #7C3AED;
          margin-top: 10px;
        }
        
        .stats-section {
          background: white;
          padding: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          margin: 20px 0;
        }
        
        .stats-list {
          margin-top: 15px;
        }
        
        .stats-item {
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .stats-item:last-child {
          border-bottom: none;
        }
        
        .stats-label {
          font-weight: 500;
        }
        
        .stats-value {
          color: #7C3AED;
          font-weight: 600;
        }
        
        .admin-actions {
          text-align: center;
          margin-top: 30px;
        }
        
        .admin-actions .btn {
          margin: 0 10px;
        }
      </style>
      
      <script>
        function exportStats() {
          fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
              const blob = new Blob([JSON.stringify(data.data, null, 2)], {
                type: 'application/json'
              });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'cloudnav-stats-' + new Date().toISOString().split('T')[0] + '.json';
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            })
            .catch(error => {
              console.error('Export error:', error);
              alert('\u5BFC\u51FA\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002');
            });
        }
      <\/script>
    `;
    const html = Layout({
      title: `\u7EDF\u8BA1\u6570\u636E - ${config.siteName}`,
      description: "\u67E5\u770B\u7F51\u7AD9\u7EDF\u8BA1\u6570\u636E",
      children: statsContent
    });
    return c.html(html);
  } catch (error) {
    console.error("Admin stats error:", error);
    return c.html(Layout({
      title: "CloudNav - Stats Error",
      description: "Stats error",
      children: '<div class="error">\u7EDF\u8BA1\u6570\u636E\u52A0\u8F7D\u5931\u8D25\u3002</div>'
    }));
  }
});
app3.get("/settings", async (c) => {
  try {
    const kvService = c.get("kvService");
    const config = await kvService.getConfig();
    const settingsContent = `
      <div class="admin-settings">
        <h1>\u7CFB\u7EDF\u8BBE\u7F6E</h1>
        
        <form id="settingsForm" class="settings-form">
          <div class="form-section">
            <h2>\u57FA\u672C\u8BBE\u7F6E</h2>
            
            <div class="form-group">
              <label for="siteName">\u7F51\u7AD9\u540D\u79F0</label>
              <input type="text" id="siteName" name="siteName" value="${config.siteName}" required>
            </div>
            
            <div class="form-group">
              <label for="siteDescription">\u7F51\u7AD9\u63CF\u8FF0</label>
              <textarea id="siteDescription" name="siteDescription" rows="3" required>${config.siteDescription}</textarea>
            </div>
          </div>
          
          <div class="form-section">
            <h2>AI \u8BBE\u7F6E</h2>
            
            <div class="form-group">
              <label>
                <input type="checkbox" id="aiEnabled" name="aiEnabled" ${config.aiConfig.enabled ? "checked" : ""}>
                \u542F\u7528 AI \u529F\u80FD
              </label>
            </div>
            
            <div class="form-group">
              <label for="aiApiUrl">AI API \u5730\u5740</label>
              <input type="url" id="aiApiUrl" name="aiApiUrl" value="${config.aiConfig.apiUrl || ""}" placeholder="https://api.openai.com/v1">
            </div>
            
            <div class="form-group">
              <label for="aiApiKey">AI API \u5BC6\u94A5</label>
              <input type="password" id="aiApiKey" name="aiApiKey" value="${config.aiConfig.apiKey || ""}" placeholder="sk-...">
            </div>
            
            <div class="form-group">
              <label for="aiModel">AI \u6A21\u578B</label>
              <input type="text" id="aiModel" name="aiModel" value="${config.aiConfig.model || ""}" placeholder="gpt-3.5-turbo">
            </div>
          </div>
          
          <div class="form-section">
            <h2>\u529F\u80FD\u8BBE\u7F6E</h2>
            
            <div class="form-group">
              <label>
                <input type="checkbox" id="statsEnabled" name="statsEnabled" ${config.features.stats ? "checked" : ""}>
                \u542F\u7528\u7EDF\u8BA1\u529F\u80FD
              </label>
            </div>
            
            <div class="form-group">
              <label>
                <input type="checkbox" id="importEnabled" name="importEnabled" ${config.features.import ? "checked" : ""}>
                \u542F\u7528\u5BFC\u5165\u529F\u80FD
              </label>
            </div>
            
            <div class="form-group">
              <label>
                <input type="checkbox" id="exportEnabled" name="exportEnabled" ${config.features.export ? "checked" : ""}>
                \u542F\u7528\u5BFC\u51FA\u529F\u80FD
              </label>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">\u4FDD\u5B58\u8BBE\u7F6E</button>
            <a href="/admin" class="btn">\u53D6\u6D88</a>
          </div>
        </form>
      </div>
      
      <style>
        .admin-settings {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .settings-form {
          background: white;
          padding: 30px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-section {
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .form-section:last-of-type {
          border-bottom: none;
        }
        
        .form-group {
          margin-bottom: 20px;
        }
        
        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
          color: #374151;
        }
        
        .form-group input,
        .form-group textarea {
          width: 100%;
          padding: 10px 12px;
          border: 2px solid #e2e8f0;
          border-radius: 8px;
          font-size: 16px;
          transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
          outline: none;
          border-color: #7C3AED;
        }
        
        .form-group input[type="checkbox"] {
          width: auto;
          margin-right: 8px;
        }
        
        .form-actions {
          text-align: center;
          margin-top: 30px;
        }
        
        .form-actions .btn {
          margin: 0 10px;
        }
        
        .btn-primary {
          background: #7C3AED;
          color: white;
        }
        
        .btn-primary:hover {
          background: #6D28D9;
        }
      </style>
      
      <script>
        document.getElementById('settingsForm').addEventListener('submit', async function(e) {
          e.preventDefault();
          
          const formData = new FormData(this);
          const settings = {
            siteName: formData.get('siteName'),
            siteDescription: formData.get('siteDescription'),
            aiConfig: {
              enabled: formData.has('aiEnabled'),
              apiUrl: formData.get('aiApiUrl'),
              apiKey: formData.get('aiApiKey'),
              model: formData.get('aiModel')
            },
            features: {
              stats: formData.has('statsEnabled'),
              ai: formData.has('aiEnabled'),
              import: formData.has('importEnabled'),
              export: formData.has('exportEnabled')
            }
          };
          
          try {
            const response = await fetch('/admin/api/config', {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(settings)
            });
            
            if (response.ok) {
              alert('\u8BBE\u7F6E\u4FDD\u5B58\u6210\u529F\uFF01');
              window.location.href = '/admin';
            } else {
              alert('\u4FDD\u5B58\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002');
            }
          } catch (error) {
            console.error('Save error:', error);
            alert('\u4FDD\u5B58\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002');
          }
        });
      <\/script>
    `;
    const html = Layout({
      title: `\u7CFB\u7EDF\u8BBE\u7F6E - ${config.siteName}`,
      description: "\u914D\u7F6E\u7CFB\u7EDF\u8BBE\u7F6E",
      children: settingsContent
    });
    return c.html(html);
  } catch (error) {
    console.error("Admin settings error:", error);
    return c.html(Layout({
      title: "CloudNav - Settings Error",
      description: "Settings error",
      children: '<div class="error">\u8BBE\u7F6E\u9875\u9762\u52A0\u8F7D\u5931\u8D25\u3002</div>'
    }));
  }
});

// src/index.ts
var app4 = new Hono2();
app4.use("*", logger());
app4.use("*", secureHeaders());
app4.use("*", prettyJSON());
app4.use("*", cors({
  origin: "*",
  allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowHeaders: ["Content-Type", "Authorization"]
}));
app4.use("*", async (c, next) => {
  const kvService = new KVService(c.env.CLOUDNAV_KV);
  c.set("kvService", kvService);
  await next();
});
app4.get("/health", (c) => {
  return c.json({
    status: "ok",
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    version: "2.0.0"
  });
});
app4.route("/api", app);
app4.route("/admin", app3);
app4.route("/", app2);
app4.notFound((c) => {
  return c.json({ error: "Not Found" }, 404);
});
app4.onError((err, c) => {
  console.error("Application error:", err);
  return c.json({
    error: "Internal Server Error",
    message: c.env.ENVIRONMENT === "development" ? err.message : void 0
  }, 500);
});
var src_default = app4;

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-iThTat/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-iThTat/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
