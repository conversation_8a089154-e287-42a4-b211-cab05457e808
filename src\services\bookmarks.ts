// CloudNav 2.0 - 书签管理服务
import { nanoid } from 'nanoid';
import type { Bookmark, Category } from '@/types';
import { KVService } from './kv';

export class BookmarkService {
  private kvService: KVService;

  constructor(kvService: KVService) {
    this.kvService = kvService;
  }

  // 获取所有书签
  async getAllBookmarks(): Promise<Bookmark[]> {
    return await this.kvService.getBookmarks();
  }

  // 根据ID获取书签
  async getBookmarkById(id: string): Promise<Bookmark | null> {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.find(bookmark => bookmark.id === id) || null;
  }

  // 根据分类获取书签
  async getBookmarksByCategory(categoryId: string): Promise<Bookmark[]> {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.filter(bookmark => bookmark.category === categoryId);
  }

  // 搜索书签
  async searchBookmarks(query: string): Promise<Bookmark[]> {
    const bookmarks = await this.getAllBookmarks();
    const searchTerm = query.toLowerCase().trim();
    
    if (!searchTerm) return bookmarks;

    return bookmarks.filter(bookmark => 
      bookmark.title.toLowerCase().includes(searchTerm) ||
      bookmark.description?.toLowerCase().includes(searchTerm) ||
      bookmark.shortDesc?.toLowerCase().includes(searchTerm) ||
      bookmark.url.toLowerCase().includes(searchTerm) ||
      bookmark.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  // 创建新书签
  async createBookmark(bookmarkData: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt' | 'clickCount'>): Promise<Bookmark> {
    const now = Date.now();
    const newBookmark: Bookmark = {
      id: nanoid(),
      ...bookmarkData,
      createdAt: now,
      updatedAt: now,
      clickCount: 0
    };

    const success = await this.kvService.addBookmark(newBookmark);
    if (!success) {
      throw new Error('Failed to create bookmark');
    }

    return newBookmark;
  }

  // 更新书签
  async updateBookmark(id: string, updates: Partial<Omit<Bookmark, 'id' | 'createdAt'>>): Promise<Bookmark | null> {
    const bookmark = await this.getBookmarkById(id);
    if (!bookmark) {
      return null;
    }

    const updatedData = {
      ...updates,
      updatedAt: Date.now()
    };

    const success = await this.kvService.updateBookmark(id, updatedData);
    if (!success) {
      throw new Error('Failed to update bookmark');
    }

    return { ...bookmark, ...updatedData };
  }

  // 删除书签
  async deleteBookmark(id: string): Promise<boolean> {
    const bookmark = await this.getBookmarkById(id);
    if (!bookmark) {
      return false;
    }

    return await this.kvService.deleteBookmark(id);
  }

  // 批量删除书签
  async deleteBookmarks(ids: string[]): Promise<{ success: string[], failed: string[] }> {
    const results: { success: string[], failed: string[] } = { success: [], failed: [] };

    for (const id of ids) {
      const deleted = await this.deleteBookmark(id);
      if (deleted) {
        results.success.push(id);
      } else {
        results.failed.push(id);
      }
    }

    return results;
  }

  // 移动书签到不同分类
  async moveBookmarkToCategory(bookmarkId: string, newCategoryId: string): Promise<boolean> {
    return await this.updateBookmark(bookmarkId, { category: newCategoryId }) !== null;
  }

  // 批量移动书签
  async moveBookmarksToCategory(bookmarkIds: string[], newCategoryId: string): Promise<{ success: string[], failed: string[] }> {
    const results: { success: string[], failed: string[] } = { success: [], failed: [] };

    for (const id of bookmarkIds) {
      const moved = await this.moveBookmarkToCategory(id, newCategoryId);
      if (moved) {
        results.success.push(id);
      } else {
        results.failed.push(id);
      }
    }

    return results;
  }

  // 增加点击计数
  async incrementClickCount(id: string): Promise<boolean> {
    const bookmark = await this.getBookmarkById(id);
    if (!bookmark) {
      return false;
    }

    const success = await this.kvService.updateBookmark(id, {
      clickCount: bookmark.clickCount + 1,
      updatedAt: Date.now()
    });

    // 同时更新统计数据
    if (success) {
      await this.kvService.incrementBookmarkClick(id);
    }

    return success;
  }

  // 获取热门书签
  async getPopularBookmarks(limit: number = 10): Promise<Bookmark[]> {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks
      .sort((a, b) => b.clickCount - a.clickCount)
      .slice(0, limit);
  }

  // 获取最近添加的书签
  async getRecentBookmarks(limit: number = 10): Promise<Bookmark[]> {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, limit);
  }

  // 获取最近更新的书签
  async getRecentlyUpdatedBookmarks(limit: number = 10): Promise<Bookmark[]> {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks
      .sort((a, b) => b.updatedAt - a.updatedAt)
      .slice(0, limit);
  }

  // 验证书签数据
  validateBookmarkData(data: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {
      errors.push('标题是必需的');
    }

    if (!data.url || typeof data.url !== 'string') {
      errors.push('URL是必需的');
    } else {
      try {
        new URL(data.url);
      } catch {
        errors.push('URL格式无效');
      }
    }

    if (!data.category || typeof data.category !== 'string') {
      errors.push('分类是必需的');
    }

    if (data.tags && !Array.isArray(data.tags)) {
      errors.push('标签必须是数组');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // 检查重复书签
  async checkDuplicateBookmark(url: string, excludeId?: string): Promise<Bookmark | null> {
    const bookmarks = await this.getAllBookmarks();
    return bookmarks.find(bookmark => 
      bookmark.url === url && bookmark.id !== excludeId
    ) || null;
  }

  // 获取书签统计信息
  async getBookmarkStats(): Promise<{
    total: number;
    byCategory: Record<string, number>;
    totalClicks: number;
    averageClicksPerBookmark: number;
  }> {
    const bookmarks = await this.getAllBookmarks();
    const total = bookmarks.length;
    const totalClicks = bookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);
    
    const byCategory: Record<string, number> = {};
    bookmarks.forEach(bookmark => {
      byCategory[bookmark.category] = (byCategory[bookmark.category] || 0) + 1;
    });

    return {
      total,
      byCategory,
      totalClicks,
      averageClicksPerBookmark: total > 0 ? totalClicks / total : 0
    };
  }

  // 导出书签数据
  async exportBookmarks(): Promise<Bookmark[]> {
    return await this.getAllBookmarks();
  }

  // 导入书签数据
  async importBookmarks(bookmarks: Bookmark[], options: { 
    overwrite?: boolean; 
    skipDuplicates?: boolean 
  } = {}): Promise<{ 
    imported: number; 
    skipped: number; 
    errors: string[] 
  }> {
    const result: { imported: number; skipped: number; errors: string[] } = { imported: 0, skipped: 0, errors: [] };
    
    for (const bookmark of bookmarks) {
      try {
        // 验证数据
        const validation = this.validateBookmarkData(bookmark);
        if (!validation.valid) {
          result.errors.push(`书签 "${bookmark.title}" 验证失败: ${validation.errors.join(', ')}`);
          continue;
        }

        // 检查重复
        if (options.skipDuplicates) {
          const duplicate = await this.checkDuplicateBookmark(bookmark.url);
          if (duplicate) {
            result.skipped++;
            continue;
          }
        }

        // 创建书签
        await this.createBookmark({
          title: bookmark.title,
          url: bookmark.url,
          description: bookmark.description,
          shortDesc: bookmark.shortDesc,
          category: bookmark.category,
          icon: bookmark.icon,
          tags: bookmark.tags
        });

        result.imported++;
      } catch (error) {
        result.errors.push(`导入书签 "${bookmark.title}" 失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return result;
  }
}
