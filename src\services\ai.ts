// CloudNav 2.0 - AI 智能整理服务
import type { Bookmark, Category, AIConfig } from '@/types';
import { BookmarkService } from './bookmarks';
import { CategoryService } from './categories';

export interface AIAnalysisResult {
  suggestions: {
    categoryRecommendations: Array<{
      bookmarkId: string;
      currentCategory: string;
      suggestedCategory: string;
      confidence: number;
      reason: string;
    }>;
    duplicateDetection: Array<{
      bookmarks: Bookmark[];
      similarity: number;
      reason: string;
    }>;
    descriptionSuggestions: Array<{
      bookmarkId: string;
      currentDescription: string;
      suggestedDescription: string;
      confidence: number;
    }>;
    tagSuggestions: Array<{
      bookmarkId: string;
      suggestedTags: string[];
      confidence: number;
    }>;
    newCategorySuggestions: Array<{
      name: string;
      description: string;
      bookmarkIds: string[];
      confidence: number;
    }>;
  };
  statistics: {
    totalAnalyzed: number;
    categorySuggestions: number;
    duplicatesFound: number;
    descriptionSuggestions: number;
    tagSuggestions: number;
  };
}

export interface AIOrganizeOptions {
  enableCategoryRecommendations: boolean;
  enableDuplicateDetection: boolean;
  enableDescriptionGeneration: boolean;
  enableTagSuggestions: boolean;
  enableNewCategoryCreation: boolean;
  confidenceThreshold: number; // 0-1
  autoApply: boolean;
}

export class AIService {
  private bookmarkService: BookmarkService;
  private categoryService: CategoryService;
  private aiConfig: AIConfig;

  constructor(
    bookmarkService: BookmarkService, 
    categoryService: CategoryService,
    aiConfig: AIConfig
  ) {
    this.bookmarkService = bookmarkService;
    this.categoryService = categoryService;
    this.aiConfig = aiConfig;
  }

  // 智能分析书签
  async analyzeBookmarks(options: AIOrganizeOptions): Promise<AIAnalysisResult> {
    const [bookmarks, categories] = await Promise.all([
      this.bookmarkService.getAllBookmarks(),
      this.categoryService.getAllCategories()
    ]);

    const result: AIAnalysisResult = {
      suggestions: {
        categoryRecommendations: [],
        duplicateDetection: [],
        descriptionSuggestions: [],
        tagSuggestions: [],
        newCategorySuggestions: []
      },
      statistics: {
        totalAnalyzed: bookmarks.length,
        categorySuggestions: 0,
        duplicatesFound: 0,
        descriptionSuggestions: 0,
        tagSuggestions: 0
      }
    };

    // 分类推荐
    if (options.enableCategoryRecommendations) {
      result.suggestions.categoryRecommendations = await this.analyzeCategoryRecommendations(
        bookmarks, 
        categories, 
        options.confidenceThreshold
      );
      result.statistics.categorySuggestions = result.suggestions.categoryRecommendations.length;
    }

    // 重复检测
    if (options.enableDuplicateDetection) {
      result.suggestions.duplicateDetection = await this.detectDuplicates(
        bookmarks, 
        options.confidenceThreshold
      );
      result.statistics.duplicatesFound = result.suggestions.duplicateDetection.length;
    }

    // 描述生成
    if (options.enableDescriptionGeneration) {
      result.suggestions.descriptionSuggestions = await this.generateDescriptions(
        bookmarks, 
        options.confidenceThreshold
      );
      result.statistics.descriptionSuggestions = result.suggestions.descriptionSuggestions.length;
    }

    // 标签建议
    if (options.enableTagSuggestions) {
      result.suggestions.tagSuggestions = await this.suggestTags(
        bookmarks, 
        options.confidenceThreshold
      );
      result.statistics.tagSuggestions = result.suggestions.tagSuggestions.length;
    }

    // 新分类建议
    if (options.enableNewCategoryCreation) {
      result.suggestions.newCategorySuggestions = await this.suggestNewCategories(
        bookmarks, 
        categories, 
        options.confidenceThreshold
      );
    }

    return result;
  }

  // 分析分类推荐
  private async analyzeCategoryRecommendations(
    bookmarks: Bookmark[], 
    categories: Category[], 
    threshold: number
  ) {
    const recommendations: any[] = [];

    for (const bookmark of bookmarks) {
      const suggestion = this.suggestCategoryForBookmark(bookmark, categories);
      
      if (suggestion && suggestion.confidence >= threshold && 
          suggestion.suggestedCategory !== bookmark.category) {
        recommendations.push({
          bookmarkId: bookmark.id,
          currentCategory: bookmark.category,
          suggestedCategory: suggestion.suggestedCategory,
          confidence: suggestion.confidence,
          reason: suggestion.reason
        });
      }
    }

    return recommendations;
  }

  // 为单个书签建议分类
  private suggestCategoryForBookmark(bookmark: Bookmark, categories: Category[]) {
    const url = bookmark.url.toLowerCase();
    const title = bookmark.title.toLowerCase();
    const description = (bookmark.description || '').toLowerCase();
    
    // 基于 URL 域名的分类规则
    const domainRules = [
      { domains: ['github.com', 'gitlab.com', 'bitbucket.org'], category: '开发工具', confidence: 0.9 },
      { domains: ['stackoverflow.com', 'stackexchange.com'], category: '技术问答', confidence: 0.9 },
      { domains: ['youtube.com', 'youtu.be'], category: '视频', confidence: 0.9 },
      { domains: ['twitter.com', 'facebook.com', 'instagram.com'], category: '社交媒体', confidence: 0.8 },
      { domains: ['amazon.com', 'taobao.com', 'jd.com'], category: '购物', confidence: 0.8 },
      { domains: ['news.', 'bbc.com', 'cnn.com'], category: '新闻', confidence: 0.8 },
      { domains: ['wikipedia.org'], category: '百科', confidence: 0.9 },
      { domains: ['docs.', 'documentation'], category: '文档', confidence: 0.8 }
    ];

    // 基于关键词的分类规则
    const keywordRules = [
      { keywords: ['api', 'documentation', 'docs'], category: '文档', confidence: 0.7 },
      { keywords: ['tutorial', 'guide', 'how to'], category: '教程', confidence: 0.7 },
      { keywords: ['tool', 'utility', 'generator'], category: '工具', confidence: 0.7 },
      { keywords: ['blog', 'article'], category: '博客', confidence: 0.6 },
      { keywords: ['design', 'ui', 'ux'], category: '设计', confidence: 0.7 },
      { keywords: ['game', 'gaming'], category: '游戏', confidence: 0.8 }
    ];

    let bestMatch = null;
    let maxConfidence = 0;

    // 检查域名规则
    for (const rule of domainRules) {
      for (const domain of rule.domains) {
        if (url.includes(domain)) {
          const category = categories.find(c => c.name.includes(rule.category));
          if (category && rule.confidence > maxConfidence) {
            bestMatch = {
              suggestedCategory: category.id,
              confidence: rule.confidence,
              reason: `基于域名 ${domain} 的智能分类`
            };
            maxConfidence = rule.confidence;
          }
        }
      }
    }

    // 检查关键词规则
    for (const rule of keywordRules) {
      for (const keyword of rule.keywords) {
        if (title.includes(keyword) || description.includes(keyword)) {
          const category = categories.find(c => c.name.includes(rule.category));
          if (category && rule.confidence > maxConfidence) {
            bestMatch = {
              suggestedCategory: category.id,
              confidence: rule.confidence,
              reason: `基于关键词 "${keyword}" 的智能分类`
            };
            maxConfidence = rule.confidence;
          }
        }
      }
    }

    return bestMatch;
  }

  // 检测重复书签
  private async detectDuplicates(bookmarks: Bookmark[], threshold: number) {
    const duplicates: any[] = [];
    const processed = new Set<string>();

    for (let i = 0; i < bookmarks.length; i++) {
      if (processed.has(bookmarks[i].id)) continue;

      const similarBookmarks = [bookmarks[i]];
      
      for (let j = i + 1; j < bookmarks.length; j++) {
        if (processed.has(bookmarks[j].id)) continue;

        const similarity = this.calculateSimilarity(bookmarks[i], bookmarks[j]);
        
        if (similarity >= threshold) {
          similarBookmarks.push(bookmarks[j]);
          processed.add(bookmarks[j].id);
        }
      }

      if (similarBookmarks.length > 1) {
        duplicates.push({
          bookmarks: similarBookmarks,
          similarity: this.calculateGroupSimilarity(similarBookmarks),
          reason: this.getDuplicateReason(similarBookmarks)
        });
        
        similarBookmarks.forEach(b => processed.add(b.id));
      }
    }

    return duplicates;
  }

  // 计算两个书签的相似度
  private calculateSimilarity(bookmark1: Bookmark, bookmark2: Bookmark): number {
    // URL 完全相同
    if (bookmark1.url === bookmark2.url) {
      return 1.0;
    }

    // 标题相似度
    const titleSimilarity = this.calculateTextSimilarity(bookmark1.title, bookmark2.title);
    
    // URL 相似度（去除协议和参数）
    const url1Clean = this.cleanUrl(bookmark1.url);
    const url2Clean = this.cleanUrl(bookmark2.url);
    const urlSimilarity = this.calculateTextSimilarity(url1Clean, url2Clean);

    // 综合相似度
    return (titleSimilarity * 0.4 + urlSimilarity * 0.6);
  }

  // 计算文本相似度（简单的 Jaccard 相似度）
  private calculateTextSimilarity(text1: string, text2: string): number {
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  // 清理 URL
  private cleanUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return `${urlObj.hostname}${urlObj.pathname}`;
    } catch {
      return url;
    }
  }

  // 计算组相似度
  private calculateGroupSimilarity(bookmarks: Bookmark[]): number {
    if (bookmarks.length < 2) return 0;
    
    let totalSimilarity = 0;
    let comparisons = 0;
    
    for (let i = 0; i < bookmarks.length; i++) {
      for (let j = i + 1; j < bookmarks.length; j++) {
        totalSimilarity += this.calculateSimilarity(bookmarks[i], bookmarks[j]);
        comparisons++;
      }
    }
    
    return comparisons > 0 ? totalSimilarity / comparisons : 0;
  }

  // 获取重复原因
  private getDuplicateReason(bookmarks: Bookmark[]): string {
    if (bookmarks.length < 2) return '';
    
    // 检查是否有完全相同的 URL
    const urls = bookmarks.map(b => b.url);
    const uniqueUrls = new Set(urls);
    
    if (uniqueUrls.size < urls.length) {
      return '发现完全相同的 URL';
    }
    
    // 检查标题相似度
    const titles = bookmarks.map(b => b.title);
    const titleSimilarity = this.calculateTextSimilarity(titles[0], titles[1]);
    
    if (titleSimilarity > 0.8) {
      return '标题高度相似';
    }
    
    return 'URL 和内容相似';
  }

  // 生成描述建议
  private async generateDescriptions(bookmarks: Bookmark[], threshold: number) {
    const suggestions: any[] = [];

    for (const bookmark of bookmarks) {
      if (!bookmark.description || bookmark.description.length < 10) {
        const suggestion = this.generateDescriptionForBookmark(bookmark);
        
        if (suggestion && suggestion.confidence >= threshold) {
          suggestions.push({
            bookmarkId: bookmark.id,
            currentDescription: bookmark.description || '',
            suggestedDescription: suggestion.description,
            confidence: suggestion.confidence
          });
        }
      }
    }

    return suggestions;
  }

  // 为单个书签生成描述
  private generateDescriptionForBookmark(bookmark: Bookmark) {
    const url = bookmark.url.toLowerCase();
    const title = bookmark.title;
    
    // 基于域名生成描述
    const domainDescriptions: Record<string, string> = {
      'github.com': '开源代码仓库和协作平台',
      'stackoverflow.com': '程序员问答社区',
      'youtube.com': '视频分享平台',
      'wikipedia.org': '在线百科全书',
      'twitter.com': '社交媒体平台',
      'linkedin.com': '职业社交网络',
      'medium.com': '在线发布平台',
      'dev.to': '开发者社区'
    };

    for (const [domain, description] of Object.entries(domainDescriptions)) {
      if (url.includes(domain)) {
        return {
          description: `${title} - ${description}`,
          confidence: 0.8
        };
      }
    }

    // 基于 URL 路径生成描述
    if (url.includes('/docs/') || url.includes('/documentation/')) {
      return {
        description: `${title} - 技术文档和使用指南`,
        confidence: 0.7
      };
    }

    if (url.includes('/blog/') || url.includes('/article/')) {
      return {
        description: `${title} - 博客文章`,
        confidence: 0.7
      };
    }

    if (url.includes('/tutorial/') || url.includes('/guide/')) {
      return {
        description: `${title} - 教程指南`,
        confidence: 0.7
      };
    }

    // 默认描述
    return {
      description: `${title} - 网页书签`,
      confidence: 0.5
    };
  }

  // 建议标签
  private async suggestTags(bookmarks: Bookmark[], threshold: number) {
    const suggestions: any[] = [];

    for (const bookmark of bookmarks) {
      const tags = this.generateTagsForBookmark(bookmark);
      
      if (tags && tags.confidence >= threshold) {
        suggestions.push({
          bookmarkId: bookmark.id,
          suggestedTags: tags.tags,
          confidence: tags.confidence
        });
      }
    }

    return suggestions;
  }

  // 为单个书签生成标签
  private generateTagsForBookmark(bookmark: Bookmark) {
    const url = bookmark.url.toLowerCase();
    const title = bookmark.title.toLowerCase();
    const tags: string[] = [];
    let confidence = 0;

    // 基于域名的标签
    const domainTags: Record<string, string[]> = {
      'github.com': ['开源', '代码', 'Git'],
      'stackoverflow.com': ['编程', '问答', '技术'],
      'youtube.com': ['视频', '娱乐'],
      'twitter.com': ['社交', '新闻'],
      'linkedin.com': ['职业', '社交', '求职'],
      'medium.com': ['博客', '文章'],
      'wikipedia.org': ['百科', '知识']
    };

    for (const [domain, domainTagList] of Object.entries(domainTags)) {
      if (url.includes(domain)) {
        tags.push(...domainTagList);
        confidence = Math.max(confidence, 0.8);
      }
    }

    // 基于关键词的标签
    const keywordTags: Record<string, string[]> = {
      'api': ['API', '接口', '开发'],
      'tutorial': ['教程', '学习'],
      'documentation': ['文档', '参考'],
      'tool': ['工具', '实用'],
      'framework': ['框架', '开发'],
      'library': ['库', '开发'],
      'design': ['设计', 'UI'],
      'css': ['CSS', '样式', '前端'],
      'javascript': ['JavaScript', 'JS', '前端'],
      'python': ['Python', '编程'],
      'react': ['React', '前端', '框架']
    };

    for (const [keyword, keywordTagList] of Object.entries(keywordTags)) {
      if (title.includes(keyword) || url.includes(keyword)) {
        tags.push(...keywordTagList);
        confidence = Math.max(confidence, 0.7);
      }
    }

    // 去重
    const uniqueTags = [...new Set(tags)];

    return uniqueTags.length > 0 ? {
      tags: uniqueTags,
      confidence
    } : null;
  }

  // 建议新分类
  private async suggestNewCategories(
    bookmarks: Bookmark[], 
    categories: Category[], 
    threshold: number
  ) {
    const suggestions: any[] = [];
    
    // 分析未分类或分类不当的书签
    const uncategorizedBookmarks = bookmarks.filter(b => {
      const category = categories.find(c => c.id === b.category);
      return !category || category.name === '未分类' || category.name === '其他';
    });

    // 基于域名聚类
    const domainGroups = this.groupBookmarksByDomain(uncategorizedBookmarks);
    
    for (const [domain, domainBookmarks] of Object.entries(domainGroups)) {
      if (domainBookmarks.length >= 3) { // 至少3个书签才建议新分类
        const categoryName = this.suggestCategoryNameForDomain(domain);
        if (categoryName) {
          suggestions.push({
            name: categoryName,
            description: `基于 ${domain} 域名的自动分类`,
            bookmarkIds: domainBookmarks.map(b => b.id),
            confidence: 0.8
          });
        }
      }
    }

    return suggestions;
  }

  // 按域名分组书签
  private groupBookmarksByDomain(bookmarks: Bookmark[]): Record<string, Bookmark[]> {
    const groups: Record<string, Bookmark[]> = {};
    
    for (const bookmark of bookmarks) {
      try {
        const domain = new URL(bookmark.url).hostname;
        if (!groups[domain]) {
          groups[domain] = [];
        }
        groups[domain].push(bookmark);
      } catch {
        // 忽略无效 URL
      }
    }
    
    return groups;
  }

  // 为域名建议分类名称
  private suggestCategoryNameForDomain(domain: string): string | null {
    const domainCategories: Record<string, string> = {
      'github.com': '开源项目',
      'stackoverflow.com': '技术问答',
      'youtube.com': '视频资源',
      'medium.com': '技术博客',
      'dev.to': '开发社区',
      'twitter.com': '社交媒体',
      'linkedin.com': '职业网络'
    };

    return domainCategories[domain] || null;
  }

  // 应用 AI 建议
  async applyAISuggestions(
    analysisResult: AIAnalysisResult,
    selectedSuggestions: {
      categoryRecommendations?: string[];
      duplicateRemovals?: string[];
      descriptionUpdates?: string[];
      tagUpdates?: string[];
      newCategories?: string[];
    }
  ): Promise<{ applied: number; errors: string[] }> {
    let applied = 0;
    const errors: string[] = [];

    try {
      // 应用分类建议
      if (selectedSuggestions.categoryRecommendations) {
        for (const suggestionId of selectedSuggestions.categoryRecommendations) {
          const suggestion = analysisResult.suggestions.categoryRecommendations.find(
            s => `${s.bookmarkId}-${s.suggestedCategory}` === suggestionId
          );
          
          if (suggestion) {
            try {
              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {
                category: suggestion.suggestedCategory
              });
              applied++;
            } catch (error) {
              errors.push(`更新书签分类失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }
        }
      }

      // 应用描述建议
      if (selectedSuggestions.descriptionUpdates) {
        for (const suggestionId of selectedSuggestions.descriptionUpdates) {
          const suggestion = analysisResult.suggestions.descriptionSuggestions.find(
            s => s.bookmarkId === suggestionId
          );
          
          if (suggestion) {
            try {
              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {
                description: suggestion.suggestedDescription
              });
              applied++;
            } catch (error) {
              errors.push(`更新书签描述失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }
        }
      }

      // 应用标签建议
      if (selectedSuggestions.tagUpdates) {
        for (const suggestionId of selectedSuggestions.tagUpdates) {
          const suggestion = analysisResult.suggestions.tagSuggestions.find(
            s => s.bookmarkId === suggestionId
          );
          
          if (suggestion) {
            try {
              await this.bookmarkService.updateBookmark(suggestion.bookmarkId, {
                tags: suggestion.suggestedTags
              });
              applied++;
            } catch (error) {
              errors.push(`更新书签标签失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }
        }
      }

      // 创建新分类
      if (selectedSuggestions.newCategories) {
        for (const suggestionId of selectedSuggestions.newCategories) {
          const suggestion = analysisResult.suggestions.newCategorySuggestions.find(
            s => s.name === suggestionId
          );
          
          if (suggestion) {
            try {
              const newCategory = await this.categoryService.createCategory({
                name: suggestion.name,
                description: suggestion.description,
                order: 999
              });
              
              // 将相关书签移动到新分类
              for (const bookmarkId of suggestion.bookmarkIds) {
                await this.bookmarkService.updateBookmark(bookmarkId, {
                  category: newCategory.id
                });
              }
              
              applied++;
            } catch (error) {
              errors.push(`创建新分类失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }
        }
      }

      // 删除重复书签
      if (selectedSuggestions.duplicateRemovals) {
        for (const suggestionId of selectedSuggestions.duplicateRemovals) {
          try {
            await this.bookmarkService.deleteBookmark(suggestionId);
            applied++;
          } catch (error) {
            errors.push(`删除重复书签失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
      }

    } catch (error) {
      errors.push(`应用 AI 建议时出错: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return { applied, errors };
  }

  // 获取 AI 配置
  getAIConfig(): AIConfig {
    return this.aiConfig;
  }

  // 更新 AI 配置
  updateAIConfig(config: Partial<AIConfig>): void {
    this.aiConfig = { ...this.aiConfig, ...config };
  }
}
