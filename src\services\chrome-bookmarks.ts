// CloudNav 2.0 - Chrome 书签导入导出服务
import type { Bookmark, Category, ChromeBookmark, ChromeBookmarkRoot, ImportResult } from '@/types';
import { BookmarkService } from './bookmarks';
import { CategoryService } from './categories';

export class ChromeBookmarkService {
  private bookmarkService: BookmarkService;
  private categoryService: CategoryService;

  constructor(bookmarkService: BookmarkService, categoryService: CategoryService) {
    this.bookmarkService = bookmarkService;
    this.categoryService = categoryService;
  }

  // 解析 Chrome 书签 JSON 文件
  parseChromeBoomarks(jsonContent: string): ChromeBookmarkRoot | null {
    try {
      const data = JSON.parse(jsonContent);

      // 验证 Chrome 书签格式
      if (!data.roots || !data.roots.bookmark_bar || !data.roots.other) {
        throw new Error('Invalid Chrome bookmarks format');
      }

      return data as ChromeBookmarkRoot;
    } catch (error) {
      console.error('Failed to parse Chrome bookmarks:', error);
      return null;
    }
  }

  // 解析 HTML 书签文件（Netscape 格式）
  parseHtmlBookmarks(htmlContent: string): { bookmarks: any[], folders: string[] } | null {
    try {
      const bookmarks: any[] = [];
      const folders: string[] = [];

      // 简单的 HTML 解析，提取书签和文件夹
      const lines = htmlContent.split('\n');
      let currentFolder = '';
      let folderStack: string[] = [];

      for (const line of lines) {
        const trimmedLine = line.trim();

        // 检测文件夹开始
        if (trimmedLine.includes('<DT><H3')) {
          const folderMatch = trimmedLine.match(/>([^<]+)</);
          if (folderMatch) {
            currentFolder = folderMatch[1];
            folderStack.push(currentFolder);
            folders.push(currentFolder);
          }
        }

        // 检测文件夹结束
        else if (trimmedLine.includes('</DL>')) {
          folderStack.pop();
          currentFolder = folderStack[folderStack.length - 1] || '';
        }

        // 检测书签
        else if (trimmedLine.includes('<DT><A HREF=')) {
          const urlMatch = trimmedLine.match(/HREF="([^"]+)"/);
          const titleMatch = trimmedLine.match(/>([^<]+)</);
          const addDateMatch = trimmedLine.match(/ADD_DATE="([^"]+)"/);
          const iconMatch = trimmedLine.match(/ICON="([^"]+)"/);

          if (urlMatch && titleMatch) {
            const url = urlMatch[1];
            const title = titleMatch[1];
            const addDate = addDateMatch ? parseInt(addDateMatch[1]) * 1000 : Date.now();
            const icon = iconMatch ? iconMatch[1] : undefined;

            bookmarks.push({
              title: title,
              url: url,
              description: `从 HTML 书签导入: ${title}`,
              shortDesc: title,
              category: currentFolder || 'imported',
              tags: currentFolder ? [currentFolder] : ['imported'],
              icon: icon,
              dateAdded: addDate,
              dateModified: addDate
            });
          }
        }
      }

      return { bookmarks, folders };
    } catch (error) {
      console.error('Failed to parse HTML bookmarks:', error);
      return null;
    }
  }

  // 从 Chrome 书签节点提取书签和文件夹
  private extractBookmarksFromNode(
    node: ChromeBookmark, 
    parentPath: string = ''
  ): { bookmarks: any[], folders: string[] } {
    const bookmarks: any[] = [];
    const folders: string[] = [];

    if (node.type === 'url' && node.url) {
      // 这是一个书签
      bookmarks.push({
        title: node.name,
        url: node.url,
        description: `从 Chrome 导入的书签`,
        shortDesc: node.name,
        category: parentPath || 'imported',
        tags: parentPath ? [parentPath] : ['imported'],
        dateAdded: node.date_added ? parseInt(node.date_added) : Date.now(),
        dateModified: node.date_modified ? parseInt(node.date_modified) : Date.now()
      });
    } else if (node.type === 'folder' && node.children) {
      // 这是一个文件夹
      const folderName = node.name;
      const folderPath = parentPath ? `${parentPath}/${folderName}` : folderName;
      
      folders.push(folderName);

      // 递归处理子节点
      for (const child of node.children) {
        const childResult = this.extractBookmarksFromNode(child, folderPath);
        bookmarks.push(...childResult.bookmarks);
        folders.push(...childResult.folders);
      }
    }

    return { bookmarks, folders };
  }

  // 通用书签导入（自动检测格式）
  async importBookmarks(
    content: string,
    options: {
      createCategories?: boolean;
      skipDuplicates?: boolean;
      defaultCategory?: string;
      format?: 'auto' | 'json' | 'html';
    } = {}
  ): Promise<ImportResult> {
    const format = options.format || this.detectFileFormat(content);

    if (format === 'json') {
      return this.importChromeBookmarks(content, options);
    } else if (format === 'html') {
      return this.importHtmlBookmarks(content, options);
    } else {
      return {
        success: false,
        imported: 0,
        skipped: 0,
        errors: ['无法识别的文件格式，请确保是 Chrome JSON 或 HTML 书签文件']
      };
    }
  }

  // 检测文件格式
  private detectFileFormat(content: string): 'json' | 'html' | 'unknown' {
    const trimmedContent = content.trim();

    // 检测 JSON 格式
    if (trimmedContent.startsWith('{') && trimmedContent.includes('"roots"')) {
      return 'json';
    }

    // 检测 HTML 格式
    if (trimmedContent.includes('<!DOCTYPE NETSCAPE-Bookmark-file-1>') ||
        trimmedContent.includes('<DT><A HREF=') ||
        trimmedContent.includes('<H3>')) {
      return 'html';
    }

    return 'unknown';
  }

  // 导入 Chrome JSON 书签
  async importChromeBookmarks(
    jsonContent: string,
    options: {
      createCategories?: boolean;
      skipDuplicates?: boolean;
      defaultCategory?: string;
    } = {}
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      imported: 0,
      skipped: 0,
      errors: []
    };

    try {
      // 解析 Chrome 书签文件
      const chromeData = this.parseChromeBoomarks(jsonContent);
      if (!chromeData) {
        result.errors.push('无法解析 Chrome 书签文件');
        return result;
      }

      // 提取所有书签和文件夹
      const allBookmarks: any[] = [];
      const allFolders: string[] = [];

      // 处理书签栏
      if (chromeData.roots.bookmark_bar) {
        const barResult = this.extractBookmarksFromNode(chromeData.roots.bookmark_bar, '书签栏');
        allBookmarks.push(...barResult.bookmarks);
        allFolders.push(...barResult.folders);
      }

      // 处理其他书签
      if (chromeData.roots.other) {
        const otherResult = this.extractBookmarksFromNode(chromeData.roots.other, '其他书签');
        allBookmarks.push(...otherResult.bookmarks);
        allFolders.push(...otherResult.folders);
      }

      // 处理同步书签（如果存在）
      if (chromeData.roots.synced) {
        const syncedResult = this.extractBookmarksFromNode(chromeData.roots.synced, '同步书签');
        allBookmarks.push(...syncedResult.bookmarks);
        allFolders.push(...syncedResult.folders);
      }

      // 创建分类（如果启用）
      const categoryMap: Record<string, string> = {};
      if (options.createCategories) {
        const uniqueFolders = [...new Set(allFolders)];
        
        for (const folderName of uniqueFolders) {
          try {
            // 检查分类是否已存在
            const existingCategory = await this.categoryService.checkDuplicateCategoryName(folderName);
            
            if (!existingCategory) {
              const category = await this.categoryService.createCategory({
                name: folderName,
                description: `从 Chrome 导入的分类: ${folderName}`,
                order: 999 // 放在最后
              });
              categoryMap[folderName] = category.id;
            } else {
              categoryMap[folderName] = existingCategory.id;
            }
          } catch (error) {
            result.errors.push(`创建分类失败: ${folderName}`);
          }
        }
      }

      // 获取现有分类
      const existingCategories = await this.categoryService.getAllCategories();
      const defaultCategoryId = options.defaultCategory || 
        existingCategories.find(c => c.name === '导入书签')?.id ||
        existingCategories[0]?.id;

      // 导入书签
      for (const bookmarkData of allBookmarks) {
        try {
          // 确定分类
          let categoryId = defaultCategoryId;
          if (options.createCategories && bookmarkData.category) {
            categoryId = categoryMap[bookmarkData.category] || defaultCategoryId;
          }

          // 验证数据
          const validation = this.bookmarkService.validateBookmarkData({
            ...bookmarkData,
            category: categoryId
          });

          if (!validation.valid) {
            result.errors.push(`书签验证失败 "${bookmarkData.title}": ${validation.errors.join(', ')}`);
            continue;
          }

          // 检查重复
          if (options.skipDuplicates) {
            const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmarkData.url);
            if (duplicate) {
              result.skipped++;
              continue;
            }
          }

          // 创建书签
          await this.bookmarkService.createBookmark({
            title: bookmarkData.title,
            url: bookmarkData.url,
            description: bookmarkData.description,
            shortDesc: bookmarkData.shortDesc,
            category: categoryId,
            tags: bookmarkData.tags
          });

          result.imported++;
        } catch (error) {
          result.errors.push(`导入书签失败 "${bookmarkData.title}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.success = true;
      console.log(`Chrome 书签导入完成: ${result.imported} 个书签, ${result.skipped} 个跳过, ${result.errors.length} 个错误`);

    } catch (error) {
      result.errors.push(`导入过程出错: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('Chrome bookmarks import failed:', error);
    }

    return result;
  }

  // 导入 HTML 书签
  async importHtmlBookmarks(
    htmlContent: string,
    options: {
      createCategories?: boolean;
      skipDuplicates?: boolean;
      defaultCategory?: string;
    } = {}
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      imported: 0,
      skipped: 0,
      errors: []
    };

    try {
      // 解析 HTML 书签文件
      const htmlData = this.parseHtmlBookmarks(htmlContent);
      if (!htmlData) {
        result.errors.push('无法解析 HTML 书签文件');
        return result;
      }

      const { bookmarks: allBookmarks, folders: allFolders } = htmlData;

      // 创建分类（如果启用）
      const categoryMap: Record<string, string> = {};
      if (options.createCategories) {
        const uniqueFolders = [...new Set(allFolders)];

        for (const folderName of uniqueFolders) {
          try {
            // 检查分类是否已存在
            const existingCategory = await this.categoryService.checkDuplicateCategoryName(folderName);

            if (!existingCategory) {
              const category = await this.categoryService.createCategory({
                name: folderName,
                description: `从 HTML 书签导入的分类: ${folderName}`,
                order: 999 // 放在最后
              });
              categoryMap[folderName] = category.id;
            } else {
              categoryMap[folderName] = existingCategory.id;
            }
          } catch (error) {
            result.errors.push(`创建分类失败: ${folderName}`);
          }
        }
      }

      // 获取现有分类
      const existingCategories = await this.categoryService.getAllCategories();
      const defaultCategoryId = options.defaultCategory ||
        existingCategories.find(c => c.name === '导入书签')?.id ||
        existingCategories[0]?.id;

      // 导入书签
      for (const bookmarkData of allBookmarks) {
        try {
          // 确定分类
          let categoryId = defaultCategoryId;
          if (options.createCategories && bookmarkData.category) {
            categoryId = categoryMap[bookmarkData.category] || defaultCategoryId;
          }

          // 验证数据
          const validation = this.bookmarkService.validateBookmarkData({
            ...bookmarkData,
            category: categoryId
          });

          if (!validation.valid) {
            result.errors.push(`书签验证失败 "${bookmarkData.title}": ${validation.errors.join(', ')}`);
            continue;
          }

          // 检查重复
          if (options.skipDuplicates) {
            const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmarkData.url);
            if (duplicate) {
              result.skipped++;
              continue;
            }
          }

          // 创建书签
          await this.bookmarkService.createBookmark({
            title: bookmarkData.title,
            url: bookmarkData.url,
            description: bookmarkData.description,
            shortDesc: bookmarkData.shortDesc,
            category: categoryId,
            icon: bookmarkData.icon,
            tags: bookmarkData.tags
          });

          result.imported++;
        } catch (error) {
          result.errors.push(`导入书签失败 "${bookmarkData.title}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.success = true;
      console.log(`HTML 书签导入完成: ${result.imported} 个书签, ${result.skipped} 个跳过, ${result.errors.length} 个错误`);

    } catch (error) {
      result.errors.push(`导入过程出错: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('HTML bookmarks import failed:', error);
    }

    return result;
  }

  // 导出为 Chrome 书签格式
  async exportToChromeFormat(): Promise<string> {
    try {
      const [bookmarks, categories] = await Promise.all([
        this.bookmarkService.getAllBookmarks(),
        this.categoryService.getAllCategories()
      ]);

      // 按分类分组书签
      const bookmarksByCategory: Record<string, Bookmark[]> = {};
      bookmarks.forEach(bookmark => {
        if (!bookmarksByCategory[bookmark.category]) {
          bookmarksByCategory[bookmark.category] = [];
        }
        bookmarksByCategory[bookmark.category].push(bookmark);
      });

      // 创建 Chrome 书签结构
      const chromeBookmarks: ChromeBookmarkRoot = {
        checksum: this.generateChecksum(),
        roots: {
          bookmark_bar: {
            date_added: Date.now().toString(),
            date_modified: Date.now().toString(),
            guid: this.generateGuid(),
            id: '1',
            name: 'CloudNav 书签',
            type: 'folder',
            children: []
          },
          other: {
            date_added: Date.now().toString(),
            date_modified: Date.now().toString(),
            guid: this.generateGuid(),
            id: '2',
            name: '其他书签',
            type: 'folder',
            children: []
          },
          synced: {
            date_added: Date.now().toString(),
            date_modified: Date.now().toString(),
            guid: this.generateGuid(),
            id: '3',
            name: '移动设备书签',
            type: 'folder',
            children: []
          }
        },
        version: 1
      };

      let idCounter = 4;

      // 为每个分类创建文件夹
      categories.forEach(category => {
        const categoryBookmarks = bookmarksByCategory[category.id] || [];
        
        if (categoryBookmarks.length > 0) {
          const folderNode: ChromeBookmark = {
            date_added: category.createdAt.toString(),
            date_modified: Date.now().toString(),
            guid: this.generateGuid(),
            id: (idCounter++).toString(),
            name: category.name,
            type: 'folder',
            children: categoryBookmarks.map(bookmark => ({
              date_added: bookmark.createdAt.toString(),
              date_modified: bookmark.updatedAt.toString(),
              guid: this.generateGuid(),
              id: (idCounter++).toString(),
              name: bookmark.title,
              type: 'url',
              url: bookmark.url
            }))
          };

          chromeBookmarks.roots.bookmark_bar.children!.push(folderNode);
        }
      });

      return JSON.stringify(chromeBookmarks, null, 2);
    } catch (error) {
      console.error('Export to Chrome format failed:', error);
      throw new Error('导出 Chrome 书签格式失败');
    }
  }

  // 导出为 Netscape 书签格式（通用格式）
  async exportToNetscapeFormat(): Promise<string> {
    try {
      const [bookmarks, categories] = await Promise.all([
        this.bookmarkService.getAllBookmarks(),
        this.categoryService.getAllCategories()
      ]);

      let html = `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<!-- This is an automatically generated file.
     It will be read and overwritten.
     DO NOT EDIT! -->
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>CloudNav 书签</TITLE>
<H1>CloudNav 书签</H1>
<DL><p>
`;

      // 按分类分组书签
      const bookmarksByCategory: Record<string, Bookmark[]> = {};
      bookmarks.forEach(bookmark => {
        if (!bookmarksByCategory[bookmark.category]) {
          bookmarksByCategory[bookmark.category] = [];
        }
        bookmarksByCategory[bookmark.category].push(bookmark);
      });

      // 为每个分类生成 HTML
      categories.forEach(category => {
        const categoryBookmarks = bookmarksByCategory[category.id] || [];
        
        if (categoryBookmarks.length > 0) {
          html += `    <DT><H3 ADD_DATE="${Math.floor(category.createdAt / 1000)}" LAST_MODIFIED="${Math.floor(Date.now() / 1000)}">${this.escapeHtml(category.name)}</H3>\n`;
          html += `    <DL><p>\n`;
          
          categoryBookmarks.forEach(bookmark => {
            const addDate = Math.floor(bookmark.createdAt / 1000);
            const lastModified = Math.floor(bookmark.updatedAt / 1000);
            html += `        <DT><A HREF="${this.escapeHtml(bookmark.url)}" ADD_DATE="${addDate}" LAST_MODIFIED="${lastModified}"`;
            if (bookmark.icon) {
              html += ` ICON="${this.escapeHtml(bookmark.icon)}"`;
            }
            html += `>${this.escapeHtml(bookmark.title)}</A>\n`;
            if (bookmark.description) {
              html += `        <DD>${this.escapeHtml(bookmark.description)}\n`;
            }
          });
          
          html += `    </DL><p>\n`;
        }
      });

      html += `</DL><p>\n`;
      
      return html;
    } catch (error) {
      console.error('Export to Netscape format failed:', error);
      throw new Error('导出 Netscape 书签格式失败');
    }
  }

  // 生成校验和
  private generateChecksum(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  // 生成 GUID
  private generateGuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // HTML 转义（Workers 环境兼容）
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  // 获取导入统计信息
  async getImportStats(): Promise<{
    totalImported: number;
    lastImportDate?: number;
    importSources: string[];
  }> {
    try {
      const bookmarks = await this.bookmarkService.getAllBookmarks();
      const importedBookmarks = bookmarks.filter(b => 
        b.tags?.includes('imported') || 
        b.description?.includes('从 Chrome 导入')
      );

      return {
        totalImported: importedBookmarks.length,
        lastImportDate: importedBookmarks.length > 0 
          ? Math.max(...importedBookmarks.map(b => b.createdAt))
          : undefined,
        importSources: ['Chrome', 'Netscape', 'Manual']
      };
    } catch (error) {
      console.error('Get import stats failed:', error);
      return {
        totalImported: 0,
        importSources: []
      };
    }
  }
}
