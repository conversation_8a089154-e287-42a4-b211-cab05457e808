// CloudNav 2.0 - 新主页组件（基于 xwnav 布局设计）
import type { Bookmark, Category, Config } from '@/types';

interface HomePageProps {
  bookmarks: Bookmark[];
  categories: Category[];
  config: Config;
}

export const NewHomePage = ({ bookmarks, categories, config }: HomePageProps): string => {
  
  // 生成 Header 组件
  const headerComponent = `
    <header class="fixed top-0 left-0 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg shadow-md z-50 transition-all duration-300" id="main-header">
      <div class="w-full px-4 h-16 flex items-center justify-between">
        <!-- Logo 和网站名称 -->
        <a href="/" class="flex items-center space-x-2">
          <div class="w-9 h-9 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">C</span>
          </div>
          <div class="flex flex-col items-start">
            <span class="text-xl font-extrabold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
              ${config.siteName}
            </span>
          </div>
        </a>
        
        <!-- 搜索框 -->
        <div class="flex-1 max-w-md mx-8 relative" id="search-container">
          <input 
            type="text" 
            id="search-input"
            placeholder="搜索书签..." 
            class="w-full px-4 py-2 pl-10 pr-4 bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
          >
          <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <!-- 搜索结果 -->
          <div id="search-results" class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-96 overflow-y-auto hidden z-50"></div>
        </div>
        
        <!-- 右侧操作按钮 -->
        <div class="flex items-center space-x-4">
          <!-- 主题切换 -->
          <button id="theme-toggle" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            </svg>
          </button>
          
          <!-- 管理面板链接 -->
          <a href="/admin" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </a>
          
          <!-- 侧边栏切换 -->
          <button id="sidebar-toggle" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>
  `;

  // 生成侧边栏组件
  const sidebarComponent = `
    <aside id="sidebar" class="fixed lg:sticky top-16 lg:top-0 left-0 w-60 h-screen lg:h-screen bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 z-40 lg:z-auto overflow-y-auto">
      <div class="p-4">
        <h2 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">分类导航</h2>
        <nav class="space-y-2">
          <a href="#all" class="flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <span class="mr-3">📚</span>
            <span>全部书签</span>
            <span class="ml-auto text-sm text-gray-500">${bookmarks.length}</span>
          </a>
          ${categories.map(category => `
            <a href="#${category.id}" class="flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
              <span class="mr-3">${category.icon || '📁'}</span>
              <span>${category.name}</span>
              <span class="ml-auto text-sm text-gray-500">${bookmarks.filter(b => b.category === category.id).length}</span>
            </a>
          `).join('')}
        </nav>
      </div>
    </aside>
  `;

  return `
    <!DOCTYPE html>
    <html lang="zh-CN" class="scroll-smooth">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${config.siteName}</title>
      <meta name="description" content="${config.siteDescription}">
      <script src="https://cdn.tailwindcss.com"></script>
      <script>
        tailwind.config = {
          darkMode: 'class',
          theme: {
            extend: {
              colors: {
                primary: '#7C3AED',
                secondary: '#3B82F6'
              }
            }
          }
        }
      </script>
      <style>
        /* 科幻风格基础样式 */
        :root {
          --cyber-blue: #00f5ff;
          --cyber-purple: #8b5cf6;
          --cyber-pink: #ec4899;
          --cyber-green: #10b981;
          --cyber-orange: #f59e0b;
          --neon-glow: 0 0 20px currentColor;
          --cyber-gradient: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple), var(--cyber-pink));
        }

        /* 动态背景 */
        body {
          background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
          position: relative;
          overflow-x: hidden;
        }

        body::before {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background:
            radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
          pointer-events: none;
          z-index: -1;
        }

        /* 网格背景 */
        .cyber-grid {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-image:
            linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px);
          background-size: 50px 50px;
          pointer-events: none;
          z-index: -1;
          animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(50px, 50px); }
        }

        /* 霓虹灯效果 */
        .neon-text {
          text-shadow:
            0 0 5px currentColor,
            0 0 10px currentColor,
            0 0 15px currentColor,
            0 0 20px currentColor;
        }

        .neon-border {
          border: 1px solid var(--cyber-blue);
          box-shadow:
            0 0 10px rgba(0, 245, 255, 0.3),
            inset 0 0 10px rgba(0, 245, 255, 0.1);
        }

        /* 科幻卡片样式 */
        .cyber-card {
          background: rgba(15, 15, 35, 0.8);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(0, 245, 255, 0.2);
          transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
          position: relative;
          overflow: hidden;
        }

        .cyber-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
          transition: left 0.5s;
        }

        .cyber-card:hover::before {
          left: 100%;
        }

        .cyber-card:hover {
          transform: translateY(-8px) scale(1.02);
          border-color: var(--cyber-blue);
          box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(0, 245, 255, 0.3),
            inset 0 0 20px rgba(0, 245, 255, 0.05);
        }

        /* 脉冲动画 */
        .pulse-glow {
          animation: pulse-glow 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
          from {
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
          }
          to {
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.6);
          }
        }

        /* 科幻按钮 */
        .cyber-btn {
          background: linear-gradient(45deg, rgba(0, 245, 255, 0.1), rgba(139, 92, 246, 0.1));
          border: 1px solid var(--cyber-blue);
          color: var(--cyber-blue);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .cyber-btn::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.2), transparent);
          transition: left 0.3s;
        }

        .cyber-btn:hover::before {
          left: 100%;
        }

        .cyber-btn:hover {
          background: rgba(0, 245, 255, 0.1);
          box-shadow: 0 0 20px rgba(0, 245, 255, 0.4);
          transform: translateY(-2px);
        }

        .cyber-btn.active {
          background: var(--cyber-blue);
          color: #0f0f23;
          box-shadow: 0 0 25px rgba(0, 245, 255, 0.6);
        }

        /* 搜索框科幻效果 */
        .cyber-search {
          background: rgba(15, 15, 35, 0.8);
          border: 1px solid rgba(0, 245, 255, 0.3);
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;
        }

        .cyber-search:focus {
          border-color: var(--cyber-blue);
          box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
          background: rgba(15, 15, 35, 0.9);
        }

        /* 侧边栏科幻效果 */
        .cyber-sidebar {
          background: rgba(15, 15, 35, 0.95);
          backdrop-filter: blur(15px);
          border-right: 1px solid rgba(0, 245, 255, 0.2);
        }

        .cyber-nav-item {
          transition: all 0.3s ease;
          border-left: 3px solid transparent;
        }

        .cyber-nav-item:hover {
          background: rgba(0, 245, 255, 0.1);
          border-left-color: var(--cyber-blue);
          box-shadow: inset 0 0 20px rgba(0, 245, 255, 0.1);
        }

        /* 头部科幻效果 */
        .cyber-header {
          background: rgba(15, 15, 35, 0.9);
          backdrop-filter: blur(20px);
          border-bottom: 1px solid rgba(0, 245, 255, 0.2);
        }

        /* Logo 科幻效果 */
        .cyber-logo {
          background: var(--cyber-gradient);
          animation: logo-pulse 3s ease-in-out infinite;
        }

        @keyframes logo-pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        /* 分类标题科幻效果 */
        .cyber-title {
          background: var(--cyber-gradient);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          position: relative;
        }

        .cyber-title::after {
          content: '';
          position: absolute;
          bottom: -5px;
          left: 0;
          width: 100%;
          height: 2px;
          background: var(--cyber-gradient);
          border-radius: 1px;
        }

        /* 图标发光效果 */
        .cyber-icon {
          filter: drop-shadow(0 0 8px rgba(0, 245, 255, 0.5));
          transition: all 0.3s ease;
        }

        .cyber-icon:hover {
          filter: drop-shadow(0 0 15px rgba(0, 245, 255, 0.8));
          transform: scale(1.1);
        }

        /* 工具提示科幻效果 */
        .cyber-tooltip {
          background: rgba(15, 15, 35, 0.95);
          border: 1px solid rgba(0, 245, 255, 0.3);
          backdrop-filter: blur(10px);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        /* 页脚科幻效果 */
        .cyber-footer {
          background: rgba(15, 15, 35, 0.9);
          border-top: 1px solid rgba(0, 245, 255, 0.2);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
          .cyber-grid {
            background-size: 30px 30px;
          }
        }

        /* 滚动条科幻样式 */
        ::-webkit-scrollbar {
          width: 8px;
        }

        ::-webkit-scrollbar-track {
          background: rgba(15, 15, 35, 0.5);
        }

        ::-webkit-scrollbar-thumb {
          background: linear-gradient(180deg, var(--cyber-blue), var(--cyber-purple));
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(180deg, var(--cyber-purple), var(--cyber-pink));
        }

        /* 文本选择科幻效果 */
        ::selection {
          background: rgba(0, 245, 255, 0.3);
          color: white;
        }

        /* 修复布局问题 */
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* 确保内容可见 */
        main {
          min-height: calc(100vh - 200px);
        }
      </style>
    </head>
    <body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      ${headerComponent}

      <div class="w-full lg:flex lg:flex-nowrap">
        ${sidebarComponent}

        <main class="pt-24 pb-8 lg:flex-1 lg:min-w-0 px-4">
          <h1 class="sr-only">${config.siteName}</h1>
          
          <!-- 水平导航栏 -->
          <div class="mb-6 overflow-x-auto">
            <div class="flex space-x-2 pb-2">
              <button class="category-filter-btn px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors whitespace-nowrap" data-category="all">
                全部
              </button>
              ${categories.map(category => `
                <button class="category-filter-btn px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors whitespace-nowrap" data-category="${category.id}">
                  ${category.name}
                </button>
              `).join('')}
            </div>
          </div>

          <!-- 分类内容区域 -->
          ${categories.map(category => {
            const categoryBookmarks = bookmarks.filter(bookmark => bookmark.category === category.id);
            if (categoryBookmarks.length === 0) return '';

            return `
              <section class="mb-10 category-section" id="${category.id}" data-category="${category.id}">
                <h2 class="category-title text-2xl font-bold mb-4 pb-2 border-b border-gray-200 dark:border-gray-700 flex items-center">
                  <div class="w-8 h-8 mr-3 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                    <span class="text-lg">${category.icon || '📁'}</span>
                  </div>
                  ${category.name}
                </h2>
                <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-2 sm:gap-6">
                  ${categoryBookmarks.map(bookmark => `
                    <div class="card-wrapper group relative z-10 m-0.5">
                      <div class="card card-hover flex flex-col h-full bg-white dark:bg-slate-900 relative rounded-xl overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700"
                           data-url="${bookmark.url}"
                           data-category="${bookmark.category}"
                           data-title="${bookmark.title}">
                        <div class="card-content p-3 flex flex-col justify-center">
                          <div class="card-header flex items-center mb-2">
                            <div class="icon-container w-10 h-10 mr-2 flex items-center justify-center rounded-full shadow-sm overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
                              ${bookmark.icon ?
                                `<img src="${bookmark.icon}" alt="${bookmark.title}" class="w-8 h-8 object-contain transition-transform duration-300 group-hover:scale-110" loading="lazy" onerror="this.src='/icons/default.svg';">` :
                                `<span class="text-xl">🔗</span>`
                              }
                            </div>
                            <h3 class="title text-sm font-bold text-gray-900 dark:text-gray-100 m-0 flex-1 line-clamp-2">${bookmark.title}</h3>
                          </div>
                          <p class="short-desc text-xs font-medium text-gray-600 dark:text-gray-300 m-0 leading-tight line-clamp-2">${bookmark.shortDesc || bookmark.description || ''}</p>
                        </div>
                      </div>

                      <!-- Tooltip -->
                      <div class="tooltip absolute bottom-full left-1/2 -translate-x-1/2 translate-y-2 w-64 mb-2 z-50 opacity-0 invisible transition-all duration-300 ease-in-out pointer-events-none group-hover:opacity-100 group-hover:visible group-hover:translate-y-0">
                        <div class="tooltip-content relative w-full bg-black/85 backdrop-blur-sm text-white p-3 text-sm rounded-xl shadow-xl">
                          <p class="description line-clamp-3">${bookmark.description || bookmark.shortDesc || ''}</p>
                          <div class="tooltip-arrow absolute bottom-[-8px] left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-l-transparent border-r-8 border-r-transparent border-t-8 border-t-black/85"></div>
                        </div>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </section>
            `;
          }).join('')}
        </main>
      </div>

      <!-- Footer -->
      <footer class="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 py-8">
        <div class="w-full px-4 text-center">
          <p class="text-gray-600 dark:text-gray-400">
            © 2024 ${config.siteName} - 智能书签管理系统
          </p>
        </div>
      </footer>

      <!-- JavaScript -->
      <script>
        // 主题切换功能
        function initTheme() {
          const themeToggle = document.getElementById('theme-toggle');
          const html = document.documentElement;

          // 检查本地存储的主题设置
          const savedTheme = localStorage.getItem('theme');
          if (savedTheme) {
            html.classList.toggle('dark', savedTheme === 'dark');
          } else {
            // 默认跟随系统主题
            html.classList.toggle('dark', window.matchMedia('(prefers-color-scheme: dark)').matches);
          }

          themeToggle?.addEventListener('click', () => {
            html.classList.toggle('dark');
            localStorage.setItem('theme', html.classList.contains('dark') ? 'dark' : 'light');
          });
        }

        // 侧边栏切换功能
        function initSidebar() {
          const sidebarToggle = document.getElementById('sidebar-toggle');
          const sidebar = document.getElementById('sidebar');

          sidebarToggle?.addEventListener('click', () => {
            sidebar?.classList.toggle('-translate-x-full');
          });

          // 点击外部关闭侧边栏
          document.addEventListener('click', (e) => {
            if (!sidebar?.contains(e.target) && !sidebarToggle?.contains(e.target)) {
              sidebar?.classList.add('-translate-x-full');
            }
          });
        }

        // 搜索功能
        function initSearch() {
          const searchInput = document.getElementById('search-input');
          const searchResults = document.getElementById('search-results');
          const allCards = document.querySelectorAll('.card[data-url]');

          searchInput?.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase().trim();

            if (!query) {
              searchResults?.classList.add('hidden');
              return;
            }

            const results = [];
            allCards.forEach(card => {
              const title = card.getAttribute('data-title')?.toLowerCase() || '';
              const category = card.getAttribute('data-category') || '';
              const url = card.getAttribute('data-url') || '';

              if (title.includes(query)) {
                results.push({ title: card.getAttribute('data-title'), category, url });
              }
            });

            if (results.length > 0) {
              searchResults.innerHTML = results.map(result =>
                \`<div class="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer" onclick="window.open('\${result.url}', '_blank')">
                  <h4 class="font-medium text-gray-900 dark:text-gray-100">\${result.title}</h4>
                  <span class="text-xs text-gray-500">分类: \${result.category}</span>
                </div>\`
              ).join('');
              searchResults?.classList.remove('hidden');
            } else {
              searchResults.innerHTML = '<div class="p-3 text-center text-gray-500">没有找到相关书签</div>';
              searchResults?.classList.remove('hidden');
            }
          });

          // 点击外部关闭搜索结果
          document.addEventListener('click', (e) => {
            if (!searchInput?.contains(e.target) && !searchResults?.contains(e.target)) {
              searchResults?.classList.add('hidden');
            }
          });
        }

        // 分类筛选功能
        function initCategoryFilter() {
          const filterBtns = document.querySelectorAll('.category-filter-btn');
          const sections = document.querySelectorAll('.category-section');

          filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
              const category = btn.getAttribute('data-category');

              // 更新按钮状态
              filterBtns.forEach(b => {
                b.classList.remove('bg-primary', 'text-white');
                b.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
              });
              btn.classList.add('bg-primary', 'text-white');
              btn.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');

              // 显示/隐藏分类
              sections.forEach(section => {
                if (category === 'all' || section.getAttribute('data-category') === category) {
                  section.style.display = 'block';
                } else {
                  section.style.display = 'none';
                }
              });
            });
          });
        }

        // 卡片点击功能
        function initCardClick() {
          const cards = document.querySelectorAll('.card[data-url]');
          cards.forEach(card => {
            card.addEventListener('click', () => {
              const url = card.getAttribute('data-url');
              if (url) {
                // 记录点击统计
                fetch('/api/bookmarks/click', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ url })
                }).catch(() => {}); // 忽略错误

                window.open(url, '_blank', 'noopener,noreferrer');
              }
            });
          });
        }

        // 初始化所有功能
        document.addEventListener('DOMContentLoaded', () => {
          initTheme();
          initSidebar();
          initSearch();
          initCategoryFilter();
          initCardClick();
        });
      </script>
    </body>
    </html>
  `;
};
