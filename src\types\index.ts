// CloudNav 2.0 - 类型定义

export interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  shortDesc?: string;
  category: string;
  icon?: string;
  tags?: string[];
  createdAt: number;
  updatedAt: number;
  clickCount: number;
}

export interface Category {
  id: string;
  name: string;
  icon?: string;
  description?: string;
  order: number;
  createdAt: number;
}

export interface Stats {
  totalClicks: number;
  totalViews: number;
  bookmarkStats: Record<string, number>;
  categoryStats: Record<string, number>;
  searchStats: Record<string, number>;
  dailyStats: Record<string, number>;
  deviceStats: {
    mobile: number;
    desktop: number;
    tablet: number;
  };
  lastUpdated: number;
}

export interface AIConfig {
  apiUrl?: string;
  apiKey?: string;
  model?: string;
  enabled: boolean;
}

export interface Config {
  siteName: string;
  siteDescription: string;
  adminPassword?: string;
  aiConfig: AIConfig;
  features: {
    stats: boolean;
    ai: boolean;
    import: boolean;
    export: boolean;
  };
  theme: {
    primaryColor: string;
    darkMode: boolean;
  };
}

export interface KVData<T> {
  data: T;
  lastUpdated: number;
  version: string;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: string[];
}

export interface BatchOperationResult {
  success: string[];
  failed: string[];
  total: number;
}

export interface AIOrganizeResult {
  suggestions: {
    categories: Category[];
    bookmarks: Bookmark[];
    duplicates: string[];
  };
  confidence: number;
}

// Chrome 书签格式
export interface ChromeBookmark {
  date_added: string;
  date_modified: string;
  guid: string;
  id: string;
  name: string;
  type: 'url' | 'folder';
  url?: string;
  children?: ChromeBookmark[];
}

export interface ChromeBookmarkRoot {
  checksum: string;
  roots: {
    bookmark_bar: ChromeBookmark;
    other: ChromeBookmark;
    synced: ChromeBookmark;
  };
  version: number;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 环境变量类型
export interface Env {
  CLOUDNAV_KV: KVNamespace;
  ENVIRONMENT?: string;
}

// 请求上下文类型
export interface RequestContext {
  env: Env;
  executionCtx: ExecutionContext;
}
