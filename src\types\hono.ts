// CloudNav 2.0 - Hono 类型定义
import type { KVService } from '@/services/kv';
import type { BookmarkService } from '@/services/bookmarks';
import type { CategoryService } from '@/services/categories';
import type { MigrationService } from '@/services/migration';
import type { ChromeBookmarkService } from '@/services/chrome-bookmarks';
import type { AIService } from '@/services/ai';
import type { StatisticsService } from '@/services/statistics';
import type { Env } from './index';

// Hono 应用变量类型
export interface Variables {
  kvService: KVService;
  bookmarkService: BookmarkService;
  categoryService: CategoryService;
  migrationService: MigrationService;
  chromeBookmarkService: ChromeBookmarkService;
  aiService: AIService;
  statisticsService: StatisticsService;
}

// Hono 应用类型
export type HonoApp = {
  Bindings: Env;
  Variables: Variables;
};
