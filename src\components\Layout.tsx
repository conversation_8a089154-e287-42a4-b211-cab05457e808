// CloudNav 2.0 - 布局组件

interface LayoutProps {
  title: string;
  description: string;
  children: string;
}

export const Layout = ({ title, description, children }: LayoutProps): string => {
  return `
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>${title}</title>
        <meta name="description" content="${description}" />

        <!-- SEO Meta Tags -->
        <meta property="og:title" content="${title}" />
        <meta property="og:description" content="${description}" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:title" content="${title}" />
        <meta name="twitter:description" content="${description}" />

        <!-- Favicon -->
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />

        <!-- 基础CSS框架 -->
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        
        {/* 基础样式 */}
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
          }
          
          .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
          }
          
          .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 10px 0;
            transition: all 0.3s ease;
          }
          
          .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
          }
          
          .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #7C3AED;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          
          .btn:hover {
            background: #6D28D9;
            transform: translateY(-1px);
          }
          
          .grid {
            display: grid;
            gap: 20px;
          }
          
          .grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
          .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
          .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
          .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
          
          @media (max-width: 768px) {
            .grid-cols-2, .grid-cols-3, .grid-cols-4 {
              grid-template-columns: repeat(2, 1fr);
            }
          }
          
          @media (max-width: 480px) {
            .grid-cols-2, .grid-cols-3, .grid-cols-4 {
              grid-template-columns: 1fr;
            }
          }
          
          .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px 0;
            margin-bottom: 30px;
          }
          
          .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          
          .logo {
            font-size: 24px;
            font-weight: bold;
            color: #7C3AED;
            text-decoration: none;
          }
          
          .search-box {
            flex: 1;
            max-width: 400px;
            margin: 0 20px;
          }
          
          .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
          }
          
          .search-input:focus {
            outline: none;
            border-color: #7C3AED;
          }
          
          .category-nav {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          
          .category-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
          }
          
          .category-item {
            padding: 8px 16px;
            background: #f1f5f9;
            border-radius: 20px;
            text-decoration: none;
            color: #64748b;
            transition: all 0.3s ease;
          }
          
          .category-item:hover,
          .category-item.active {
            background: #7C3AED;
            color: white;
          }
          
          .bookmark-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
          }
          
          .bookmark-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
          }
          
          .bookmark-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
          }
          
          .bookmark-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            margin-bottom: 12px;
            object-fit: cover;
          }
          
          .bookmark-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1e293b;
          }
          
          .bookmark-desc {
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
          }
          
          .error {
            background: #fee2e2;
            color: #dc2626;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
          }
          
          .loading {
            text-align: center;
            padding: 40px;
            color: #64748b;
          }
          
          /* 暗色模式 */
          @media (prefers-color-scheme: dark) {
            body {
              background: #0f172a;
              color: #e2e8f0;
            }
            
            .card, .header, .category-nav, .bookmark-card {
              background: #1e293b;
              color: #e2e8f0;
            }
            
            .search-input {
              background: #334155;
              border-color: #475569;
              color: #e2e8f0;
            }
            
            .category-item {
              background: #334155;
              color: #94a3b8;
            }
            
            .bookmark-title {
              color: #f1f5f9;
            }
          }
        </style>
      </head>
      <body>
        ${children}

        <!-- 基础 JavaScript -->
        <script>
          // 搜索功能
          function initSearch() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
              searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                  const query = this.value.trim();
                  if (query) {
                    window.location.href = '/search?q=' + encodeURIComponent(query);
                  }
                }
              });
            }
          }
          
          // 点击统计
          function trackClick(bookmarkId) {
            fetch('/api/stats/click', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ bookmarkId })
            }).catch(console.error);
          }
          
          // 初始化
          document.addEventListener('DOMContentLoaded', function() {
            initSearch();
          });
        </script>
      </body>
    </html>
  `;
};
