{"name": "cloudnav", "type": "module", "version": "2.0.0", "packageManager": "pnpm@10.12.1", "description": "CloudNav - 智能书签导航站，支持CF Workers一键部署", "keywords": ["cloudflare-workers", "bookmarks", "navigation", "ai-powered", "hono"], "license": "MIT", "scripts": {"dev": "wrangler dev", "build": "tsc", "deploy": "wrangler deploy", "migrate": "tsx src/scripts/migrate.ts", "test": "vitest", "astro:dev": "astro dev", "astro:build": "astro build"}, "dependencies": {"@astrojs/react": "^4.3.0", "@astrojs/sitemap": "^3.4.0", "astro": "^5.8.1", "astro-seo": "^0.8.4", "bcryptjs": "^2.4.3", "hono": "^4.0.0", "nanoid": "^5.0.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@astrojs/tailwind": "^6.0.2", "@cloudflare/workers-types": "^4.20240117.0", "@types/bcryptjs": "^2.4.6", "@types/jsdom": "^21.1.7", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.4.21", "chalk": "^5.4.1", "cheerio": "^1.0.0", "p-limit": "^6.2.0", "postcss": "^8.5.3", "sharp": "^0.34.2", "svgo": "^3.3.2", "tailwindcss": "^3.4.17", "terser": "^5.39.2", "tsx": "^4.19.4", "typescript": "^5.3.0", "undici": "^7.10.0", "vitest": "^1.2.0", "wrangler": "^4.20.3"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}