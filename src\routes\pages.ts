// CloudNav 2.0 - 页面路由
import { Hono } from 'hono';
import type { HonoApp } from '@/types/hono';
import { KVService } from '@/services/kv';
import { HomePage } from '@/components/HomePage';
import { AdminPanel } from '@/components/AdminPanel';
import { Layout } from '@/components/Layout';

const app = new Hono<HonoApp>();

// 主页（支持搜索和分类查询参数）
app.get('/', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const searchQuery = c.req.query('search') || '';
    const categoryId = c.req.query('category') || '';

    // 获取数据
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);

    // 增加页面访问统计
    const stats = await kvService.getStats();
    await kvService.updateStats({
      ...stats,
      totalViews: stats.totalViews + 1
    });

    // 记录搜索统计
    if (searchQuery) {
      stats.searchStats[searchQuery] = (stats.searchStats[searchQuery] || 0) + 1;
      await kvService.updateStats(stats);
    }

    // 找到当前分类
    const currentCategory = categoryId ? categories.find(cat => cat.id === categoryId) : undefined;

    // 渲染页面
    const content = HomePage({
      bookmarks,
      categories,
      config,
      searchQuery: searchQuery || undefined,
      currentCategory
    });

    let title = config.siteName;
    let description = config.siteDescription;

    if (searchQuery) {
      title = `搜索: ${searchQuery} - ${config.siteName}`;
      description = `搜索结果: ${searchQuery}`;
    } else if (currentCategory) {
      title = `${currentCategory.name} - ${config.siteName}`;
      description = currentCategory.description || `${currentCategory.name} 分类下的书签`;
    }

    const html = Layout({
      title,
      description,
      children: content
    });

    return c.html(html);
  } catch (error) {
    console.error('Homepage error:', error);
    return c.html(Layout({
      title: 'CloudNav - Error',
      description: 'An error occurred',
      children: '<div class="error">服务暂时不可用，请稍后再试。</div>'
    }));
  }
});

// 搜索页面
app.get('/search', async (c) => {
  const query = c.req.query('q') || '';
  const kvService = c.get('kvService') as KVService;
  
  try {
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);

    // 搜索逻辑
    const filteredBookmarks = bookmarks.filter(bookmark => 
      bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.description?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    // 记录搜索统计
    if (query) {
      const stats = await kvService.getStats();
      stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
      await kvService.updateStats(stats);
    }

    const content = HomePage({ 
      bookmarks: filteredBookmarks, 
      categories, 
      config,
      searchQuery: query 
    });
    
    const html = Layout({ 
      title: `搜索: ${query} - ${config.siteName}`,
      description: `搜索结果: ${query}`,
      children: content 
    });

    return c.html(html);
  } catch (error) {
    console.error('Search error:', error);
    return c.html(Layout({ 
      title: 'CloudNav - Search Error',
      description: 'Search error',
      children: '<div class="error">搜索服务暂时不可用。</div>'
    }));
  }
});

// 分类页面
app.get('/category/:id', async (c) => {
  const categoryId = c.req.param('id');
  const kvService = c.get('kvService') as KVService;
  
  try {
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);

    const category = categories.find(cat => cat.id === categoryId);
    if (!category) {
      return c.html(Layout({ 
        title: 'Category Not Found',
        description: 'Category not found',
        children: '<div class="error">分类不存在。</div>'
      }), 404);
    }

    const categoryBookmarks = bookmarks.filter(bookmark => bookmark.category === categoryId);

    // 更新分类统计
    const stats = await kvService.getStats();
    stats.categoryStats[categoryId] = (stats.categoryStats[categoryId] || 0) + 1;
    await kvService.updateStats(stats);

    const content = HomePage({ 
      bookmarks: categoryBookmarks, 
      categories, 
      config,
      currentCategory: category 
    });
    
    const html = Layout({ 
      title: `${category.name} - ${config.siteName}`,
      description: category.description || `${category.name} 分类下的书签`,
      children: content 
    });

    return c.html(html);
  } catch (error) {
    console.error('Category page error:', error);
    return c.html(Layout({ 
      title: 'CloudNav - Category Error',
      description: 'Category error',
      children: '<div class="error">分类页面加载失败。</div>'
    }));
  }
});

// 管理面板
app.get('/admin', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;

    // 获取数据
    const [bookmarks, categories, stats, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getStats(),
      kvService.getConfig()
    ]);

    // 渲染管理面板
    const content = AdminPanel({ bookmarks, categories, stats, config });
    const html = Layout({
      title: `管理面板 - ${config.siteName}`,
      description: '书签管理面板',
      children: content
    });

    return c.html(html);
  } catch (error) {
    console.error('Admin panel error:', error);
    return c.html(Layout({
      title: 'CloudNav - Admin Error',
      description: 'Admin error',
      children: '<div class="error">管理面板加载失败。</div>'
    }));
  }
});

// 关于页面
app.get('/about', async (c) => {
  const kvService = c.get('kvService') as KVService;
  const config = await kvService.getConfig();

  const aboutContent = `
    <div class="about-page">
      <div class="about-container">
        <div class="about-header">
          <h1>关于 ${config.siteName}</h1>
          <p class="about-description">${config.siteDescription}</p>
        </div>

        <div class="about-content">
          <div class="features-section">
            <h2>✨ 功能特性</h2>
            <div class="features-grid">
              <div class="feature-card">
                <div class="feature-icon">🚀</div>
                <h3>边缘计算</h3>
                <p>基于 Cloudflare Workers 的全球边缘计算网络</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>智能统计</h3>
                <p>详细的访问统计和数据分析</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3>AI 整理</h3>
                <p>智能分类推荐和重复检测</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3>响应式设计</h3>
                <p>完美适配各种设备和屏幕尺寸</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3>强大搜索</h3>
                <p>支持标题、描述、标签的全文搜索</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">📥</div>
                <h3>导入导出</h3>
                <p>支持 Chrome 和 HTML 格式的书签导入导出</p>
              </div>
            </div>
          </div>

          <div class="tech-section">
            <h2>🛠️ 技术栈</h2>
            <div class="tech-stack">
              <div class="tech-item">
                <strong>前端：</strong>TypeScript + 现代 CSS + 响应式设计
              </div>
              <div class="tech-item">
                <strong>后端：</strong>Hono.js + Cloudflare Workers
              </div>
              <div class="tech-item">
                <strong>存储：</strong>Cloudflare KV Storage
              </div>
              <div class="tech-item">
                <strong>部署：</strong>Cloudflare Workers 全球边缘网络
              </div>
            </div>
          </div>

          <div class="version-section">
            <h2>📋 版本信息</h2>
            <p><strong>版本：</strong>CloudNav 2.0</p>
            <p><strong>更新时间：</strong>2025年6月</p>
            <p><strong>开发者：</strong>Claude 4.0 Sonnet</p>
          </div>
        </div>
      </div>

      <style>
        .about-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 2rem;
        }

        .about-container {
          max-width: 1000px;
          margin: 0 auto;
          background: white;
          border-radius: 1rem;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          overflow: hidden;
        }

        .about-header {
          background: linear-gradient(135deg, #7C3AED 0%, #A855F7 100%);
          color: white;
          padding: 3rem 2rem;
          text-align: center;
        }

        .about-header h1 {
          font-size: 2.5rem;
          margin-bottom: 1rem;
        }

        .about-description {
          font-size: 1.2rem;
          opacity: 0.9;
        }

        .about-content {
          padding: 3rem 2rem;
        }

        .features-section h2,
        .tech-section h2,
        .version-section h2 {
          font-size: 1.5rem;
          margin-bottom: 1.5rem;
          color: #1F2937;
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1.5rem;
          margin-bottom: 3rem;
        }

        .feature-card {
          background: #F9FAFB;
          padding: 1.5rem;
          border-radius: 0.75rem;
          text-align: center;
          transition: transform 0.2s;
        }

        .feature-card:hover {
          transform: translateY(-2px);
        }

        .feature-icon {
          font-size: 2.5rem;
          margin-bottom: 1rem;
        }

        .feature-card h3 {
          font-size: 1.2rem;
          margin-bottom: 0.5rem;
          color: #374151;
        }

        .feature-card p {
          color: #6B7280;
          line-height: 1.6;
        }

        .tech-stack {
          background: #F3F4F6;
          padding: 1.5rem;
          border-radius: 0.75rem;
          margin-bottom: 3rem;
        }

        .tech-item {
          margin-bottom: 0.75rem;
          color: #374151;
          line-height: 1.6;
        }

        .tech-item:last-child {
          margin-bottom: 0;
        }

        .version-section {
          background: #EEF2FF;
          padding: 1.5rem;
          border-radius: 0.75rem;
          color: #374151;
        }

        .version-section p {
          margin-bottom: 0.5rem;
          line-height: 1.6;
        }

        .version-section p:last-child {
          margin-bottom: 0;
        }

        @media (max-width: 768px) {
          .about-page {
            padding: 1rem;
          }

          .about-header {
            padding: 2rem 1rem;
          }

          .about-header h1 {
            font-size: 2rem;
          }

          .about-content {
            padding: 2rem 1rem;
          }

          .features-grid {
            grid-template-columns: 1fr;
          }
        }
      </style>
    </div>
  `;

  const html = Layout({
    title: `关于 - ${config.siteName}`,
    description: `关于 ${config.siteName}`,
    children: aboutContent
  });

  return c.html(html);
});

export { app as pageRoutes };
