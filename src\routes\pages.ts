// CloudNav 2.0 - 页面路由
import { Hono } from 'hono';
import type { Env } from '@/types';
import { KVService } from '@/services/kv';
import { HomePage } from '@/components/HomePage';
import { Layout } from '@/components/Layout';

const app = new Hono<{ Bindings: Env }>();

// 主页
app.get('/', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    
    // 获取数据
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);

    // 增加页面访问统计
    const stats = await kvService.getStats();
    await kvService.updateStats({
      ...stats,
      totalViews: stats.totalViews + 1
    });

    // 渲染页面
    const content = HomePage({ bookmarks, categories, config });
    const html = Layout({ 
      title: config.siteName,
      description: config.siteDescription,
      children: content 
    });

    return c.html(html);
  } catch (error) {
    console.error('Homepage error:', error);
    return c.html(Layout({ 
      title: 'CloudNav - Error',
      description: 'An error occurred',
      children: '<div class="error">服务暂时不可用，请稍后再试。</div>'
    }));
  }
});

// 搜索页面
app.get('/search', async (c) => {
  const query = c.req.query('q') || '';
  const kvService = c.get('kvService') as KVService;
  
  try {
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);

    // 搜索逻辑
    const filteredBookmarks = bookmarks.filter(bookmark => 
      bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.description?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    // 记录搜索统计
    if (query) {
      const stats = await kvService.getStats();
      stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
      await kvService.updateStats(stats);
    }

    const content = HomePage({ 
      bookmarks: filteredBookmarks, 
      categories, 
      config,
      searchQuery: query 
    });
    
    const html = Layout({ 
      title: `搜索: ${query} - ${config.siteName}`,
      description: `搜索结果: ${query}`,
      children: content 
    });

    return c.html(html);
  } catch (error) {
    console.error('Search error:', error);
    return c.html(Layout({ 
      title: 'CloudNav - Search Error',
      description: 'Search error',
      children: '<div class="error">搜索服务暂时不可用。</div>'
    }));
  }
});

// 分类页面
app.get('/category/:id', async (c) => {
  const categoryId = c.req.param('id');
  const kvService = c.get('kvService') as KVService;
  
  try {
    const [bookmarks, categories, config] = await Promise.all([
      kvService.getBookmarks(),
      kvService.getCategories(),
      kvService.getConfig()
    ]);

    const category = categories.find(cat => cat.id === categoryId);
    if (!category) {
      return c.html(Layout({ 
        title: 'Category Not Found',
        description: 'Category not found',
        children: '<div class="error">分类不存在。</div>'
      }), 404);
    }

    const categoryBookmarks = bookmarks.filter(bookmark => bookmark.category === categoryId);

    // 更新分类统计
    const stats = await kvService.getStats();
    stats.categoryStats[categoryId] = (stats.categoryStats[categoryId] || 0) + 1;
    await kvService.updateStats(stats);

    const content = HomePage({ 
      bookmarks: categoryBookmarks, 
      categories, 
      config,
      currentCategory: category 
    });
    
    const html = Layout({ 
      title: `${category.name} - ${config.siteName}`,
      description: category.description || `${category.name} 分类下的书签`,
      children: content 
    });

    return c.html(html);
  } catch (error) {
    console.error('Category page error:', error);
    return c.html(Layout({ 
      title: 'CloudNav - Category Error',
      description: 'Category error',
      children: '<div class="error">分类页面加载失败。</div>'
    }));
  }
});

// 关于页面
app.get('/about', async (c) => {
  const kvService = c.get('kvService') as KVService;
  const config = await kvService.getConfig();
  
  const aboutContent = `
    <div class="about-page">
      <h1>关于 ${config.siteName}</h1>
      <p>${config.siteDescription}</p>
      <div class="features">
        <h2>功能特性</h2>
        <ul>
          <li>🚀 基于 Cloudflare Workers 的边缘计算</li>
          <li>📊 智能统计分析</li>
          <li>🤖 AI 智能整理</li>
          <li>📱 响应式设计</li>
          <li>🔍 强大的搜索功能</li>
          <li>📥 Chrome 书签导入导出</li>
        </ul>
      </div>
      <div class="tech-stack">
        <h2>技术栈</h2>
        <p>Hono.js + TypeScript + Cloudflare Workers + KV Storage</p>
      </div>
    </div>
  `;

  const html = Layout({ 
    title: `关于 - ${config.siteName}`,
    description: `关于 ${config.siteName}`,
    children: aboutContent 
  });

  return c.html(html);
});

export { app as pageRoutes };
