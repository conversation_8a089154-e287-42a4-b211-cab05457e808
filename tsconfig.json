{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": true, "noEmit": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "jsx": "preserve", "types": ["@cloudflare/workers-types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "icon-system", ".astro"]}