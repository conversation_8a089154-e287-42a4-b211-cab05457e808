// CloudNav 2.0 - API 路由
import { Hono } from 'hono';
import type { Env, ApiResponse } from '@/types';
import { KVService } from '@/services/kv';
import { BookmarkService } from '@/services/bookmarks';
import { CategoryService } from '@/services/categories';
import { MigrationService } from '@/services/migration';
import { ChromeBookmarkService } from '@/services/chrome-bookmarks';
import { AIService } from '@/services/ai';
import { StatisticsService } from '@/services/statistics';

const app = new Hono<{ Bindings: Env }>();

// 中间件：添加服务实例到上下文
app.use('*', async (c, next) => {
  const kvService = c.get('kvService') as KVService;
  const bookmarkService = new BookmarkService(kvService);
  const categoryService = new CategoryService(kvService);

  // 获取 AI 配置
  const config = await kvService.getConfig();

  c.set('bookmarkService', bookmarkService);
  c.set('categoryService', categoryService);
  c.set('migrationService', new MigrationService(kvService));
  c.set('chromeBookmarkService', new ChromeBookmarkService(bookmarkService, categoryService));
  c.set('aiService', new AIService(bookmarkService, categoryService, config.aiConfig));
  c.set('statisticsService', new StatisticsService(kvService));
  await next();
});

// ==================== 书签管理 API ====================

// 获取所有书签
app.get('/bookmarks', async (c) => {
  try {
    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const bookmarks = await bookmarkService.getAllBookmarks();

    return c.json<ApiResponse>({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error('Get bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get bookmarks'
    }, 500);
  }
});

// 根据ID获取书签
app.get('/bookmarks/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const bookmark = await bookmarkService.getBookmarkById(id);

    if (!bookmark) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: bookmark
    });
  } catch (error) {
    console.error('Get bookmark error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get bookmark'
    }, 500);
  }
});

// 创建新书签
app.post('/bookmarks', async (c) => {
  try {
    const bookmarkData = await c.req.json();
    const bookmarkService = c.get('bookmarkService') as BookmarkService;

    // 验证数据
    const validation = bookmarkService.validateBookmarkData(bookmarkData);
    if (!validation.valid) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Validation failed',
        data: { errors: validation.errors }
      }, 400);
    }

    // 检查重复
    const duplicate = await bookmarkService.checkDuplicateBookmark(bookmarkData.url);
    if (duplicate) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark with this URL already exists'
      }, 409);
    }

    const bookmark = await bookmarkService.createBookmark(bookmarkData);

    return c.json<ApiResponse>({
      success: true,
      data: bookmark,
      message: 'Bookmark created successfully'
    }, 201);
  } catch (error) {
    console.error('Create bookmark error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to create bookmark'
    }, 500);
  }
});

// 更新书签
app.put('/bookmarks/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const updates = await c.req.json();
    const bookmarkService = c.get('bookmarkService') as BookmarkService;

    const bookmark = await bookmarkService.updateBookmark(id, updates);

    if (!bookmark) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: bookmark,
      message: 'Bookmark updated successfully'
    });
  } catch (error) {
    console.error('Update bookmark error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to update bookmark'
    }, 500);
  }
});

// 删除书签
app.delete('/bookmarks/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const bookmarkService = c.get('bookmarkService') as BookmarkService;

    const success = await bookmarkService.deleteBookmark(id);

    if (!success) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      message: 'Bookmark deleted successfully'
    });
  } catch (error) {
    console.error('Delete bookmark error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to delete bookmark'
    }, 500);
  }
});

// 批量删除书签
app.post('/bookmarks/batch-delete', async (c) => {
  try {
    const { ids } = await c.req.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Invalid bookmark IDs'
      }, 400);
    }

    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const result = await bookmarkService.deleteBookmarks(ids);

    return c.json<ApiResponse>({
      success: true,
      data: result,
      message: `Deleted ${result.success.length} bookmarks`
    });
  } catch (error) {
    console.error('Batch delete bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to delete bookmarks'
    }, 500);
  }
});

// ==================== 分类管理 API ====================

// 获取所有分类
app.get('/categories', async (c) => {
  try {
    const categoryService = c.get('categoryService') as CategoryService;
    const categories = await categoryService.getAllCategories();

    return c.json<ApiResponse>({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get categories error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get categories'
    }, 500);
  }
});

// 根据ID获取分类
app.get('/categories/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const categoryService = c.get('categoryService') as CategoryService;
    const category = await categoryService.getCategoryById(id);

    if (!category) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Category not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Get category error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get category'
    }, 500);
  }
});

// 创建新分类
app.post('/categories', async (c) => {
  try {
    const categoryData = await c.req.json();
    const categoryService = c.get('categoryService') as CategoryService;

    // 验证数据
    const validation = categoryService.validateCategoryData(categoryData);
    if (!validation.valid) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Validation failed',
        data: { errors: validation.errors }
      }, 400);
    }

    // 检查重复
    const duplicate = await categoryService.checkDuplicateCategoryName(categoryData.name);
    if (duplicate) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Category with this name already exists'
      }, 409);
    }

    const category = await categoryService.createCategory(categoryData);

    return c.json<ApiResponse>({
      success: true,
      data: category,
      message: 'Category created successfully'
    }, 201);
  } catch (error) {
    console.error('Create category error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to create category'
    }, 500);
  }
});

// 更新分类
app.put('/categories/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const updates = await c.req.json();
    const categoryService = c.get('categoryService') as CategoryService;

    const category = await categoryService.updateCategory(id, updates);

    if (!category) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Category not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: category,
      message: 'Category updated successfully'
    });
  } catch (error) {
    console.error('Update category error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to update category'
    }, 500);
  }
});

// 删除分类
app.delete('/categories/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { moveBookmarksTo, deleteBookmarks } = c.req.query();
    const categoryService = c.get('categoryService') as CategoryService;

    const options: any = {};
    if (moveBookmarksTo) options.moveBookmarksTo = moveBookmarksTo;
    if (deleteBookmarks === 'true') options.deleteBookmarks = true;

    const result = await categoryService.deleteCategory(id, options);

    if (!result.success) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Category not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: result,
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Delete category error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete category'
    }, 500);
  }
});

// 获取分类统计
app.get('/categories/stats', async (c) => {
  try {
    const categoryService = c.get('categoryService') as CategoryService;
    const stats = await categoryService.getCategoryStats();

    return c.json<ApiResponse>({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get category stats error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get category stats'
    }, 500);
  }
});

// 获取统计数据
app.get('/stats', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const stats = await kvService.getStats();
    
    return c.json<ApiResponse>({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get stats error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get stats'
    }, 500);
  }
});

// ==================== 统计和点击 API ====================

// 记录点击统计
app.post('/stats/click', async (c) => {
  try {
    const { bookmarkId } = await c.req.json();

    if (!bookmarkId) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark ID is required'
      }, 400);
    }

    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const success = await bookmarkService.incrementClickCount(bookmarkId);

    if (success) {
      return c.json<ApiResponse>({
        success: true,
        message: 'Click recorded'
      });
    } else {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark not found'
      }, 404);
    }
  } catch (error) {
    console.error('Record click error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to record click'
    }, 500);
  }
});

// 获取热门书签
app.get('/bookmarks/popular', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '10');
    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const bookmarks = await bookmarkService.getPopularBookmarks(limit);

    return c.json<ApiResponse>({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error('Get popular bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get popular bookmarks'
    }, 500);
  }
});

// 获取最近添加的书签
app.get('/bookmarks/recent', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '10');
    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const bookmarks = await bookmarkService.getRecentBookmarks(limit);

    return c.json<ApiResponse>({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error('Get recent bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get recent bookmarks'
    }, 500);
  }
});

// ==================== 数据迁移 API ====================

// 获取迁移状态
app.get('/migration/status', async (c) => {
  try {
    const migrationService = c.get('migrationService') as MigrationService;
    const status = await migrationService.getMigrationStatus();

    return c.json<ApiResponse>({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('Get migration status error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get migration status'
    }, 500);
  }
});

// 执行数据迁移
app.post('/migration/migrate', async (c) => {
  try {
    const migrationService = c.get('migrationService') as MigrationService;
    const result = await migrationService.performMigration();

    return c.json<ApiResponse>({
      success: result.success,
      data: result.details,
      message: result.message
    });
  } catch (error) {
    console.error('Migration error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Migration failed'
    }, 500);
  }
});

// 创建示例数据
app.post('/migration/sample-data', async (c) => {
  try {
    const migrationService = c.get('migrationService') as MigrationService;
    const result = await migrationService.createSampleData();

    return c.json<ApiResponse>({
      success: result.success,
      data: {
        categoriesCreated: result.categoriesCreated,
        bookmarksCreated: result.bookmarksCreated,
        errors: result.errors
      },
      message: `Created ${result.categoriesCreated} categories and ${result.bookmarksCreated} bookmarks`
    });
  } catch (error) {
    console.error('Create sample data error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to create sample data'
    }, 500);
  }
});

// 清空所有数据
app.post('/migration/clear', async (c) => {
  try {
    const migrationService = c.get('migrationService') as MigrationService;
    const success = await migrationService.clearAllData();

    return c.json<ApiResponse>({
      success,
      message: success ? 'All data cleared successfully' : 'Failed to clear data'
    });
  } catch (error) {
    console.error('Clear data error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to clear data'
    }, 500);
  }
});

// ==================== Chrome 书签导入导出 API ====================

// 通用书签导入（支持 JSON 和 HTML 格式）
app.post('/import/bookmarks', async (c) => {
  try {
    const body = await c.req.json();
    const { content, options = {} } = body;

    if (!content) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark file content is required'
      }, 400);
    }

    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;
    const result = await chromeBookmarkService.importBookmarks(content, {
      createCategories: options.createCategories !== false,
      skipDuplicates: options.skipDuplicates !== false,
      defaultCategory: options.defaultCategory,
      format: options.format || 'auto'
    });

    return c.json<ApiResponse>({
      success: result.success,
      data: {
        imported: result.imported,
        skipped: result.skipped,
        errors: result.errors
      },
      message: result.success
        ? `成功导入 ${result.imported} 个书签，跳过 ${result.skipped} 个重复项`
        : '导入失败'
    });
  } catch (error) {
    console.error('Bookmark import error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to import bookmarks'
    }, 500);
  }
});

// 导入 Chrome 书签（保持向后兼容）
app.post('/import/chrome', async (c) => {
  try {
    const body = await c.req.json();
    const { jsonContent, options = {} } = body;

    if (!jsonContent) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Chrome bookmarks JSON content is required'
      }, 400);
    }

    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;
    const result = await chromeBookmarkService.importChromeBookmarks(jsonContent, {
      createCategories: options.createCategories !== false,
      skipDuplicates: options.skipDuplicates !== false,
      defaultCategory: options.defaultCategory
    });

    return c.json<ApiResponse>({
      success: result.success,
      data: {
        imported: result.imported,
        skipped: result.skipped,
        errors: result.errors
      },
      message: result.success
        ? `成功导入 ${result.imported} 个书签，跳过 ${result.skipped} 个重复项`
        : '导入失败'
    });
  } catch (error) {
    console.error('Chrome import error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to import Chrome bookmarks'
    }, 500);
  }
});

// 导出为 Chrome 格式
app.get('/export/chrome', async (c) => {
  try {
    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;
    const chromeJson = await chromeBookmarkService.exportToChromeFormat();

    // 设置下载头
    c.header('Content-Type', 'application/json');
    c.header('Content-Disposition', 'attachment; filename="cloudnav-bookmarks.json"');

    return c.text(chromeJson);
  } catch (error) {
    console.error('Chrome export error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to export Chrome bookmarks'
    }, 500);
  }
});

// 导出为 Netscape 格式
app.get('/export/netscape', async (c) => {
  try {
    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;
    const netscapeHtml = await chromeBookmarkService.exportToNetscapeFormat();

    // 设置下载头
    c.header('Content-Type', 'text/html');
    c.header('Content-Disposition', 'attachment; filename="cloudnav-bookmarks.html"');

    return c.text(netscapeHtml);
  } catch (error) {
    console.error('Netscape export error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to export Netscape bookmarks'
    }, 500);
  }
});

// 获取导入统计
app.get('/import/stats', async (c) => {
  try {
    const chromeBookmarkService = c.get('chromeBookmarkService') as ChromeBookmarkService;
    const stats = await chromeBookmarkService.getImportStats();

    return c.json<ApiResponse>({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Import stats error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get import stats'
    }, 500);
  }
});

// ==================== AI 智能整理 API ====================

// AI 分析书签
app.post('/ai/analyze', async (c) => {
  try {
    const body = await c.req.json();
    const options = {
      enableCategoryRecommendations: body.enableCategoryRecommendations !== false,
      enableDuplicateDetection: body.enableDuplicateDetection !== false,
      enableDescriptionGeneration: body.enableDescriptionGeneration !== false,
      enableTagSuggestions: body.enableTagSuggestions !== false,
      enableNewCategoryCreation: body.enableNewCategoryCreation !== false,
      confidenceThreshold: body.confidenceThreshold || 0.7,
      autoApply: body.autoApply || false
    };

    const aiService = c.get('aiService') as AIService;
    const result = await aiService.analyzeBookmarks(options);

    return c.json<ApiResponse>({
      success: true,
      data: result,
      message: `AI 分析完成，发现 ${result.statistics.categorySuggestions} 个分类建议，${result.statistics.duplicatesFound} 个重复项`
    });
  } catch (error) {
    console.error('AI analyze error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to analyze bookmarks with AI'
    }, 500);
  }
});

// 应用 AI 建议
app.post('/ai/apply', async (c) => {
  try {
    const body = await c.req.json();
    const { analysisResult, selectedSuggestions } = body;

    if (!analysisResult || !selectedSuggestions) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Analysis result and selected suggestions are required'
      }, 400);
    }

    const aiService = c.get('aiService') as AIService;
    const result = await aiService.applyAISuggestions(analysisResult, selectedSuggestions);

    return c.json<ApiResponse>({
      success: true,
      data: result,
      message: `成功应用 ${result.applied} 个 AI 建议${result.errors.length > 0 ? `，${result.errors.length} 个错误` : ''}`
    });
  } catch (error) {
    console.error('AI apply error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to apply AI suggestions'
    }, 500);
  }
});

// 获取 AI 配置
app.get('/ai/config', async (c) => {
  try {
    const aiService = c.get('aiService') as AIService;
    const config = aiService.getAIConfig();

    return c.json<ApiResponse>({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Get AI config error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get AI config'
    }, 500);
  }
});

// 更新 AI 配置
app.put('/ai/config', async (c) => {
  try {
    const body = await c.req.json();
    const aiService = c.get('aiService') as AIService;

    aiService.updateAIConfig(body);

    return c.json<ApiResponse>({
      success: true,
      message: 'AI configuration updated successfully'
    });
  } catch (error) {
    console.error('Update AI config error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to update AI config'
    }, 500);
  }
});

// ==================== 统计功能 API ====================

// 获取详细统计数据
app.get('/statistics/detailed', async (c) => {
  try {
    const statisticsService = c.get('statisticsService') as StatisticsService;
    const stats = await statisticsService.getDetailedStats();

    return c.json<ApiResponse>({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get detailed statistics error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get detailed statistics'
    }, 500);
  }
});

// 记录页面访问
app.post('/statistics/page-view', async (c) => {
  try {
    const userAgent = c.req.header('User-Agent');
    const statisticsService = c.get('statisticsService') as StatisticsService;

    await statisticsService.recordPageView(userAgent);

    return c.json<ApiResponse>({
      success: true,
      message: 'Page view recorded'
    });
  } catch (error) {
    console.error('Record page view error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to record page view'
    }, 500);
  }
});

// 记录搜索
app.post('/statistics/search', async (c) => {
  try {
    const body = await c.req.json();
    const { query } = body;

    if (!query) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Search query is required'
      }, 400);
    }

    const statisticsService = c.get('statisticsService') as StatisticsService;
    await statisticsService.recordSearch(query);

    return c.json<ApiResponse>({
      success: true,
      message: 'Search recorded'
    });
  } catch (error) {
    console.error('Record search error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to record search'
    }, 500);
  }
});

// 导出统计数据
app.get('/statistics/export', async (c) => {
  try {
    const statisticsService = c.get('statisticsService') as StatisticsService;
    const stats = await statisticsService.exportStats();

    // 设置下载头
    c.header('Content-Type', 'application/json');
    c.header('Content-Disposition', 'attachment; filename="cloudnav-statistics.json"');

    return c.text(JSON.stringify(stats, null, 2));
  } catch (error) {
    console.error('Export statistics error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to export statistics'
    }, 500);
  }
});

// 重置统计数据
app.post('/statistics/reset', async (c) => {
  try {
    const statisticsService = c.get('statisticsService') as StatisticsService;
    await statisticsService.resetStats();

    return c.json<ApiResponse>({
      success: true,
      message: 'Statistics reset successfully'
    });
  } catch (error) {
    console.error('Reset statistics error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to reset statistics'
    }, 500);
  }
});

// 清理过期统计数据
app.post('/statistics/cleanup', async (c) => {
  try {
    const body = await c.req.json();
    const daysToKeep = body.daysToKeep || 90;

    const statisticsService = c.get('statisticsService') as StatisticsService;
    await statisticsService.cleanupOldStats(daysToKeep);

    return c.json<ApiResponse>({
      success: true,
      message: `Cleaned up statistics older than ${daysToKeep} days`
    });
  } catch (error) {
    console.error('Cleanup statistics error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to cleanup statistics'
    }, 500);
  }
});

// 获取配置
app.get('/config', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const config = await kvService.getConfig();
    
    // 不返回敏感信息
    const publicConfig = {
      siteName: config.siteName,
      siteDescription: config.siteDescription,
      features: config.features,
      theme: config.theme
    };
    
    return c.json<ApiResponse>({
      success: true,
      data: publicConfig
    });
  } catch (error) {
    console.error('Get config error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get config'
    }, 500);
  }
});

// 搜索书签
app.get('/search', async (c) => {
  try {
    const query = c.req.query('q') || '';
    
    if (!query.trim()) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Search query is required'
      }, 400);
    }

    const kvService = c.get('kvService') as KVService;
    const bookmarks = await kvService.getBookmarks();
    
    // 搜索逻辑
    const filteredBookmarks = bookmarks.filter(bookmark => 
      bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.description?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    // 记录搜索统计
    const stats = await kvService.getStats();
    stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
    await kvService.updateStats(stats);
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        query,
        results: filteredBookmarks,
        total: filteredBookmarks.length
      }
    });
  } catch (error) {
    console.error('Search error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Search failed'
    }, 500);
  }
});

// 数据备份
app.get('/backup', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const backup = await kvService.backup();
    
    return c.json<ApiResponse>({
      success: true,
      data: backup
    });
  } catch (error) {
    console.error('Backup error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Backup failed'
    }, 500);
  }
});

// 健康检查
app.get('/health', (c) => {
  return c.json<ApiResponse>({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '2.0.0'
    }
  });
});

// API 测试页面
app.get('/test', (c) => {
  const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CloudNav 2.0 - API 测试</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      </head>
      <body class="bg-gray-50">
        <div class="min-h-screen">
          <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 py-4">
              <h1 class="text-2xl font-bold text-gray-900">🧪 API 测试页面</h1>
              <p class="text-gray-600">测试 CloudNav 2.0 的核心 API 功能</p>
            </div>
          </header>

          <div class="max-w-7xl mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📊 迁移状态</h2>
                <button onclick="testMigrationStatus()" class="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mb-4">
                  获取迁移状态
                </button>
                <pre id="migrationStatus" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">🎯 创建示例数据</h2>
                <button onclick="createSampleData()" class="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mb-4">
                  创建示例数据
                </button>
                <pre id="sampleDataResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📚 书签列表</h2>
                <button onclick="getBookmarks()" class="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 mb-4">
                  获取书签列表
                </button>
                <pre id="bookmarksList" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>

              <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">📁 分类列表</h2>
                <button onclick="getCategories()" class="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 mb-4">
                  获取分类列表
                </button>
                <pre id="categoriesList" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
              </div>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">➕ 创建新书签</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <input type="text" id="bookmarkTitle" placeholder="书签标题" class="px-3 py-2 border border-gray-300 rounded">
                <input type="url" id="bookmarkUrl" placeholder="书签URL" class="px-3 py-2 border border-gray-300 rounded">
                <input type="text" id="bookmarkCategory" placeholder="分类ID (先创建示例数据获取)" class="px-3 py-2 border border-gray-300 rounded">
                <input type="text" id="bookmarkDesc" placeholder="描述" class="px-3 py-2 border border-gray-300 rounded">
              </div>
              <button onclick="createBookmark()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4">
                创建书签
              </button>
              <pre id="createBookmarkResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">📥 书签导入（支持多种格式）</h2>
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  上传书签文件 (支持 .json 和 .html 格式)
                </label>
                <input type="file" id="bookmarkFile" accept=".json,.html,.htm" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                <div class="mt-2 text-sm text-gray-600">
                  <p>支持的格式：</p>
                  <ul class="list-disc list-inside ml-4">
                    <li>Chrome JSON 格式 (bookmarks.json)</li>
                    <li>HTML 书签格式 (bookmarks.html)</li>
                    <li>Netscape 书签格式</li>
                  </ul>
                </div>
              </div>
              <div class="mb-4 space-y-2">
                <label class="flex items-center">
                  <input type="checkbox" id="createCategories" checked class="mr-2">
                  <span class="text-sm">自动创建分类</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="skipDuplicates" checked class="mr-2">
                  <span class="text-sm">跳过重复书签</span>
                </label>
              </div>
              <button onclick="importBookmarks()" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 mb-4">
                导入书签文件
              </button>
              <pre id="importResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">📤 书签导出</h2>
              <div class="space-x-4 mb-4">
                <button onclick="exportChromeBookmarks()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                  导出为 Chrome 格式
                </button>
                <button onclick="exportNetscapeBookmarks()" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                  导出为 HTML 格式
                </button>
                <button onclick="getImportStats()" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                  导入统计
                </button>
              </div>
              <pre id="exportResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">🤖 AI 智能整理</h2>
              <div class="mb-4 space-y-2">
                <label class="flex items-center">
                  <input type="checkbox" id="enableCategoryRecommendations" checked class="mr-2">
                  <span class="text-sm">启用分类推荐</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="enableDuplicateDetection" checked class="mr-2">
                  <span class="text-sm">启用重复检测</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="enableDescriptionGeneration" checked class="mr-2">
                  <span class="text-sm">启用描述生成</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="enableTagSuggestions" checked class="mr-2">
                  <span class="text-sm">启用标签建议</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="enableNewCategoryCreation" class="mr-2">
                  <span class="text-sm">启用新分类创建</span>
                </label>
                <div class="flex items-center space-x-2">
                  <label class="text-sm">置信度阈值:</label>
                  <input type="range" id="confidenceThreshold" min="0.1" max="1.0" step="0.1" value="0.7" class="flex-1">
                  <span id="confidenceValue" class="text-sm font-mono">0.7</span>
                </div>
              </div>
              <div class="space-x-4 mb-4">
                <button onclick="analyzeWithAI()" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                  AI 分析书签
                </button>
                <button onclick="getAIConfig()" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                  获取 AI 配置
                </button>
              </div>
              <pre id="aiResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">📊 统计功能</h2>
              <div class="space-x-4 mb-4">
                <button onclick="getDetailedStats()" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
                  获取详细统计
                </button>
                <button onclick="recordPageView()" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                  记录页面访问
                </button>
                <button onclick="recordSearch()" class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700">
                  记录搜索
                </button>
                <button onclick="exportStats()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                  导出统计
                </button>
                <button onclick="resetStats()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                  重置统计
                </button>
              </div>
              <pre id="statsResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">🧹 清理数据</h2>
              <button onclick="clearAllData()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mb-4">
                清空所有数据
              </button>
              <pre id="clearDataResult" class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40"></pre>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
              <h2 class="text-xl font-semibold mb-4">🔗 快速链接</h2>
              <div class="space-x-4">
                <a href="/" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">返回主页</a>
                <a href="/admin" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">管理面板</a>
                <a href="/about" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">关于页面</a>
              </div>
            </div>
          </div>
        </div>

        <script>
          // 更新置信度显示
          document.getElementById('confidenceThreshold').addEventListener('input', function() {
            document.getElementById('confidenceValue').textContent = this.value;
          });

          async function testMigrationStatus() {
            try {
              const response = await fetch('/api/migration/status');
              const data = await response.json();
              document.getElementById('migrationStatus').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('migrationStatus').textContent = 'Error: ' + error.message;
            }
          }

          async function createSampleData() {
            try {
              const response = await fetch('/api/migration/sample-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('sampleDataResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('sampleDataResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getBookmarks() {
            try {
              const response = await fetch('/api/bookmarks');
              const data = await response.json();
              document.getElementById('bookmarksList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('bookmarksList').textContent = 'Error: ' + error.message;
            }
          }

          async function getCategories() {
            try {
              const response = await fetch('/api/categories');
              const data = await response.json();
              document.getElementById('categoriesList').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('categoriesList').textContent = 'Error: ' + error.message;
            }
          }

          async function createBookmark() {
            try {
              const title = document.getElementById('bookmarkTitle').value;
              const url = document.getElementById('bookmarkUrl').value;
              const category = document.getElementById('bookmarkCategory').value;
              const description = document.getElementById('bookmarkDesc').value;

              if (!title || !url || !category) {
                alert('请填写标题、URL和分类ID');
                return;
              }

              const response = await fetch('/api/bookmarks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  title,
                  url,
                  category,
                  description,
                  shortDesc: description
                })
              });
              const data = await response.json();
              document.getElementById('createBookmarkResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('createBookmarkResult').textContent = 'Error: ' + error.message;
            }
          }

          async function importBookmarks() {
            try {
              const fileInput = document.getElementById('bookmarkFile');
              const file = fileInput.files[0];

              if (!file) {
                alert('请选择书签文件');
                return;
              }

              const content = await file.text();
              const createCategories = document.getElementById('createCategories').checked;
              const skipDuplicates = document.getElementById('skipDuplicates').checked;

              // 检测文件类型
              const fileName = file.name.toLowerCase();
              let format = 'auto';
              if (fileName.endsWith('.json')) {
                format = 'json';
              } else if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
                format = 'html';
              }

              const response = await fetch('/api/import/bookmarks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  content,
                  options: {
                    createCategories,
                    skipDuplicates,
                    format
                  }
                })
              });
              const data = await response.json();
              document.getElementById('importResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('importResult').textContent = 'Error: ' + error.message;
            }
          }

          async function exportChromeBookmarks() {
            try {
              const response = await fetch('/api/export/chrome');
              if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'cloudnav-bookmarks.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                document.getElementById('exportResult').textContent = '✅ Chrome 格式书签导出成功';
              } else {
                const error = await response.json();
                document.getElementById('exportResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
              }
            } catch (error) {
              document.getElementById('exportResult').textContent = 'Error: ' + error.message;
            }
          }

          async function exportNetscapeBookmarks() {
            try {
              const response = await fetch('/api/export/netscape');
              if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'cloudnav-bookmarks.html';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                document.getElementById('exportResult').textContent = '✅ HTML 格式书签导出成功';
              } else {
                const error = await response.json();
                document.getElementById('exportResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
              }
            } catch (error) {
              document.getElementById('exportResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getImportStats() {
            try {
              const response = await fetch('/api/import/stats');
              const data = await response.json();
              document.getElementById('exportResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('exportResult').textContent = 'Error: ' + error.message;
            }
          }

          async function analyzeWithAI() {
            try {
              const options = {
                enableCategoryRecommendations: document.getElementById('enableCategoryRecommendations').checked,
                enableDuplicateDetection: document.getElementById('enableDuplicateDetection').checked,
                enableDescriptionGeneration: document.getElementById('enableDescriptionGeneration').checked,
                enableTagSuggestions: document.getElementById('enableTagSuggestions').checked,
                enableNewCategoryCreation: document.getElementById('enableNewCategoryCreation').checked,
                confidenceThreshold: parseFloat(document.getElementById('confidenceThreshold').value)
              };

              const response = await fetch('/api/ai/analyze', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(options)
              });
              const data = await response.json();

              // 格式化显示结果
              if (data.success && data.data) {
                const result = data.data;
                const summary = {
                  message: data.message,
                  statistics: result.statistics,
                  suggestions: {
                    categoryRecommendations: result.suggestions.categoryRecommendations.length,
                    duplicateDetection: result.suggestions.duplicateDetection.length,
                    descriptionSuggestions: result.suggestions.descriptionSuggestions.length,
                    tagSuggestions: result.suggestions.tagSuggestions.length,
                    newCategorySuggestions: result.suggestions.newCategorySuggestions.length
                  },
                  sampleSuggestions: {
                    categoryRecommendations: result.suggestions.categoryRecommendations.slice(0, 3),
                    duplicates: result.suggestions.duplicateDetection.slice(0, 2),
                    descriptions: result.suggestions.descriptionSuggestions.slice(0, 3),
                    tags: result.suggestions.tagSuggestions.slice(0, 3),
                    newCategories: result.suggestions.newCategorySuggestions.slice(0, 2)
                  }
                };
                document.getElementById('aiResult').textContent = JSON.stringify(summary, null, 2);
              } else {
                document.getElementById('aiResult').textContent = JSON.stringify(data, null, 2);
              }
            } catch (error) {
              document.getElementById('aiResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getAIConfig() {
            try {
              const response = await fetch('/api/ai/config');
              const data = await response.json();
              document.getElementById('aiResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('aiResult').textContent = 'Error: ' + error.message;
            }
          }

          async function getDetailedStats() {
            try {
              const response = await fetch('/api/statistics/detailed');
              const data = await response.json();

              // 格式化显示统计数据
              if (data.success && data.data) {
                const stats = data.data;
                const summary = {
                  overview: stats.overview,
                  topBookmarks: stats.bookmarkStats.topBookmarks.slice(0, 5),
                  topCategories: stats.categoryStats.topCategories.slice(0, 5),
                  topSearches: stats.searchStats.topSearches.slice(0, 10),
                  deviceStats: stats.deviceStats,
                  recentTrends: {
                    dailyStats: Object.entries(stats.timeStats.dailyStats).slice(-7),
                    weeklyTrend: stats.timeStats.weeklyTrend.slice(-4),
                    monthlyTrend: stats.timeStats.monthlyTrend.slice(-6)
                  }
                };
                document.getElementById('statsResult').textContent = JSON.stringify(summary, null, 2);
              } else {
                document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);
              }
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function recordPageView() {
            try {
              const response = await fetch('/api/statistics/page-view', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function recordSearch() {
            const query = prompt('请输入搜索关键词:');
            if (!query) return;

            try {
              const response = await fetch('/api/statistics/search', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
              });
              const data = await response.json();
              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function exportStats() {
            try {
              const response = await fetch('/api/statistics/export');
              if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'cloudnav-statistics.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                document.getElementById('statsResult').textContent = '✅ 统计数据导出成功';
              } else {
                const error = await response.json();
                document.getElementById('statsResult').textContent = 'Error: ' + JSON.stringify(error, null, 2);
              }
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function resetStats() {
            if (!confirm('确定要重置所有统计数据吗？此操作不可恢复！')) {
              return;
            }

            try {
              const response = await fetch('/api/statistics/reset', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('statsResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('statsResult').textContent = 'Error: ' + error.message;
            }
          }

          async function clearAllData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {
              return;
            }

            try {
              const response = await fetch('/api/migration/clear', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
              });
              const data = await response.json();
              document.getElementById('clearDataResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
              document.getElementById('clearDataResult').textContent = 'Error: ' + error.message;
            }
          }
        </script>
      </body>
    </html>
  `;

  return c.html(html);
});

export { app as apiRoutes };
