// CloudNav 2.0 - API 路由
import { Hono } from 'hono';
import type { Env, ApiResponse } from '@/types';
import { KVService } from '@/services/kv';
import { BookmarkService } from '@/services/bookmarks';
import { CategoryService } from '@/services/categories';
import { MigrationService } from '@/services/migration';

const app = new Hono<{ Bindings: Env }>();

// 中间件：添加服务实例到上下文
app.use('*', async (c, next) => {
  const kvService = c.get('kvService') as KVService;
  c.set('bookmarkService', new BookmarkService(kvService));
  c.set('categoryService', new CategoryService(kvService));
  c.set('migrationService', new MigrationService(kvService));
  await next();
});

// ==================== 书签管理 API ====================

// 获取所有书签
app.get('/bookmarks', async (c) => {
  try {
    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const bookmarks = await bookmarkService.getAllBookmarks();

    return c.json<ApiResponse>({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error('Get bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get bookmarks'
    }, 500);
  }
});

// 根据ID获取书签
app.get('/bookmarks/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const bookmark = await bookmarkService.getBookmarkById(id);

    if (!bookmark) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: bookmark
    });
  } catch (error) {
    console.error('Get bookmark error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get bookmark'
    }, 500);
  }
});

// 创建新书签
app.post('/bookmarks', async (c) => {
  try {
    const bookmarkData = await c.req.json();
    const bookmarkService = c.get('bookmarkService') as BookmarkService;

    // 验证数据
    const validation = bookmarkService.validateBookmarkData(bookmarkData);
    if (!validation.valid) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Validation failed',
        data: { errors: validation.errors }
      }, 400);
    }

    // 检查重复
    const duplicate = await bookmarkService.checkDuplicateBookmark(bookmarkData.url);
    if (duplicate) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark with this URL already exists'
      }, 409);
    }

    const bookmark = await bookmarkService.createBookmark(bookmarkData);

    return c.json<ApiResponse>({
      success: true,
      data: bookmark,
      message: 'Bookmark created successfully'
    }, 201);
  } catch (error) {
    console.error('Create bookmark error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to create bookmark'
    }, 500);
  }
});

// 更新书签
app.put('/bookmarks/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const updates = await c.req.json();
    const bookmarkService = c.get('bookmarkService') as BookmarkService;

    const bookmark = await bookmarkService.updateBookmark(id, updates);

    if (!bookmark) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: bookmark,
      message: 'Bookmark updated successfully'
    });
  } catch (error) {
    console.error('Update bookmark error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to update bookmark'
    }, 500);
  }
});

// 删除书签
app.delete('/bookmarks/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const bookmarkService = c.get('bookmarkService') as BookmarkService;

    const success = await bookmarkService.deleteBookmark(id);

    if (!success) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      message: 'Bookmark deleted successfully'
    });
  } catch (error) {
    console.error('Delete bookmark error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to delete bookmark'
    }, 500);
  }
});

// 批量删除书签
app.post('/bookmarks/batch-delete', async (c) => {
  try {
    const { ids } = await c.req.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Invalid bookmark IDs'
      }, 400);
    }

    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const result = await bookmarkService.deleteBookmarks(ids);

    return c.json<ApiResponse>({
      success: true,
      data: result,
      message: `Deleted ${result.success.length} bookmarks`
    });
  } catch (error) {
    console.error('Batch delete bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to delete bookmarks'
    }, 500);
  }
});

// ==================== 分类管理 API ====================

// 获取所有分类
app.get('/categories', async (c) => {
  try {
    const categoryService = c.get('categoryService') as CategoryService;
    const categories = await categoryService.getAllCategories();

    return c.json<ApiResponse>({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get categories error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get categories'
    }, 500);
  }
});

// 根据ID获取分类
app.get('/categories/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const categoryService = c.get('categoryService') as CategoryService;
    const category = await categoryService.getCategoryById(id);

    if (!category) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Category not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Get category error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get category'
    }, 500);
  }
});

// 创建新分类
app.post('/categories', async (c) => {
  try {
    const categoryData = await c.req.json();
    const categoryService = c.get('categoryService') as CategoryService;

    // 验证数据
    const validation = categoryService.validateCategoryData(categoryData);
    if (!validation.valid) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Validation failed',
        data: { errors: validation.errors }
      }, 400);
    }

    // 检查重复
    const duplicate = await categoryService.checkDuplicateCategoryName(categoryData.name);
    if (duplicate) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Category with this name already exists'
      }, 409);
    }

    const category = await categoryService.createCategory(categoryData);

    return c.json<ApiResponse>({
      success: true,
      data: category,
      message: 'Category created successfully'
    }, 201);
  } catch (error) {
    console.error('Create category error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to create category'
    }, 500);
  }
});

// 更新分类
app.put('/categories/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const updates = await c.req.json();
    const categoryService = c.get('categoryService') as CategoryService;

    const category = await categoryService.updateCategory(id, updates);

    if (!category) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Category not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: category,
      message: 'Category updated successfully'
    });
  } catch (error) {
    console.error('Update category error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to update category'
    }, 500);
  }
});

// 删除分类
app.delete('/categories/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const { moveBookmarksTo, deleteBookmarks } = c.req.query();
    const categoryService = c.get('categoryService') as CategoryService;

    const options: any = {};
    if (moveBookmarksTo) options.moveBookmarksTo = moveBookmarksTo;
    if (deleteBookmarks === 'true') options.deleteBookmarks = true;

    const result = await categoryService.deleteCategory(id, options);

    if (!result.success) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Category not found'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      data: result,
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Delete category error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete category'
    }, 500);
  }
});

// 获取分类统计
app.get('/categories/stats', async (c) => {
  try {
    const categoryService = c.get('categoryService') as CategoryService;
    const stats = await categoryService.getCategoryStats();

    return c.json<ApiResponse>({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get category stats error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get category stats'
    }, 500);
  }
});

// 获取统计数据
app.get('/stats', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const stats = await kvService.getStats();
    
    return c.json<ApiResponse>({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get stats error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get stats'
    }, 500);
  }
});

// ==================== 统计和点击 API ====================

// 记录点击统计
app.post('/stats/click', async (c) => {
  try {
    const { bookmarkId } = await c.req.json();

    if (!bookmarkId) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark ID is required'
      }, 400);
    }

    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const success = await bookmarkService.incrementClickCount(bookmarkId);

    if (success) {
      return c.json<ApiResponse>({
        success: true,
        message: 'Click recorded'
      });
    } else {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark not found'
      }, 404);
    }
  } catch (error) {
    console.error('Record click error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to record click'
    }, 500);
  }
});

// 获取热门书签
app.get('/bookmarks/popular', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '10');
    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const bookmarks = await bookmarkService.getPopularBookmarks(limit);

    return c.json<ApiResponse>({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error('Get popular bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get popular bookmarks'
    }, 500);
  }
});

// 获取最近添加的书签
app.get('/bookmarks/recent', async (c) => {
  try {
    const limit = parseInt(c.req.query('limit') || '10');
    const bookmarkService = c.get('bookmarkService') as BookmarkService;
    const bookmarks = await bookmarkService.getRecentBookmarks(limit);

    return c.json<ApiResponse>({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error('Get recent bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get recent bookmarks'
    }, 500);
  }
});

// ==================== 数据迁移 API ====================

// 获取迁移状态
app.get('/migration/status', async (c) => {
  try {
    const migrationService = c.get('migrationService') as MigrationService;
    const status = await migrationService.getMigrationStatus();

    return c.json<ApiResponse>({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('Get migration status error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get migration status'
    }, 500);
  }
});

// 执行数据迁移
app.post('/migration/migrate', async (c) => {
  try {
    const migrationService = c.get('migrationService') as MigrationService;
    const result = await migrationService.performMigration();

    return c.json<ApiResponse>({
      success: result.success,
      data: result.details,
      message: result.message
    });
  } catch (error) {
    console.error('Migration error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Migration failed'
    }, 500);
  }
});

// 创建示例数据
app.post('/migration/sample-data', async (c) => {
  try {
    const migrationService = c.get('migrationService') as MigrationService;
    const result = await migrationService.createSampleData();

    return c.json<ApiResponse>({
      success: result.success,
      data: {
        categoriesCreated: result.categoriesCreated,
        bookmarksCreated: result.bookmarksCreated,
        errors: result.errors
      },
      message: `Created ${result.categoriesCreated} categories and ${result.bookmarksCreated} bookmarks`
    });
  } catch (error) {
    console.error('Create sample data error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to create sample data'
    }, 500);
  }
});

// 清空所有数据
app.post('/migration/clear', async (c) => {
  try {
    const migrationService = c.get('migrationService') as MigrationService;
    const success = await migrationService.clearAllData();

    return c.json<ApiResponse>({
      success,
      message: success ? 'All data cleared successfully' : 'Failed to clear data'
    });
  } catch (error) {
    console.error('Clear data error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to clear data'
    }, 500);
  }
});

// 获取配置
app.get('/config', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const config = await kvService.getConfig();
    
    // 不返回敏感信息
    const publicConfig = {
      siteName: config.siteName,
      siteDescription: config.siteDescription,
      features: config.features,
      theme: config.theme
    };
    
    return c.json<ApiResponse>({
      success: true,
      data: publicConfig
    });
  } catch (error) {
    console.error('Get config error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get config'
    }, 500);
  }
});

// 搜索书签
app.get('/search', async (c) => {
  try {
    const query = c.req.query('q') || '';
    
    if (!query.trim()) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Search query is required'
      }, 400);
    }

    const kvService = c.get('kvService') as KVService;
    const bookmarks = await kvService.getBookmarks();
    
    // 搜索逻辑
    const filteredBookmarks = bookmarks.filter(bookmark => 
      bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.description?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    // 记录搜索统计
    const stats = await kvService.getStats();
    stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
    await kvService.updateStats(stats);
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        query,
        results: filteredBookmarks,
        total: filteredBookmarks.length
      }
    });
  } catch (error) {
    console.error('Search error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Search failed'
    }, 500);
  }
});

// 数据备份
app.get('/backup', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const backup = await kvService.backup();
    
    return c.json<ApiResponse>({
      success: true,
      data: backup
    });
  } catch (error) {
    console.error('Backup error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Backup failed'
    }, 500);
  }
});

// 健康检查
app.get('/health', (c) => {
  return c.json<ApiResponse>({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '2.0.0'
    }
  });
});

export { app as apiRoutes };
