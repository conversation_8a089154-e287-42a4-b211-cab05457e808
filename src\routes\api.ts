// CloudNav 2.0 - API 路由
import { Hono } from 'hono';
import type { Env, ApiResponse } from '@/types';
import { KVService } from '@/services/kv';

const app = new Hono<{ Bindings: Env }>();

// 获取所有书签
app.get('/bookmarks', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const bookmarks = await kvService.getBookmarks();
    
    return c.json<ApiResponse>({
      success: true,
      data: bookmarks
    });
  } catch (error) {
    console.error('Get bookmarks error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get bookmarks'
    }, 500);
  }
});

// 获取所有分类
app.get('/categories', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const categories = await kvService.getCategories();
    
    return c.json<ApiResponse>({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Get categories error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get categories'
    }, 500);
  }
});

// 获取统计数据
app.get('/stats', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const stats = await kvService.getStats();
    
    return c.json<ApiResponse>({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get stats error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get stats'
    }, 500);
  }
});

// 记录点击统计
app.post('/stats/click', async (c) => {
  try {
    const { bookmarkId } = await c.req.json();
    
    if (!bookmarkId) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Bookmark ID is required'
      }, 400);
    }

    const kvService = c.get('kvService') as KVService;
    const success = await kvService.incrementBookmarkClick(bookmarkId);
    
    if (success) {
      return c.json<ApiResponse>({
        success: true,
        message: 'Click recorded'
      });
    } else {
      return c.json<ApiResponse>({
        success: false,
        error: 'Failed to record click'
      }, 500);
    }
  } catch (error) {
    console.error('Record click error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to record click'
    }, 500);
  }
});

// 获取配置
app.get('/config', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const config = await kvService.getConfig();
    
    // 不返回敏感信息
    const publicConfig = {
      siteName: config.siteName,
      siteDescription: config.siteDescription,
      features: config.features,
      theme: config.theme
    };
    
    return c.json<ApiResponse>({
      success: true,
      data: publicConfig
    });
  } catch (error) {
    console.error('Get config error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Failed to get config'
    }, 500);
  }
});

// 搜索书签
app.get('/search', async (c) => {
  try {
    const query = c.req.query('q') || '';
    
    if (!query.trim()) {
      return c.json<ApiResponse>({
        success: false,
        error: 'Search query is required'
      }, 400);
    }

    const kvService = c.get('kvService') as KVService;
    const bookmarks = await kvService.getBookmarks();
    
    // 搜索逻辑
    const filteredBookmarks = bookmarks.filter(bookmark => 
      bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.description?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.shortDesc?.toLowerCase().includes(query.toLowerCase()) ||
      bookmark.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    // 记录搜索统计
    const stats = await kvService.getStats();
    stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
    await kvService.updateStats(stats);
    
    return c.json<ApiResponse>({
      success: true,
      data: {
        query,
        results: filteredBookmarks,
        total: filteredBookmarks.length
      }
    });
  } catch (error) {
    console.error('Search error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Search failed'
    }, 500);
  }
});

// 数据备份
app.get('/backup', async (c) => {
  try {
    const kvService = c.get('kvService') as KVService;
    const backup = await kvService.backup();
    
    return c.json<ApiResponse>({
      success: true,
      data: backup
    });
  } catch (error) {
    console.error('Backup error:', error);
    return c.json<ApiResponse>({
      success: false,
      error: 'Backup failed'
    }, 500);
  }
});

// 健康检查
app.get('/health', (c) => {
  return c.json<ApiResponse>({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '2.0.0'
    }
  });
});

export { app as apiRoutes };
