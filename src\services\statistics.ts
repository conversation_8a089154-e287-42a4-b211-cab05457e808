// CloudNav 2.0 - 统计功能服务
import type { Stats, Bookmark, Category } from '@/types';
import { KVService } from './kv';
import { BookmarkService } from './bookmarks';
import { CategoryService } from './categories';

export interface DetailedStats {
  overview: {
    totalBookmarks: number;
    totalCategories: number;
    totalClicks: number;
    totalViews: number;
    averageClicksPerBookmark: number;
  };
  bookmarkStats: {
    topBookmarks: Array<{
      bookmark: Bookmark;
      clicks: number;
      clickRate: number;
    }>;
    recentBookmarks: Bookmark[];
    bookmarksByCategory: Record<string, number>;
  };
  categoryStats: {
    topCategories: Array<{
      category: Category;
      bookmarkCount: number;
      totalClicks: number;
      averageClicks: number;
    }>;
    categoryDistribution: Array<{
      categoryName: string;
      percentage: number;
      count: number;
    }>;
  };
  searchStats: {
    topSearches: Array<{
      query: string;
      count: number;
      percentage: number;
    }>;
    totalSearches: number;
    uniqueSearches: number;
  };
  timeStats: {
    dailyStats: Record<string, number>;
    weeklyTrend: Array<{
      week: string;
      clicks: number;
      views: number;
    }>;
    monthlyTrend: Array<{
      month: string;
      clicks: number;
      views: number;
    }>;
  };
  deviceStats: {
    mobile: number;
    desktop: number;
    tablet: number;
    mobilePercentage: number;
    desktopPercentage: number;
    tabletPercentage: number;
  };
  performanceStats: {
    averageLoadTime?: number;
    errorRate?: number;
    uptime?: number;
  };
}

export class StatisticsService {
  private kvService: KVService;
  private bookmarkService: BookmarkService;
  private categoryService: CategoryService;

  constructor(kvService: KVService) {
    this.kvService = kvService;
    this.bookmarkService = new BookmarkService(kvService);
    this.categoryService = new CategoryService(kvService);
  }

  // 获取详细统计数据
  async getDetailedStats(): Promise<DetailedStats> {
    const [stats, bookmarks, categories] = await Promise.all([
      this.kvService.getStats(),
      this.bookmarkService.getAllBookmarks(),
      this.categoryService.getAllCategories()
    ]);

    return {
      overview: this.calculateOverviewStats(stats, bookmarks, categories),
      bookmarkStats: this.calculateBookmarkStats(stats, bookmarks, categories),
      categoryStats: this.calculateCategoryStats(stats, bookmarks, categories),
      searchStats: this.calculateSearchStats(stats),
      timeStats: this.calculateTimeStats(stats),
      deviceStats: this.calculateDeviceStats(stats),
      performanceStats: this.calculatePerformanceStats()
    };
  }

  // 计算概览统计
  private calculateOverviewStats(stats: Stats, bookmarks: Bookmark[], categories: Category[]) {
    const totalClicks = bookmarks.reduce((sum, bookmark) => sum + bookmark.clickCount, 0);
    
    return {
      totalBookmarks: bookmarks.length,
      totalCategories: categories.length,
      totalClicks: totalClicks,
      totalViews: stats.totalViews,
      averageClicksPerBookmark: bookmarks.length > 0 ? totalClicks / bookmarks.length : 0
    };
  }

  // 计算书签统计
  private calculateBookmarkStats(stats: Stats, bookmarks: Bookmark[], categories: Category[]) {
    // 热门书签
    const topBookmarks = bookmarks
      .sort((a, b) => b.clickCount - a.clickCount)
      .slice(0, 10)
      .map(bookmark => ({
        bookmark,
        clicks: bookmark.clickCount,
        clickRate: stats.totalClicks > 0 ? (bookmark.clickCount / stats.totalClicks) * 100 : 0
      }));

    // 最近添加的书签
    const recentBookmarks = bookmarks
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, 10);

    // 按分类统计书签数量
    const bookmarksByCategory: Record<string, number> = {};
    bookmarks.forEach(bookmark => {
      bookmarksByCategory[bookmark.category] = (bookmarksByCategory[bookmark.category] || 0) + 1;
    });

    return {
      topBookmarks,
      recentBookmarks,
      bookmarksByCategory
    };
  }

  // 计算分类统计
  private calculateCategoryStats(stats: Stats, bookmarks: Bookmark[], categories: Category[]) {
    // 分类点击统计
    const categoryClickStats: Record<string, number> = {};
    bookmarks.forEach(bookmark => {
      categoryClickStats[bookmark.category] = (categoryClickStats[bookmark.category] || 0) + bookmark.clickCount;
    });

    // 热门分类
    const topCategories = categories
      .map(category => {
        const bookmarkCount = bookmarks.filter(b => b.category === category.id).length;
        const totalClicks = categoryClickStats[category.id] || 0;
        return {
          category,
          bookmarkCount,
          totalClicks,
          averageClicks: bookmarkCount > 0 ? totalClicks / bookmarkCount : 0
        };
      })
      .sort((a, b) => b.totalClicks - a.totalClicks)
      .slice(0, 10);

    // 分类分布
    const totalBookmarks = bookmarks.length;
    const categoryDistribution = categories
      .map(category => {
        const count = bookmarks.filter(b => b.category === category.id).length;
        return {
          categoryName: category.name,
          count,
          percentage: totalBookmarks > 0 ? (count / totalBookmarks) * 100 : 0
        };
      })
      .sort((a, b) => b.count - a.count);

    return {
      topCategories,
      categoryDistribution
    };
  }

  // 计算搜索统计
  private calculateSearchStats(stats: Stats) {
    const searchEntries = Object.entries(stats.searchStats);
    const totalSearches = searchEntries.reduce((sum, [, count]) => sum + count, 0);
    
    const topSearches = searchEntries
      .sort(([, a], [, b]) => b - a)
      .slice(0, 20)
      .map(([query, count]) => ({
        query,
        count,
        percentage: totalSearches > 0 ? (count / totalSearches) * 100 : 0
      }));

    return {
      topSearches,
      totalSearches,
      uniqueSearches: searchEntries.length
    };
  }

  // 计算时间统计
  private calculateTimeStats(stats: Stats) {
    const dailyStats = stats.dailyStats;
    
    // 计算周统计
    const weeklyTrend = this.aggregateByWeek(dailyStats);
    
    // 计算月统计
    const monthlyTrend = this.aggregateByMonth(dailyStats);

    return {
      dailyStats,
      weeklyTrend,
      monthlyTrend
    };
  }

  // 计算设备统计
  private calculateDeviceStats(stats: Stats) {
    const { mobile, desktop, tablet } = stats.deviceStats;
    const total = mobile + desktop + tablet;

    return {
      mobile,
      desktop,
      tablet,
      mobilePercentage: total > 0 ? (mobile / total) * 100 : 0,
      desktopPercentage: total > 0 ? (desktop / total) * 100 : 0,
      tabletPercentage: total > 0 ? (tablet / total) * 100 : 0
    };
  }

  // 计算性能统计
  private calculatePerformanceStats() {
    // 这里可以添加性能监控数据
    return {
      averageLoadTime: undefined,
      errorRate: undefined,
      uptime: undefined
    };
  }

  // 按周聚合数据
  private aggregateByWeek(dailyStats: Record<string, number>) {
    const weeklyData: Record<string, { clicks: number; views: number }> = {};
    
    Object.entries(dailyStats).forEach(([date, clicks]) => {
      const weekStart = this.getWeekStart(new Date(date));
      const weekKey = weekStart.toISOString().split('T')[0];
      
      if (!weeklyData[weekKey]) {
        weeklyData[weekKey] = { clicks: 0, views: 0 };
      }
      weeklyData[weekKey].clicks += clicks;
    });

    return Object.entries(weeklyData)
      .map(([week, data]) => ({ week, ...data }))
      .sort((a, b) => a.week.localeCompare(b.week));
  }

  // 按月聚合数据
  private aggregateByMonth(dailyStats: Record<string, number>) {
    const monthlyData: Record<string, { clicks: number; views: number }> = {};
    
    Object.entries(dailyStats).forEach(([date, clicks]) => {
      const monthKey = date.substring(0, 7); // YYYY-MM
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { clicks: 0, views: 0 };
      }
      monthlyData[monthKey].clicks += clicks;
    });

    return Object.entries(monthlyData)
      .map(([month, data]) => ({ month, ...data }))
      .sort((a, b) => a.month.localeCompare(b.month));
  }

  // 获取周的开始日期
  private getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 周一为一周开始
    return new Date(d.setDate(diff));
  }

  // 记录页面访问
  async recordPageView(userAgent?: string): Promise<void> {
    try {
      const stats = await this.kvService.getStats();
      stats.totalViews++;

      // 检测设备类型
      if (userAgent) {
        const deviceType = this.detectDeviceType(userAgent);
        stats.deviceStats[deviceType]++;
      }

      // 记录日统计
      const today = new Date().toISOString().split('T')[0];
      stats.dailyStats[today] = (stats.dailyStats[today] || 0) + 1;

      await this.kvService.updateStats(stats);
    } catch (error) {
      console.error('Failed to record page view:', error);
    }
  }

  // 检测设备类型
  private detectDeviceType(userAgent: string): 'mobile' | 'desktop' | 'tablet' {
    const ua = userAgent.toLowerCase();
    
    if (/tablet|ipad|playbook|silk/.test(ua)) {
      return 'tablet';
    }
    
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/.test(ua)) {
      return 'mobile';
    }
    
    return 'desktop';
  }

  // 记录搜索
  async recordSearch(query: string): Promise<void> {
    try {
      if (!query.trim()) return;

      const stats = await this.kvService.getStats();
      stats.searchStats[query] = (stats.searchStats[query] || 0) + 1;
      await this.kvService.updateStats(stats);
    } catch (error) {
      console.error('Failed to record search:', error);
    }
  }

  // 清理过期统计数据
  async cleanupOldStats(daysToKeep: number = 90): Promise<void> {
    try {
      const stats = await this.kvService.getStats();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const cutoffString = cutoffDate.toISOString().split('T')[0];

      // 清理日统计数据
      Object.keys(stats.dailyStats).forEach(date => {
        if (date < cutoffString) {
          delete stats.dailyStats[date];
        }
      });

      await this.kvService.updateStats(stats);
      console.log(`Cleaned up stats older than ${daysToKeep} days`);
    } catch (error) {
      console.error('Failed to cleanup old stats:', error);
    }
  }

  // 导出统计数据
  async exportStats(): Promise<DetailedStats> {
    return await this.getDetailedStats();
  }

  // 重置统计数据
  async resetStats(): Promise<void> {
    try {
      await this.kvService.updateStats({
        totalClicks: 0,
        totalViews: 0,
        bookmarkStats: {},
        categoryStats: {},
        searchStats: {},
        dailyStats: {},
        deviceStats: { mobile: 0, desktop: 0, tablet: 0 },
        lastUpdated: Date.now()
      });
      console.log('Statistics reset successfully');
    } catch (error) {
      console.error('Failed to reset stats:', error);
      throw error;
    }
  }
}
