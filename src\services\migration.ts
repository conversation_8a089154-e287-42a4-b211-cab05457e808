// CloudNav 2.0 - 数据迁移服务
import type { Bookmark, Category } from '@/types';
import { KVService } from './kv';
import { BookmarkService } from './bookmarks';
import { CategoryService } from './categories';

// 原始数据格式（来自 navLinks.js）
interface LegacySite {
  id: string;
  title: string;
  description?: string;
  shortDesc?: string;
  url: string;
  category: string;
  icon?: string;
}

interface LegacyCategory {
  id: string;
  name: string;
  icon?: string;
}

interface LegacyData {
  categories: LegacyCategory[];
  sites: LegacySite[];
}

export class MigrationService {
  private kvService: KVService;
  private bookmarkService: BookmarkService;
  private categoryService: CategoryService;

  constructor(kvService: KVService) {
    this.kvService = kvService;
    this.bookmarkService = new BookmarkService(kvService);
    this.categoryService = new CategoryService(kvService);
  }

  // 从 navLinks.js 迁移数据
  async migrateFromNavLinks(legacyData: LegacyData): Promise<{
    success: boolean;
    categoriesImported: number;
    bookmarksImported: number;
    errors: string[];
  }> {
    const result = {
      success: false,
      categoriesImported: 0,
      bookmarksImported: 0,
      errors: []
    };

    try {
      console.log('开始数据迁移...');

      // 1. 迁移分类
      console.log('迁移分类数据...');
      for (let i = 0; i < legacyData.categories.length; i++) {
        const legacyCategory = legacyData.categories[i];
        try {
          const category: Omit<Category, 'id' | 'createdAt'> = {
            name: legacyCategory.name,
            icon: legacyCategory.icon,
            description: `从原系统迁移的分类: ${legacyCategory.name}`,
            order: i + 1
          };

          // 检查是否已存在
          const existing = await this.categoryService.checkDuplicateCategoryName(category.name);
          if (!existing) {
            await this.categoryService.createCategory(category);
            result.categoriesImported++;
            console.log(`✓ 分类迁移成功: ${category.name}`);
          } else {
            console.log(`- 分类已存在，跳过: ${category.name}`);
          }
        } catch (error) {
          const errorMsg = `分类迁移失败 ${legacyCategory.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      // 2. 迁移书签
      console.log('迁移书签数据...');
      for (const legacySite of legacyData.sites) {
        try {
          const bookmark: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt' | 'clickCount'> = {
            title: legacySite.title,
            url: legacySite.url,
            description: legacySite.description,
            shortDesc: legacySite.shortDesc,
            category: legacySite.category,
            icon: legacySite.icon,
            tags: []
          };

          // 验证数据
          const validation = this.bookmarkService.validateBookmarkData(bookmark);
          if (!validation.valid) {
            result.errors.push(`书签验证失败 ${bookmark.title}: ${validation.errors.join(', ')}`);
            continue;
          }

          // 检查重复
          const duplicate = await this.bookmarkService.checkDuplicateBookmark(bookmark.url);
          if (!duplicate) {
            await this.bookmarkService.createBookmark(bookmark);
            result.bookmarksImported++;
            console.log(`✓ 书签迁移成功: ${bookmark.title}`);
          } else {
            console.log(`- 书签已存在，跳过: ${bookmark.title}`);
          }
        } catch (error) {
          const errorMsg = `书签迁移失败 ${legacySite.title}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      result.success = true;
      console.log(`迁移完成: ${result.categoriesImported} 个分类, ${result.bookmarksImported} 个书签`);

    } catch (error) {
      result.errors.push(`迁移过程出错: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('迁移失败:', error);
    }

    return result;
  }

  // 从当前项目的 navLinks.js 读取数据
  async loadNavLinksData(): Promise<LegacyData | null> {
    try {
      // 这里需要动态导入原始的 navLinks.js 文件
      // 由于在 Workers 环境中，我们需要手动解析数据
      
      // 示例数据结构，实际使用时需要从文件中读取
      const sampleData: LegacyData = {
        categories: [
          { id: 'opensource', name: '开源项目', icon: '🔓' },
          { id: 'ai', name: 'AI工具', icon: '🤖' },
          { id: 'design', name: '设计工具', icon: '🎨' },
          { id: 'dev', name: '开发工具', icon: '⚡' },
          { id: 'learn', name: '学习资源', icon: '📚' },
          { id: 'life', name: '生活服务', icon: '🏠' }
        ],
        sites: [
          {
            id: 'github',
            title: 'GitHub',
            description: '全球最大的代码托管平台',
            shortDesc: '代码托管',
            url: 'https://github.com',
            category: 'opensource',
            icon: '/icons/github.webp'
          },
          {
            id: 'chatgpt',
            title: 'ChatGPT',
            description: 'OpenAI开发的AI聊天机器人',
            shortDesc: 'AI聊天',
            url: 'https://chat.openai.com',
            category: 'ai',
            icon: '/icons/chatgpt.webp'
          }
        ]
      };

      return sampleData;
    } catch (error) {
      console.error('读取 navLinks 数据失败:', error);
      return null;
    }
  }

  // 执行完整迁移流程
  async performMigration(): Promise<{
    success: boolean;
    message: string;
    details: {
      categoriesImported: number;
      bookmarksImported: number;
      errors: string[];
    };
  }> {
    try {
      // 1. 检查是否已有数据
      const [existingBookmarks, existingCategories] = await Promise.all([
        this.bookmarkService.getAllBookmarks(),
        this.categoryService.getAllCategories()
      ]);

      if (existingBookmarks.length > 0 || existingCategories.length > 0) {
        return {
          success: false,
          message: '系统中已存在数据，请先清空数据或选择强制迁移',
          details: { categoriesImported: 0, bookmarksImported: 0, errors: [] }
        };
      }

      // 2. 读取原始数据
      const legacyData = await this.loadNavLinksData();
      if (!legacyData) {
        return {
          success: false,
          message: '无法读取原始数据文件',
          details: { categoriesImported: 0, bookmarksImported: 0, errors: ['数据文件读取失败'] }
        };
      }

      // 3. 执行迁移
      const migrationResult = await this.migrateFromNavLinks(legacyData);

      return {
        success: migrationResult.success,
        message: migrationResult.success 
          ? `迁移成功完成！导入了 ${migrationResult.categoriesImported} 个分类和 ${migrationResult.bookmarksImported} 个书签`
          : '迁移过程中出现错误',
        details: {
          categoriesImported: migrationResult.categoriesImported,
          bookmarksImported: migrationResult.bookmarksImported,
          errors: migrationResult.errors
        }
      };

    } catch (error) {
      return {
        success: false,
        message: '迁移过程中发生未知错误',
        details: {
          categoriesImported: 0,
          bookmarksImported: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error']
        }
      };
    }
  }

  // 清空所有数据
  async clearAllData(): Promise<boolean> {
    try {
      const [bookmarks, categories] = await Promise.all([
        this.bookmarkService.getAllBookmarks(),
        this.categoryService.getAllCategories()
      ]);

      // 删除所有书签
      for (const bookmark of bookmarks) {
        await this.bookmarkService.deleteBookmark(bookmark.id);
      }

      // 删除所有分类
      for (const category of categories) {
        await this.categoryService.deleteCategory(category.id, { deleteBookmarks: true });
      }

      // 清理统计数据
      await this.kvService.updateStats({
        totalClicks: 0,
        totalViews: 0,
        bookmarkStats: {},
        categoryStats: {},
        searchStats: {},
        dailyStats: {},
        deviceStats: { mobile: 0, desktop: 0, tablet: 0 },
        lastUpdated: Date.now()
      });

      return true;
    } catch (error) {
      console.error('清空数据失败:', error);
      return false;
    }
  }

  // 创建示例数据
  async createSampleData(): Promise<{
    success: boolean;
    categoriesCreated: number;
    bookmarksCreated: number;
    errors: string[];
  }> {
    const result = {
      success: false,
      categoriesCreated: 0,
      bookmarksCreated: 0,
      errors: []
    };

    try {
      // 创建默认分类
      const defaultCategories = await this.categoryService.createDefaultCategories();
      result.categoriesCreated = defaultCategories.length;

      // 创建示例书签
      const sampleBookmarks = [
        {
          title: 'GitHub',
          url: 'https://github.com',
          description: '全球最大的代码托管平台，开发者的必备工具',
          shortDesc: '代码托管平台',
          category: defaultCategories.find(c => c.name === '开发工具')?.id || 'dev',
          icon: 'https://github.com/favicon.ico',
          tags: ['代码', '开源', 'Git']
        },
        {
          title: 'Figma',
          url: 'https://figma.com',
          description: '现代化的界面设计工具，支持团队协作',
          shortDesc: '界面设计工具',
          category: defaultCategories.find(c => c.name === '设计资源')?.id || 'design',
          icon: 'https://figma.com/favicon.ico',
          tags: ['设计', 'UI', '协作']
        },
        {
          title: 'MDN Web Docs',
          url: 'https://developer.mozilla.org',
          description: 'Web开发者的权威参考文档',
          shortDesc: 'Web开发文档',
          category: defaultCategories.find(c => c.name === '学习资料')?.id || 'learn',
          icon: 'https://developer.mozilla.org/favicon.ico',
          tags: ['文档', 'Web', '学习']
        }
      ];

      for (const bookmarkData of sampleBookmarks) {
        try {
          await this.bookmarkService.createBookmark(bookmarkData);
          result.bookmarksCreated++;
        } catch (error) {
          result.errors.push(`创建示例书签失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.success = true;
    } catch (error) {
      result.errors.push(`创建示例数据失败: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  // 获取迁移状态
  async getMigrationStatus(): Promise<{
    hasData: boolean;
    bookmarkCount: number;
    categoryCount: number;
    lastMigration?: number;
  }> {
    const [bookmarks, categories, config] = await Promise.all([
      this.bookmarkService.getAllBookmarks(),
      this.categoryService.getAllCategories(),
      this.kvService.getConfig()
    ]);

    return {
      hasData: bookmarks.length > 0 || categories.length > 0,
      bookmarkCount: bookmarks.length,
      categoryCount: categories.length,
      lastMigration: undefined // 可以从配置中读取上次迁移时间
    };
  }
}
